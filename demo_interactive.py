#!/usr/bin/env python3
"""
无人机交通警察系统 - 交互式演示
提供命令行交互式演示界面
"""

import sys
import asyncio
import time
import random
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core.system import SystemManager

class InteractiveDemo:
    """交互式演示类"""
    
    def __init__(self):
        self.system_manager = None
        self.running = False
        self.demo_active = False
        
    def print_banner(self):
        """打印横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                🚁 无人机交通警察系统                          ║
║                   交互式演示界面                             ║
║                                                              ║
║  基于AI的智能交通监控系统                                     ║
║  - 实时目标检测与跟踪                                         ║
║  - 交通流量分析                                              ║
║  - 事故自动识别                                              ║
║  - 违法行为检测                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def print_menu(self):
        """打印菜单"""
        print("\n" + "="*60)
        print("🎮 演示菜单")
        print("="*60)
        print("1. 🚀 启动系统")
        print("2. ⏹️  停止系统") 
        print("3. 🎭 开始模拟演示")
        print("4. ⏸️  停止演示")
        print("5. 📊 查看系统状态")
        print("6. 📈 查看检测统计")
        print("7. 🔧 系统测试")
        print("8. ❓ 帮助信息")
        print("9. 👋 退出程序")
        print("="*60)
    
    def print_status(self):
        """打印系统状态"""
        print("\n📊 系统状态")
        print("-" * 40)
        print(f"系统管理器: {'✅ 已初始化' if self.system_manager else '❌ 未初始化'}")
        print(f"系统运行: {'✅ 运行中' if self.running else '❌ 已停止'}")
        print(f"演示状态: {'🎭 进行中' if self.demo_active else '⏸️ 未运行'}")
        
        if self.system_manager:
            try:
                # 这里可以添加更多系统信息
                print(f"AI引擎: ✅ 已加载")
                print(f"数据管理: ✅ 正常")
                print(f"硬件连接: ✅ 就绪")
            except:
                print(f"组件状态: ⚠️ 部分异常")
    
    async def start_system(self):
        """启动系统"""
        if self.system_manager:
            print("⚠️ 系统已经启动")
            return
        
        print("🚀 正在启动系统...")
        print("   ├─ 初始化系统管理器...")
        
        try:
            self.system_manager = SystemManager()
            print("   ├─ 加载AI引擎...")
            
            success = await self.system_manager.initialize()
            
            if success:
                self.running = True
                print("   ├─ 连接硬件设备...")
                print("   ├─ 加载检测模型...")
                print("   └─ ✅ 系统启动成功!")
                
                # 显示系统信息
                await self.show_system_info()
            else:
                print("   └─ ❌ 系统启动失败")
                self.system_manager = None
                
        except Exception as e:
            print(f"   └─ ❌ 启动错误: {e}")
            self.system_manager = None
    
    async def stop_system(self):
        """停止系统"""
        if not self.system_manager:
            print("⚠️ 系统未启动")
            return
        
        print("⏹️ 正在停止系统...")
        
        try:
            self.demo_active = False
            await self.system_manager.shutdown()
            self.system_manager = None
            self.running = False
            print("✅ 系统已停止")
            
        except Exception as e:
            print(f"❌ 停止错误: {e}")
    
    async def show_system_info(self):
        """显示系统信息"""
        if not self.system_manager:
            print("❌ 系统未启动")
            return
        
        try:
            info = await self.system_manager.get_system_info()
            status = await self.system_manager.get_system_status()
            
            print("\n📋 系统信息")
            print("-" * 40)
            print(f"系统名称: {info['name']}")
            print(f"版本号: {info['version']}")
            print(f"启动时间: {info['start_time']}")
            print(f"运行状态: {'✅ 正常' if status['running'] else '❌ 异常'}")
            print(f"初始化: {'✅ 完成' if status['initialized'] else '❌ 未完成'}")
            
        except Exception as e:
            print(f"❌ 获取系统信息失败: {e}")
    
    async def start_demo(self):
        """开始演示"""
        if not self.system_manager:
            print("❌ 请先启动系统")
            return
        
        if self.demo_active:
            print("⚠️ 演示已在进行中")
            return
        
        print("🎭 开始模拟演示...")
        self.demo_active = True
        
        # 启动演示循环
        asyncio.create_task(self.demo_loop())
    
    def stop_demo(self):
        """停止演示"""
        if not self.demo_active:
            print("⚠️ 演示未在运行")
            return
        
        self.demo_active = False
        print("⏸️ 演示已停止")
    
    async def demo_loop(self):
        """演示循环"""
        frame_count = 0
        total_detections = 0
        
        print("\n🎬 模拟检测开始...")
        print("按 Ctrl+C 或选择菜单选项4停止演示")
        print("-" * 50)
        
        while self.demo_active:
            frame_count += 1
            
            # 模拟检测结果
            detections = self.simulate_detection()
            total_detections += len(detections)
            
            # 显示检测结果
            timestamp = time.strftime("%H:%M:%S")
            print(f"[{timestamp}] 帧 {frame_count:04d}: ", end="")
            
            if detections:
                detection_str = ", ".join([f"{d['class']}({d['confidence']:.2f})" for d in detections])
                print(f"检测到 {len(detections)} 个目标 - {detection_str}")
                
                # 模拟交通分析
                if frame_count % 30 == 0:  # 每30帧分析一次
                    await self.analyze_traffic(detections)
                    
            else:
                print("无目标检测")
            
            # 每100帧显示统计
            if frame_count % 100 == 0:
                fps = 30  # 模拟30FPS
                avg_detections = total_detections / frame_count
                print(f"\n📊 统计信息 (帧 {frame_count}):")
                print(f"   平均FPS: {fps}")
                print(f"   总检测数: {total_detections}")
                print(f"   平均每帧: {avg_detections:.2f}")
                print("-" * 50)
            
            await asyncio.sleep(0.1)  # 模拟处理时间
        
        print(f"\n🎬 演示结束 - 总共处理 {frame_count} 帧")
    
    def simulate_detection(self):
        """模拟检测结果"""
        # 随机生成检测结果
        detections = []
        
        # 随机决定是否有检测结果
        if random.random() > 0.3:  # 70%概率有检测
            num_objects = random.randint(1, 5)
            
            for _ in range(num_objects):
                obj_type = random.choice(['car', 'person', 'bicycle', 'motorcycle'])
                confidence = random.uniform(0.5, 0.95)
                
                detections.append({
                    'class': obj_type,
                    'confidence': confidence,
                    'bbox': [
                        random.randint(0, 640),
                        random.randint(0, 480),
                        random.randint(50, 200),
                        random.randint(50, 200)
                    ]
                })
        
        return detections
    
    async def analyze_traffic(self, detections):
        """分析交通情况"""
        cars = [d for d in detections if d['class'] == 'car']
        persons = [d for d in detections if d['class'] == 'person']
        
        if len(cars) > 3:
            print("   🚨 检测到交通拥堵!")
        elif len(persons) > 2:
            print("   👥 检测到人群聚集")
        elif any(d['class'] == 'bicycle' for d in detections):
            print("   🚴 检测到非机动车")
    
    async def show_statistics(self):
        """显示统计信息"""
        if not self.system_manager:
            print("❌ 系统未启动")
            return
        
        try:
            stats = await self.system_manager.get_comprehensive_statistics()
            
            print("\n📈 系统统计")
            print("-" * 40)
            print(f"运行时间: {stats['performance']['uptime']:.2f} 秒")
            print(f"处理帧数: {stats['ai']['total_frames']}")
            print(f"检测总数: {stats['ai']['total_detections']}")
            print(f"平均FPS: {stats['performance']['avg_fps']:.2f}")
            print(f"内存使用: {stats['performance']['memory_usage']:.2f} MB")
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    async def run_test(self):
        """运行系统测试"""
        print("🔧 运行系统测试...")
        
        try:
            # 导入测试模块
            from test_final import test_complete_system
            
            print("   ├─ 执行完整系统测试...")
            success = await test_complete_system()
            
            if success:
                print("   └─ ✅ 系统测试通过")
            else:
                print("   └─ ❌ 系统测试失败")
                
        except Exception as e:
            print(f"   └─ ❌ 测试错误: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🔧 系统功能说明:

1. 启动系统: 初始化AI引擎、硬件管理器等核心组件
2. 停止系统: 安全关闭所有组件和连接
3. 模拟演示: 运行模拟的目标检测和交通分析
4. 系统状态: 查看各组件的运行状态
5. 检测统计: 查看性能指标和检测数据
6. 系统测试: 运行完整的功能测试

💡 使用提示:
- 演示前请先启动系统
- 可以随时查看系统状态
- 演示过程中会显示实时检测结果
- 使用Ctrl+C可以快速停止演示

🎯 演示特色:
- 实时模拟目标检测
- 交通流量分析
- 异常情况报警
- 性能统计展示
        """
        print(help_text)
    
    async def run(self):
        """运行交互式演示"""
        self.print_banner()
        
        while True:
            try:
                self.print_menu()
                choice = input("\n请选择操作 (1-9): ").strip()
                
                if choice == "1":
                    await self.start_system()
                elif choice == "2":
                    await self.stop_system()
                elif choice == "3":
                    await self.start_demo()
                elif choice == "4":
                    self.stop_demo()
                elif choice == "5":
                    self.print_status()
                elif choice == "6":
                    await self.show_statistics()
                elif choice == "7":
                    await self.run_test()
                elif choice == "8":
                    self.show_help()
                elif choice == "9":
                    print("👋 感谢使用无人机交通警察系统!")
                    if self.system_manager:
                        await self.stop_system()
                    break
                else:
                    print("❌ 无效选择，请输入1-9")
                
                # 等待用户按键继续
                if choice in ["1", "2", "5", "6", "7", "8"]:
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n⚠️ 检测到中断信号")
                if self.demo_active:
                    self.stop_demo()
                choice = input("是否退出程序? (y/n): ").lower().strip()
                if choice == 'y':
                    if self.system_manager:
                        await self.stop_system()
                    break
            except Exception as e:
                print(f"\n❌ 运行错误: {e}")
                input("按回车键继续...")

def main():
    """主函数"""
    try:
        demo = InteractiveDemo()
        asyncio.run(demo.run())
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
