# 🚁 无人机交通警察系统 - 详细部署教程

## 📋 目录
- [系统概述](#系统概述)
- [环境要求](#环境要求)
- [安装步骤](#安装步骤)
- [配置说明](#配置说明)
- [启动系统](#启动系统)
- [功能测试](#功能测试)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🎯 系统概述

**无人机交通警察系统**是一个基于AI的智能交通监控系统，具备以下核心功能：

### 🔧 核心功能
- **实时目标检测**: 基于YOLOv8的高精度车辆、行人检测
- **多目标跟踪**: 智能轨迹跟踪和行为分析
- **交通流分析**: 实时交通密度、流量、拥堵分析
- **事故检测**: 自动识别交通事故和异常行为
- **违法识别**: 超速、逆行、未戴头盔等违法行为检测
- **智能报告**: AI生成的交通分析报告
- **语音播报**: 实时交通状况语音提醒

### 🏗️ 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   硬件层        │    │    AI引擎       │    │   业务引擎      │
│ - 无人机        │◄──►│ - YOLO检测      │◄──►│ - 交通分析      │
│ - 摄像头        │    │ - 目标跟踪      │    │ - 事故检测      │
│ - 通信模块      │    │ - 行为分析      │    │ - 违法识别      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据层        │    │   LLM引擎       │    │   API接口       │
│ - SQLite数据库  │    │ - 报告生成      │    │ - REST API      │
│ - 内存缓存      │    │ - 语音合成      │    │ - WebSocket     │
│ - 文件存储      │    │ - NLP处理       │    │ - Web界面       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 💻 环境要求

### 🖥️ 硬件要求

#### 最低配置
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 或更高
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间
- **GPU**: 可选，支持CUDA的NVIDIA显卡（推荐）

#### 推荐配置
- **CPU**: Intel i7-10700K / AMD Ryzen 7 3700X 或更高
- **内存**: 16GB RAM 或更多
- **存储**: 50GB 可用空间（SSD推荐）
- **GPU**: NVIDIA RTX 3060 或更高（8GB显存推荐）

### 🐧 软件要求

#### 操作系统
- **Linux**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Windows**: Windows 10/11 (64位)
- **macOS**: macOS 11+ (Intel/Apple Silicon)

#### 基础软件
- **Python**: 3.8 - 3.11
- **Conda**: Miniconda 或 Anaconda
- **Git**: 最新版本
- **CUDA**: 11.8+ (如果使用GPU)

## 🚀 安装步骤

### 步骤1: 环境准备

#### 1.1 安装Conda
```bash
# Linux/macOS
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh

# 或使用包管理器
# Ubuntu/Debian
sudo apt update && sudo apt install -y conda

# CentOS/RHEL
sudo yum install -y conda
```

#### 1.2 安装CUDA (GPU用户)
```bash
# Ubuntu 20.04/22.04
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-ubuntu2004.pin
sudo mv cuda-ubuntu2004.pin /etc/apt/preferences.d/cuda-repository-pin-600
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
sudo dpkg -i cuda-repo-ubuntu2004-11-8-local_11.8.0-520.61.05-1_amd64.deb
sudo cp /var/cuda-repo-ubuntu2004-11-8-local/cuda-*-keyring.gpg /usr/share/keyrings/
sudo apt-get update
sudo apt-get -y install cuda
```

### 步骤2: 获取源码

```bash
# 克隆项目
git clone <your-repository-url>
cd air_traffic_police

# 或者解压源码包
unzip air_traffic_police.zip
cd air_traffic_police
```

### 步骤3: 创建虚拟环境

```bash
# 创建conda环境
conda create -n yolov5 python=3.9 -y
conda activate yolov5

# 验证环境
python --version  # 应显示 Python 3.9.x
which python      # 应显示conda环境路径
```

### 步骤4: 安装依赖

#### 4.1 安装PyTorch (GPU版本)
```bash
# CUDA 11.8
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 验证GPU支持
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'CUDA devices: {torch.cuda.device_count()}')"
```

#### 4.2 安装PyTorch (CPU版本)
```bash
# 仅CPU版本
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

#### 4.3 安装项目依赖
```bash
# 安装核心依赖
pip install -r requirements.txt

# 安装额外依赖
pip install ultralytics>=8.0.0
pip install opencv-python>=4.8.0
pip install fastapi>=0.104.0
pip install uvicorn>=0.24.0
pip install websockets>=12.0
pip install pydantic>=2.5.0
pip install pydantic-settings>=2.1.0
pip install sqlalchemy>=2.0.0
pip install aiosqlite>=0.19.0
```

### 步骤5: 下载模型文件

```bash
# 创建模型目录
mkdir -p models

# 下载YOLOv8模型
cd models
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8l.pt
cd ..

# 验证模型文件
ls -la models/
```

### 步骤6: 准备测试数据

```bash
# 创建数据目录
mkdir -p data/videos
mkdir -p data/images

# 下载示例视频（可选）
cd data/videos
# 放置您的测试视频文件，如 action1.mp4
cd ../..
```

## ⚙️ 配置说明

### 配置文件结构
```
config/
├── ai_config.py          # AI引擎配置
├── api_config.py         # API服务配置
├── hardware_config.py    # 硬件设备配置
└── system_config.py      # 系统全局配置
```

### 主要配置项

#### AI配置 (config/ai_config.py)
```python
# 模型配置
MODEL_TYPE = ModelType.YOLOV8S  # 可选: YOLOV8S, YOLOV8M, YOLOV8L
DEVICE = DeviceType.AUTO        # 自动检测最佳设备
CONFIDENCE_THRESHOLD = 0.25     # 检测置信度阈值
IOU_THRESHOLD = 0.45           # NMS IoU阈值

# 性能配置
INPUT_SIZE = (640, 640)        # 输入图像尺寸
BATCH_SIZE = 1                 # 批处理大小
HALF_PRECISION = False         # 是否使用半精度
```

#### 硬件配置 (config/hardware_config.py)
```python
# 摄像头配置
CAMERA_COUNT = 1               # 摄像头数量
CAMERA_RESOLUTION = (1920, 1080)  # 分辨率
CAMERA_FPS = 30               # 帧率

# 无人机配置
DRONE_ENABLED = False         # 是否启用无人机
DRONE_COUNT = 0              # 无人机数量
```

#### API配置 (config/api_config.py)
```python
# 服务器配置
HOST = "0.0.0.0"              # 监听地址
PORT = 8000                   # 监听端口
DEBUG = False                 # 调试模式

# 安全配置
SECRET_KEY = "your-secret-key"  # JWT密钥
ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Token过期时间
```

## 🎬 启动系统

### 方法1: 快速启动 (推荐)

```bash
# 激活环境
conda activate yolov5

# 快速启动
python start.py

# 选择启动模式
# 1. Demo Mode - 演示模式
# 2. API Mode - API服务模式
# 3. Full Mode - 完整模式
```

### 方法2: 命令行启动

```bash
# 激活环境
conda activate yolov5

# 演示模式
python main.py --mode demo

# API服务模式
python main.py --mode api

# 完整模式 (API + 实时处理)
python main.py --mode full

# 指定配置文件
python main.py --mode api --config config/custom_config.py
```

### 方法3: 分步启动

```bash
# 1. 系统测试
python test_final.py

# 2. 启动API服务
uvicorn api.app:app --host 0.0.0.0 --port 8000 --reload

# 3. 启动Web界面 (如果有)
cd web && npm start
```

## 🧪 功能测试

### 基础功能测试

```bash
# 1. 系统完整性测试
python test_final.py

# 2. AI引擎测试
python -c "
from core.ai.ai_engine import AIEngine
import asyncio
async def test():
    engine = AIEngine()
    success = await engine.initialize()
    print(f'AI Engine: {\"✅ OK\" if success else \"❌ FAIL\"}')
asyncio.run(test())
"

# 3. 硬件连接测试
python -c "
from core.hardware.hardware_manager import HardwareManager
import asyncio
async def test():
    hw = HardwareManager()
    success = await hw.initialize()
    print(f'Hardware: {\"✅ OK\" if success else \"❌ FAIL\"}')
asyncio.run(test())
"
```

### API接口测试

```bash
# 启动API服务
python main.py --mode api &

# 等待服务启动
sleep 5

# 测试健康检查
curl http://localhost:8000/health

# 测试系统状态
curl http://localhost:8000/api/v1/system/status

# 测试系统信息
curl http://localhost:8000/api/v1/system/info

# 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

### 性能测试

```bash
# GPU性能测试
python -c "
import torch
import time
if torch.cuda.is_available():
    device = torch.device('cuda')
    x = torch.randn(1000, 1000).to(device)
    start = time.time()
    for _ in range(100):
        y = torch.mm(x, x)
    end = time.time()
    print(f'GPU Performance: {100/(end-start):.2f} ops/sec')
else:
    print('GPU not available')
"

# 内存使用测试
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f'Memory Usage: {memory_mb:.2f} MB')
"
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. CUDA相关问题

**问题**: `CUDA out of memory`
```bash
# 解决方案1: 降低批处理大小
# 编辑 config/ai_config.py
BATCH_SIZE = 1

# 解决方案2: 使用更小的模型
MODEL_TYPE = ModelType.YOLOV8S

# 解决方案3: 启用半精度
HALF_PRECISION = True
```

**问题**: `CUDA not available`
```bash
# 检查CUDA安装
nvidia-smi
nvcc --version

# 重新安装PyTorch
pip uninstall torch torchvision torchaudio
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

#### 2. 依赖问题

**问题**: `ModuleNotFoundError`
```bash
# 检查环境
conda list | grep <package-name>

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 检查Python路径
python -c "import sys; print(sys.path)"
```

**问题**: `Pydantic validation error`
```bash
# 更新Pydantic
pip install pydantic>=2.5.0 pydantic-settings>=2.1.0

# 检查配置文件语法
python -c "from config.ai_config import AIConfig; print('Config OK')"
```

#### 3. 性能问题

**问题**: 检测速度慢
```bash
# 优化建议
# 1. 使用GPU加速
DEVICE = DeviceType.CUDA

# 2. 降低输入分辨率
INPUT_SIZE = (416, 416)

# 3. 提高置信度阈值
CONFIDENCE_THRESHOLD = 0.5

# 4. 启用半精度
HALF_PRECISION = True
```

#### 4. 内存问题

**问题**: 内存不足
```bash
# 监控内存使用
htop
# 或
watch -n 1 'free -h'

# 优化内存使用
# 1. 减少缓存大小
# 2. 降低图像分辨率
# 3. 减少并发处理数量
```

### 日志分析

```bash
# 查看系统日志
tail -f logs/system.log

# 查看错误日志
grep ERROR logs/system.log

# 查看性能日志
grep "Processing time" logs/system.log
```

## ⚡ 性能优化

### GPU优化

```python
# config/ai_config.py 优化配置
MODEL_TYPE = ModelType.YOLOV8S    # 平衡性能和精度
DEVICE = DeviceType.AUTO          # 自动选择最佳设备
HALF_PRECISION = True             # 启用半精度加速
BATCH_SIZE = 4                    # 根据显存调整
INPUT_SIZE = (640, 640)           # 标准输入尺寸
```

### 系统优化

```bash
# 1. 设置CPU亲和性
taskset -c 0-3 python main.py --mode api

# 2. 调整系统参数
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'net.core.rmem_max=134217728' | sudo tee -a /etc/sysctl.conf

# 3. 使用高性能调度器
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

### 数据库优化

```python
# config/system_config.py
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600
}

CACHE_CONFIG = {
    "max_size": 1000,
    "ttl": 300
}
```

## 🌐 生产部署

### Docker部署

```dockerfile
# Dockerfile
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

WORKDIR /app
COPY . .

RUN apt-get update && apt-get install -y python3 python3-pip
RUN pip3 install -r requirements.txt

EXPOSE 8000
CMD ["python3", "main.py", "--mode", "api"]
```

```bash
# 构建镜像
docker build -t air-traffic-police .

# 运行容器
docker run -d --gpus all -p 8000:8000 air-traffic-police
```

### 系统服务

```bash
# 创建systemd服务
sudo tee /etc/systemd/system/air-traffic-police.service << EOF
[Unit]
Description=Air Traffic Police System
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/path/to/air_traffic_police
Environment=PATH=/home/<USER>/miniconda3/envs/yolov5/bin
ExecStart=/home/<USER>/miniconda3/envs/yolov5/bin/python main.py --mode api
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启用服务
sudo systemctl enable air-traffic-police
sudo systemctl start air-traffic-police
sudo systemctl status air-traffic-police
```

### 负载均衡

```nginx
# nginx配置
upstream air_traffic_police {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://air_traffic_police;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📞 技术支持

### 获取帮助

1. **查看文档**: 详细阅读本部署指南
2. **检查日志**: 查看系统日志文件
3. **运行测试**: 执行测试脚本诊断问题
4. **社区支持**: 在GitHub Issues中提问

### 联系信息

- **项目地址**: [GitHub Repository]
- **技术文档**: [Documentation]
- **问题反馈**: [Issues]

---

🎉 **恭喜！您已成功部署无人机交通警察系统！**

系统现在已准备就绪，可以开始进行交通监控和分析工作。祝您在大学竞赛中取得优异成绩！
