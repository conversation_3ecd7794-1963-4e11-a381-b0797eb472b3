#!/usr/bin/env python3
"""
快速安全帽检测测试
简单验证安全帽检测功能
"""

import sys
import cv2
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    """主函数"""
    print("🛡️ 快速安全帽检测测试")
    print("=" * 50)
    
    try:
        # 导入必要模块
        from core.ai.detection.helmet_detector import HelmetDetector
        from config.ai_config import AIConfig
        
        print("✅ 模块导入成功")
        
        # 创建检测器
        ai_config = AIConfig()
        helmet_config = ai_config.get_helmet_detection_config()
        detector = HelmetDetector(helmet_config)
        
        print("✅ 安全帽检测器初始化成功")
        
        # 检查视频文件
        video_path = "data/videos/action1.mp4"
        if not Path(video_path).exists():
            print(f"❌ 视频文件不存在: {video_path}")
            return
        
        print("✅ 视频文件检查通过")
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        print("✅ 视频文件打开成功")
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📹 视频信息: {width}x{height}, {fps:.1f}FPS, {frame_count}帧")
        
        # 处理视频帧
        print("\n🎬 开始处理视频...")
        processed_frames = 0
        total_riders = 0
        total_violations = 0
        
        # 处理前50帧进行测试
        for i in range(min(50, frame_count)):
            ret, frame = cap.read()
            if not ret:
                break
            
            processed_frames += 1
            
            # 执行安全帽检测
            violations = detector.detect_helmet_violations(frame)
            
            if violations:
                frame_riders = len(violations)
                frame_violations = sum(1 for v in violations if not v.helmet_detected)
                
                total_riders += frame_riders
                total_violations += frame_violations
                
                print(f"帧 {processed_frames:3d}: {frame_riders}个骑行者, {frame_violations}个违规")
                
                # 显示详细信息
                for j, violation in enumerate(violations):
                    status = "❌ 未戴" if not violation.helmet_detected else "✅ 佩戴"
                    vehicle = violation.vehicle_detection.class_name if violation.vehicle_detection else "无车辆"
                    confidence = violation.helmet_confidence
                    risk = violation.risk_level
                    
                    print(f"  骑行者{j+1}: {status}安全帽 (置信度:{confidence:.2f}, 车辆:{vehicle}, 风险:{risk})")
            
            # 每10帧显示一次进度
            if processed_frames % 10 == 0:
                print(f"📊 进度: {processed_frames}/{min(50, frame_count)} 帧")
        
        cap.release()
        
        # 获取最终统计
        stats = detector.get_violation_statistics()
        
        print("\n" + "=" * 50)
        print("📊 检测结果统计")
        print("=" * 50)
        print(f"处理帧数: {processed_frames}")
        print(f"检测到的骑行者总数: {stats['total_riders']}")
        print(f"违规数量: {stats['violation_count']}")
        print(f"违规率: {stats['violation_rate']:.1f}%")
        print(f"合规率: {stats['compliance_rate']:.1f}%")
        print(f"检测置信度阈值: {stats['helmet_confidence_threshold']}")
        
        print("\n🎯 检测效果评估:")
        if stats['total_riders'] > 0:
            print("✅ 成功检测到骑行者")
            if stats['violation_rate'] > 0:
                print("✅ 成功识别安全帽违规")
            else:
                print("ℹ️ 未发现安全帽违规（所有骑行者都佩戴了安全帽）")
        else:
            print("ℹ️ 在测试帧中未检测到骑行者")
        
        print("\n🚀 功能验证:")
        print("✅ 安全帽检测器工作正常")
        print("✅ 视频处理功能正常")
        print("✅ 统计功能正常")
        print("✅ 违规识别功能正常")
        
        print("\n💡 使用建议:")
        print("1. 启动完整演示: python start.py → 选择 '5. 🛡️ 安全帽检测演示'")
        print("2. 启动Qt6界面: python demo_helmet_detection.py")
        print("3. 集成到其他演示: 安全帽检测已集成到AI引擎中")
        
        print("\n🎉 安全帽检测功能测试完成！")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖包")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
