#!/usr/bin/env python3
"""
安全帽检测功能测试
验证电动车骑行者未戴安全帽检测功能
"""

import sys
import cv2
import time
import numpy as np
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🛡️ {title}")
    print("=" * 60)

def print_result(test_name, success, details=""):
    """打印结果"""
    status = "✅ 成功" if success else "❌ 失败"
    print(f"{test_name}: {status}")
    if details:
        print(f"   详情: {details}")

def test_helmet_detector_import():
    """测试安全帽检测器导入"""
    print_header("安全帽检测器导入测试")
    
    try:
        from core.ai.detection.helmet_detector import HelmetDetector, HelmetDetection
        print_result("导入HelmetDetector", True)
        
        from config.ai_config import AIConfig
        print_result("导入AIConfig", True)
        
        return True
        
    except ImportError as e:
        print_result("导入测试", False, f"导入错误: {e}")
        return False
    except Exception as e:
        print_result("导入测试", False, f"未知错误: {e}")
        return False

def test_helmet_detector_initialization():
    """测试安全帽检测器初始化"""
    print_header("安全帽检测器初始化测试")
    
    try:
        from core.ai.detection.helmet_detector import HelmetDetector
        from config.ai_config import AIConfig
        
        # 创建配置
        ai_config = AIConfig()
        helmet_config = ai_config.get_helmet_detection_config()
        print_result("获取配置", True, f"配置项数量: {len(helmet_config)}")
        
        # 初始化检测器
        detector = HelmetDetector(helmet_config)
        print_result("初始化检测器", True)
        
        # 测试配置访问
        stats = detector.get_violation_statistics()
        print_result("获取统计信息", True, f"统计项: {list(stats.keys())}")
        
        return True
        
    except Exception as e:
        print_result("初始化测试", False, f"错误: {e}")
        return False

def test_base_detector():
    """测试基础检测器"""
    print_header("基础检测器测试")
    
    try:
        from core.ai.detection.yolo_detector import YOLODetector
        from config.ai_config import AIConfig
        
        # 创建基础检测器
        ai_config = AIConfig()
        model_config = ai_config.get_model_config()
        detector = YOLODetector(model_config)
        
        print_result("创建YOLO检测器", True)
        
        # 尝试加载模型
        if detector.load_model():
            print_result("加载YOLO模型", True)
            
            # 创建测试图像
            test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # 执行检测
            detections = detector.detect(test_image)
            print_result("执行检测", True, f"检测到 {len(detections)} 个目标")
            
            return True
        else:
            print_result("加载YOLO模型", False, "模型加载失败")
            return False
            
    except Exception as e:
        print_result("基础检测器测试", False, f"错误: {e}")
        return False

def test_helmet_detection_with_sample_image():
    """使用示例图像测试安全帽检测"""
    print_header("示例图像安全帽检测测试")
    
    try:
        from core.ai.detection.helmet_detector import HelmetDetector
        from config.ai_config import AIConfig
        
        # 创建检测器
        ai_config = AIConfig()
        helmet_config = ai_config.get_helmet_detection_config()
        detector = HelmetDetector(helmet_config)
        
        # 创建测试图像（模拟有人员和车辆的场景）
        test_image = create_test_image_with_riders()
        print_result("创建测试图像", True, f"图像尺寸: {test_image.shape}")
        
        # 执行安全帽检测
        helmet_violations = detector.detect_helmet_violations(test_image)
        print_result("执行安全帽检测", True, f"检测到 {len(helmet_violations)} 个骑行者")
        
        # 分析检测结果
        if helmet_violations:
            for i, violation in enumerate(helmet_violations):
                helmet_status = "佩戴" if violation.helmet_detected else "未佩戴"
                vehicle_type = violation.vehicle_detection.class_name if violation.vehicle_detection else "无车辆"
                print(f"   骑行者 {i+1}: {helmet_status}安全帽, 车辆: {vehicle_type}, 风险: {violation.risk_level}")
        
        # 获取统计信息
        stats = detector.get_violation_statistics()
        print_result("获取统计信息", True, 
                    f"总骑行者: {stats['total_riders']}, 违规: {stats['violation_count']}")
        
        return True
        
    except Exception as e:
        print_result("示例图像测试", False, f"错误: {e}")
        return False

def create_test_image_with_riders():
    """创建包含骑行者的测试图像"""
    # 创建640x480的测试图像
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景色
    image[:] = (50, 50, 50)  # 深灰色背景
    
    # 模拟道路
    cv2.rectangle(image, (0, 300), (640, 480), (80, 80, 80), -1)
    
    # 添加一些模拟的人员和车辆（用简单的矩形表示）
    # 人员1 - 模拟骑摩托车的人
    cv2.rectangle(image, (100, 200), (140, 300), (0, 255, 0), -1)  # 人员
    cv2.rectangle(image, (80, 280), (160, 320), (0, 0, 255), -1)   # 摩托车
    
    # 人员2 - 模拟骑自行车的人
    cv2.rectangle(image, (300, 220), (330, 310), (0, 255, 0), -1)  # 人员
    cv2.rectangle(image, (290, 300), (340, 330), (255, 0, 0), -1)  # 自行车
    
    # 人员3 - 单独的行人
    cv2.rectangle(image, (500, 250), (520, 350), (0, 255, 0), -1)  # 人员
    
    return image

def test_video_processing():
    """测试视频处理"""
    print_header("视频处理测试")
    
    video_path = Path("data/videos/action1.mp4")
    if not video_path.exists():
        print_result("视频文件检查", False, f"文件不存在: {video_path}")
        return False
    
    try:
        from core.ai.detection.helmet_detector import HelmetDetector
        from config.ai_config import AIConfig
        
        # 创建检测器
        ai_config = AIConfig()
        helmet_config = ai_config.get_helmet_detection_config()
        detector = HelmetDetector(helmet_config)
        
        # 打开视频
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            print_result("视频打开", False, "无法打开视频文件")
            return False
        
        print_result("视频打开", True)
        
        # 处理前几帧
        frame_count = 0
        total_violations = 0
        
        for i in range(10):  # 处理前10帧
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 执行检测
            violations = detector.detect_helmet_violations(frame)
            total_violations += len(violations)
            
            if violations:
                print(f"   帧 {frame_count}: 检测到 {len(violations)} 个骑行者")
        
        cap.release()
        
        print_result("视频处理", True, 
                    f"处理了 {frame_count} 帧, 总计检测到 {total_violations} 个骑行者")
        
        # 获取最终统计
        stats = detector.get_violation_statistics()
        print_result("最终统计", True, 
                    f"违规率: {stats.get('violation_rate', 0):.1f}%")
        
        return True
        
    except Exception as e:
        print_result("视频处理测试", False, f"错误: {e}")
        return False

def test_ai_engine_integration():
    """测试AI引擎集成"""
    print_header("AI引擎集成测试")
    
    try:
        from core.ai.ai_engine import AIEngine
        
        # 创建AI引擎
        config = {
            "helmet_detection": {
                "enabled": True,
                "helmet_confidence_threshold": 0.3
            }
        }
        
        engine = AIEngine(config)
        print_result("创建AI引擎", True)
        
        # 初始化
        import asyncio
        
        async def test_engine():
            if await engine.initialize():
                print_result("AI引擎初始化", True)
                
                # 创建测试图像
                test_image = create_test_image_with_riders()
                
                # 处理帧
                result = await engine.process_frame(test_image)
                
                if "helmet_violations" in result:
                    violations = result["helmet_violations"]
                    print_result("安全帽检测集成", True, 
                                f"检测到 {len(violations)} 个安全帽违规")
                else:
                    print_result("安全帽检测集成", False, "结果中没有安全帽检测数据")
                
                # 获取统计信息
                stats = engine.get_helmet_statistics()
                print_result("获取安全帽统计", True, f"统计项: {list(stats.keys())}")
                
                await engine.cleanup()
                return True
            else:
                print_result("AI引擎初始化", False)
                return False
        
        return asyncio.run(test_engine())
        
    except Exception as e:
        print_result("AI引擎集成测试", False, f"错误: {e}")
        return False

def main():
    """主函数"""
    print_header("安全帽检测功能测试")
    
    print("🎯 这个测试将验证电动车骑行者未戴安全帽检测功能")
    
    # 测试结果
    results = {}
    
    # 1. 导入测试
    results['import'] = test_helmet_detector_import()
    
    # 2. 初始化测试
    results['initialization'] = test_helmet_detector_initialization()
    
    # 3. 基础检测器测试
    results['base_detector'] = test_base_detector()
    
    # 4. 示例图像测试
    results['sample_image'] = test_helmet_detection_with_sample_image()
    
    # 5. 视频处理测试
    results['video_processing'] = test_video_processing()
    
    # 6. AI引擎集成测试
    results['ai_engine_integration'] = test_ai_engine_integration()
    
    # 汇总结果
    print_header("测试结果汇总")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        print_result(test_name.replace('_', ' ').title(), result)
    
    print(f"\n📊 测试汇总: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！安全帽检测功能已成功集成！")
        print("✅ 功能特点:")
        print("   - 🎯 自动检测电动车骑行者")
        print("   - 🛡️ 识别是否佩戴安全帽")
        print("   - ⚠️ 评估违规风险等级")
        print("   - 📊 提供详细统计信息")
        print("   - 🎬 支持实时视频处理")
        print("\n🚀 使用方式:")
        print("   python start.py → 选择 '5. 🛡️ 安全帽检测演示'")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 个测试失败")
        print("💡 请检查错误信息并进行相应修复")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
