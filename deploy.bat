@echo off
REM 🚁 无人机交通警察系统 - Windows自动部署脚本
REM 作者: AI Assistant
REM 版本: 1.0.0

setlocal enabledelayedexpansion

echo 🚁 无人机交通警察系统 - Windows自动部署脚本
echo ================================================

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] 检测到管理员权限
) else (
    echo [WARNING] 建议以管理员身份运行此脚本
)

REM 检查Python安装
echo [INFO] 检查Python安装...
python --version >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [SUCCESS] 检测到Python版本: !PYTHON_VERSION!
) else (
    echo [ERROR] 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查Conda安装
echo [INFO] 检查Conda安装...
conda --version >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] Conda已安装
) else (
    echo [INFO] 安装Miniconda...
    echo 请手动下载并安装Miniconda:
    echo https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe
    echo 安装完成后重新运行此脚本
    pause
    exit /b 1
)

REM 检查Git安装
echo [INFO] 检查Git安装...
git --version >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] Git已安装
) else (
    echo [WARNING] 未检测到Git，建议安装Git以便后续更新
)

REM 检查NVIDIA GPU
echo [INFO] 检查GPU支持...
nvidia-smi >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] 检测到NVIDIA GPU
    set GPU_AVAILABLE=1
) else (
    echo [INFO] 未检测到NVIDIA GPU，将使用CPU版本
    set GPU_AVAILABLE=0
)

REM 创建虚拟环境
echo [INFO] 创建Python虚拟环境...
conda env list | findstr "yolov5" >nul
if %errorLevel% == 0 (
    echo [WARNING] 环境yolov5已存在
    set /p "recreate=是否重新创建环境? (y/n): "
    if /i "!recreate!"=="y" (
        conda env remove -n yolov5 -y
        conda create -n yolov5 python=3.9 -y
    )
) else (
    conda create -n yolov5 python=3.9 -y
)

if %errorLevel% == 0 (
    echo [SUCCESS] 虚拟环境创建完成
) else (
    echo [ERROR] 虚拟环境创建失败
    pause
    exit /b 1
)

REM 激活环境并安装依赖
echo [INFO] 安装项目依赖...
call conda activate yolov5

REM 安装PyTorch
if !GPU_AVAILABLE! == 1 (
    echo [INFO] 安装GPU版本PyTorch...
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
) else (
    echo [INFO] 安装CPU版本PyTorch...
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
)

REM 安装其他依赖
echo [INFO] 安装其他依赖包...
pip install ultralytics>=8.0.0
pip install opencv-python>=4.8.0
pip install fastapi>=0.104.0
pip install uvicorn>=0.24.0
pip install websockets>=12.0
pip install pydantic>=2.5.0
pip install pydantic-settings>=2.1.0
pip install sqlalchemy>=2.0.0
pip install aiosqlite>=0.19.0
pip install numpy>=1.21.0
pip install pillow>=8.3.0
pip install requests>=2.28.0
pip install python-multipart>=0.0.5
pip install jinja2>=3.1.0

REM 安装requirements.txt中的依赖（如果存在）
if exist requirements.txt (
    pip install -r requirements.txt
)

echo [SUCCESS] 依赖安装完成

REM 创建目录结构
echo [INFO] 创建目录结构...
if not exist "models" mkdir models
if not exist "data\videos" mkdir data\videos
if not exist "data\images" mkdir data\images
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp
if not exist "output" mkdir output

echo [SUCCESS] 目录结构创建完成

REM 下载模型文件
echo [INFO] 下载YOLO模型文件...
cd models

if not exist "yolov8s.pt" (
    echo [INFO] 下载YOLOv8s模型...
    curl -L -o yolov8s.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
)

if not exist "yolov8m.pt" (
    echo [INFO] 下载YOLOv8m模型...
    curl -L -o yolov8m.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt
)

cd ..
echo [SUCCESS] 模型文件下载完成

REM 创建启动脚本
echo [INFO] 创建启动脚本...

REM 创建start_system.bat
echo @echo off > start_system.bat
echo REM 无人机交通警察系统启动脚本 >> start_system.bat
echo. >> start_system.bat
echo call conda activate yolov5 >> start_system.bat
echo. >> start_system.bat
echo echo 🚁 启动无人机交通警察系统... >> start_system.bat
echo echo 请选择启动模式: >> start_system.bat
echo echo 1. 演示模式 ^(Demo Mode^) >> start_system.bat
echo echo 2. API服务模式 ^(API Mode^) >> start_system.bat
echo echo 3. 完整模式 ^(Full Mode^) >> start_system.bat
echo echo 4. 系统测试 ^(Test Mode^) >> start_system.bat
echo. >> start_system.bat
echo set /p "choice=请输入选择 (1-4): " >> start_system.bat
echo. >> start_system.bat
echo if "%%choice%%"=="1" ^( >> start_system.bat
echo     echo 启动演示模式... >> start_system.bat
echo     python main.py --mode demo >> start_system.bat
echo ^) else if "%%choice%%"=="2" ^( >> start_system.bat
echo     echo 启动API服务模式... >> start_system.bat
echo     python main.py --mode api >> start_system.bat
echo ^) else if "%%choice%%"=="3" ^( >> start_system.bat
echo     echo 启动完整模式... >> start_system.bat
echo     python main.py --mode full >> start_system.bat
echo ^) else if "%%choice%%"=="4" ^( >> start_system.bat
echo     echo 运行系统测试... >> start_system.bat
echo     python test_final.py >> start_system.bat
echo ^) else ^( >> start_system.bat
echo     echo 无效选择，启动演示模式... >> start_system.bat
echo     python main.py --mode demo >> start_system.bat
echo ^) >> start_system.bat
echo. >> start_system.bat
echo pause >> start_system.bat

REM 创建stop_system.bat
echo @echo off > stop_system.bat
echo REM 停止系统脚本 >> stop_system.bat
echo. >> stop_system.bat
echo echo 停止无人机交通警察系统... >> stop_system.bat
echo taskkill /f /im python.exe >> stop_system.bat
echo echo 系统已停止 >> stop_system.bat
echo pause >> stop_system.bat

echo [SUCCESS] 启动脚本创建完成

REM 运行测试
echo [INFO] 运行系统测试...
python test_final.py

if %errorLevel% == 0 (
    echo [SUCCESS] 系统测试通过
) else (
    echo [ERROR] 系统测试失败，请检查配置
    pause
    exit /b 1
)

REM 完成部署
echo.
echo 🎉 部署完成！
echo ================================================
echo 启动系统: start_system.bat
echo 停止系统: stop_system.bat
echo 查看文档: DEPLOYMENT_GUIDE.md
echo.
echo 系统已准备就绪，祝您在竞赛中取得好成绩！
echo.
pause
