#!/usr/bin/env python3
"""
测试字体修复效果
验证中文显示是否正常
"""
import sys
import os

def test_matplotlib_chinese():
    """测试matplotlib中文显示"""
    print("=== 测试Matplotlib中文显示 ===")
    
    try:
        # 导入字体配置
        exec(open('font_config.py').read())
        
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 测试数据
        x = np.arange(10)
        vehicle_data = [2, 3, 5, 4, 6, 7, 5, 8, 6, 4]
        person_data = [1, 2, 1, 3, 2, 3, 4, 2, 3, 2]
        
        # 绘制图表
        ax.plot(x, vehicle_data, 'r-', label='车辆密度', linewidth=2, marker='o')
        ax.plot(x, person_data, 'b-', label='行人密度', linewidth=2, marker='s')
        
        # 设置标签和标题
        ax.set_xlabel('时间 (分钟)')
        ax.set_ylabel('密度 (个/平方米)')
        ax.set_title('交通密度实时监控图表')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加注释
        ax.annotate('高峰期', xy=(7, 8), xytext=(8, 9),
                   arrowprops=dict(arrowstyle='->', color='red'),
                   fontsize=12, color='red')
        
        # 保存图片
        plt.savefig('matplotlib_chinese_fixed.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ Matplotlib中文显示测试成功")
        print("  📁 已保存: matplotlib_chinese_fixed.png")
        return True
        
    except Exception as e:
        print(f"❌ Matplotlib中文显示测试失败: {e}")
        return False


def test_opencv_chinese():
    """测试OpenCV中文显示"""
    print("\n=== 测试OpenCV中文显示 ===")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建测试图像
        img = np.ones((400, 600, 3), dtype=np.uint8) * 240
        
        # 使用PIL绘制中文
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 加载中文字体
        font_paths = [
            "/usr/share/fonts/program_font/simhei.ttf",
            "/usr/share/fonts/MyFonts/simhei.ttf",
            "/usr/share/fonts/program_font/msyh.ttf",
            "/usr/share/fonts/MyFonts/msyh.ttf"
        ]
        
        font = None
        used_font = "默认字体"
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 24)
                    used_font = os.path.basename(font_path)
                    break
                except:
                    continue
        
        if font is None:
            font = ImageFont.load_default()
        
        # 绘制标题
        title_font = ImageFont.truetype(font_paths[0], 32) if os.path.exists(font_paths[0]) else font
        draw.text((50, 30), "交通监控系统界面测试", font=title_font, fill=(0, 0, 0))
        
        # 绘制测试文本
        test_texts = [
            ("当前状态:", "系统正常运行", (0, 150, 0)),
            ("检测结果:", "车辆: 15辆, 行人: 8人", (0, 0, 150)),
            ("密度分析:", "车辆密度: 0.025/m², 行人密度: 0.013/m²", (100, 100, 0)),
            ("区域面积:", "监控区域: 1200.5 平方米", (150, 0, 150)),
            ("处理性能:", "帧率: 25.3 FPS, 延迟: 40ms", (0, 100, 100)),
            ("事故检测:", "⚠️ 检测到车辆碰撞事故!", (255, 0, 0))
        ]
        
        y_pos = 100
        for label, content, color in test_texts:
            # 绘制标签
            draw.text((50, y_pos), label, font=font, fill=(50, 50, 50))
            # 绘制内容
            draw.text((150, y_pos), content, font=font, fill=color)
            y_pos += 40
        
        # 添加底部信息
        draw.text((50, 350), f"使用字体: {used_font}", font=font, fill=(100, 100, 100))
        
        # 转换回OpenCV格式并保存
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        cv2.imwrite('opencv_chinese_fixed.png', img_cv)
        
        print(f"✅ OpenCV中文显示测试成功")
        print(f"  📁 已保存: opencv_chinese_fixed.png")
        print(f"  🔤 使用字体: {used_font}")
        return True
        
    except Exception as e:
        print(f"❌ OpenCV中文显示测试失败: {e}")
        return False


def test_visualization_module():
    """测试可视化模块"""
    print("\n=== 测试可视化模块中文显示 ===")
    
    try:
        # 导入字体配置
        exec(open('font_config.py').read())

        import cv2
        import numpy as np
        from visualization import Visualizer
        from config import default_config
        from detector import Detection
        from data_manager import FrameData
        
        # 创建可视化器
        visualizer = Visualizer(default_config)
        
        # 创建测试帧
        test_frame = np.ones((480, 640, 3), dtype=np.uint8) * 60
        
        # 创建模拟检测结果
        vehicles = [
            Detection(
                bbox=(100, 100, 200, 200),
                confidence=0.89,
                class_id=0,
                class_name="car",
                center=(150, 150),
                area=10000
            ),
            Detection(
                bbox=(300, 150, 420, 280),
                confidence=0.94,
                class_id=1,
                class_name="truck",
                center=(360, 215),
                area=15600
            )
        ]
        
        persons = [
            Detection(
                bbox=(50, 300, 80, 380),
                confidence=0.82,
                class_id=2,
                class_name="person",
                center=(65, 340),
                area=2400
            ),
            Detection(
                bbox=(500, 280, 530, 360),
                confidence=0.76,
                class_id=2,
                class_name="person",
                center=(515, 320),
                area=2400
            )
        ]
        
        # 创建帧数据
        frame_data = FrameData(
            frame_id=1234,
            timestamp=1234567890.0,
            vehicle_count=2,
            person_count=2,
            vehicle_density=0.0017,
            person_density=0.0017,
            vehicles=vehicles,
            persons=persons
        )
        
        # 创建统计信息
        statistics = {
            'estimated_area_m2': 1200.5,
            'processing_fps': 25.3,
            'avg_vehicle_count': 2.8,
            'avg_person_count': 1.5
        }
        
        # 创建事故信息
        accident_info = {
            'detected': True,
            'type': 'collision',
            'confidence': 0.87,
            'description': '检测到车辆碰撞事故'
        }
        
        # 绘制检测结果
        result_frame = visualizer.draw_detections(test_frame, vehicles, persons)
        
        # 绘制统计信息
        result_frame = visualizer.draw_statistics(result_frame, frame_data, 
                                                statistics, accident_info)
        
        # 保存结果
        cv2.imwrite('visualization_module_fixed.png', result_frame)
        
        # 测试密度图
        vehicle_densities = [0.001 + 0.0005 * np.sin(i * 0.1) for i in range(50)]
        person_densities = [0.0005 + 0.0002 * np.cos(i * 0.15) for i in range(50)]
        
        plot_img = visualizer.draw_density_plot(vehicle_densities, person_densities)
        cv2.imwrite('density_plot_fixed.png', plot_img)
        
        print("✅ 可视化模块中文显示测试成功")
        print("  📁 已保存: visualization_module_fixed.png")
        print("  📁 已保存: density_plot_fixed.png")
        return True
        
    except Exception as e:
        print(f"❌ 可视化模块测试失败: {e}")
        return False


def check_generated_images():
    """检查生成的图片"""
    print("\n=== 检查生成的测试图片 ===")
    
    test_images = [
        'matplotlib_chinese_fixed.png',
        'opencv_chinese_fixed.png',
        'visualization_module_fixed.png',
        'density_plot_fixed.png',
        'matplotlib_font_test.png',
        'opencv_font_test.png',
        'final_chinese_test.png'
    ]
    
    found_images = []
    for img in test_images:
        if os.path.exists(img):
            size = os.path.getsize(img)
            print(f"  ✅ {img} ({size} bytes)")
            found_images.append(img)
        else:
            print(f"  ❌ {img} (未找到)")
    
    print(f"\n📊 总计找到 {len(found_images)}/{len(test_images)} 个测试图片")
    return len(found_images)


def main():
    """主函数"""
    print("🔤 中文字体修复效果测试")
    print("=" * 50)
    
    # 检查字体配置文件
    if not os.path.exists('font_config.py'):
        print("❌ 字体配置文件不存在，请先运行: python fix_chinese_font.py")
        return False
    
    # 运行测试
    test_results = []
    test_results.append(("Matplotlib中文显示", test_matplotlib_chinese()))
    test_results.append(("OpenCV中文显示", test_opencv_chinese()))
    test_results.append(("可视化模块", test_visualization_module()))
    
    # 检查生成的图片
    image_count = check_generated_images()
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("🧪 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 功能测试: {passed}/{total} 个通过")
    print(f"📊 生成图片: {image_count} 个")
    
    if passed == total and image_count >= 4:
        print("\n🎉 中文字体修复完全成功！")
        print("💡 所有中文显示功能正常工作")
        print("\n📋 建议:")
        print("1. 查看生成的测试图片确认显示效果")
        print("2. 如果显示正常，您的系统已完全支持中文")
        print("3. 可以正常使用交通监控系统了")
    else:
        print(f"\n⚠️ 部分测试失败或图片生成不完整")
        print("💡 建议检查系统字体安装情况")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
