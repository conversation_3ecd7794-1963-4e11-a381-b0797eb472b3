#!/usr/bin/env python3
"""
修复演示模式警告的脚本
安装可选依赖并配置系统以消除警告
"""

import subprocess
import sys
import os
from pathlib import Path

def print_status(message, status="INFO"):
    """打印状态信息"""
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "END": "\033[0m"
    }
    print(f"{colors.get(status, '')}{message}{colors['END']}")

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print_status(f"正在安装 {package_name}...", "INFO")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print_status(f"✅ {package_name} 安装成功", "SUCCESS")
        return True
    except subprocess.CalledProcessError as e:
        print_status(f"❌ {package_name} 安装失败: {e}", "ERROR")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_optional_dependencies():
    """安装可选依赖"""
    print_status("🔧 开始安装可选依赖...", "INFO")
    
    # 语音合成依赖
    packages = [
        ("pyttsx3>=2.90", "本地文字转语音库"),
        ("jieba>=0.42.1", "中文分词库"),
        ("edge-tts>=6.1.0", "微软Edge TTS"),
        ("gTTS>=2.3.0", "Google TTS"),
    ]
    
    success_count = 0
    for package, description in packages:
        if install_package(package, description):
            success_count += 1
    
    print_status(f"📦 安装完成: {success_count}/{len(packages)} 个包安装成功", "SUCCESS")
    
    # Azure Speech SDK (可选，需要API密钥)
    print_status("📝 注意: Azure Speech SDK需要API密钥，如需使用请手动安装:", "WARNING")
    print_status("pip install azure-cognitiveservices-speech>=1.30.0", "INFO")

def create_demo_config():
    """创建演示模式配置文件"""
    print_status("📝 创建演示模式配置...", "INFO")
    
    config_content = '''"""
演示模式配置文件
禁用不必要的警告和功能
"""

# 禁用语音功能的环境变量
import os

# 设置环境变量以禁用TTS功能
os.environ.setdefault("TTS_ENABLED", "false")
os.environ.setdefault("LLM_ENABLED", "true")  # 保持LLM功能启用

# 配置日志级别以减少警告
import logging
import warnings

# 设置特定模块的日志级别
logging.getLogger("azure").setLevel(logging.ERROR)
logging.getLogger("pyttsx3").setLevel(logging.ERROR)
logging.getLogger("jieba").setLevel(logging.ERROR)

# 过滤特定警告
warnings.filterwarnings("ignore", message=".*Azure Speech SDK not available.*")
warnings.filterwarnings("ignore", message=".*pyttsx3 not available.*")
warnings.filterwarnings("ignore", message=".*jieba not available.*")

print("🎯 演示模式配置已加载 - 警告已最小化")
'''
    
    with open("demo_config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print_status("✅ 演示配置文件已创建: demo_config.py", "SUCCESS")

def create_silent_demo_script():
    """创建静默演示脚本"""
    print_status("📝 创建静默演示脚本...", "INFO")
    
    script_content = '''#!/usr/bin/env python3
"""
静默演示模式启动脚本
最小化警告输出
"""

import sys
import os
import warnings
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 导入演示配置
try:
    import demo_config
except ImportError:
    pass

# 设置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# 过滤警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", message=".*not available.*")

def main():
    """主函数"""
    print("🚁 启动无人机交通警察系统 (静默模式)")
    print("=" * 50)
    
    try:
        # 导入并运行主程序
        from main import main as main_func
        import asyncio
        
        # 设置演示模式参数
        sys.argv = ["main.py", "--mode", "demo"]
        
        # 运行主程序
        asyncio.run(main_func())
        
    except KeyboardInterrupt:
        print("\\n👋 演示已停止")
    except Exception as e:
        print(f"❌ 演示运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    with open("demo_silent.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    # 添加执行权限
    os.chmod("demo_silent.py", 0o755)
    
    print_status("✅ 静默演示脚本已创建: demo_silent.py", "SUCCESS")

def update_voice_synthesizer():
    """更新语音合成器以减少警告"""
    print_status("🔧 更新语音合成器配置...", "INFO")
    
    voice_synthesizer_path = Path("core/llm/voice_synthesizer.py")
    if voice_synthesizer_path.exists():
        # 读取文件内容
        with open(voice_synthesizer_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换警告为调试信息
        content = content.replace(
            'self.logger.warning(f"TTS provider {provider} not available")',
            'self.logger.debug(f"TTS provider {provider} not available")'
        )
        
        # 写回文件
        with open(voice_synthesizer_path, "w", encoding="utf-8") as f:
            f.write(content)
        
        print_status("✅ 语音合成器配置已更新", "SUCCESS")
    else:
        print_status("⚠️ 语音合成器文件未找到", "WARNING")

def main():
    """主函数"""
    print("🔧 无人机交通警察系统 - 警告修复工具")
    print("=" * 50)
    
    print("此工具将:")
    print("1. 安装可选的语音和NLP依赖")
    print("2. 创建演示模式配置文件")
    print("3. 创建静默演示脚本")
    print("4. 更新配置以减少警告")
    print()
    
    choice = input("是否继续? (y/n): ").lower().strip()
    if choice != 'y':
        print("操作已取消")
        return
    
    # 1. 安装可选依赖
    install_optional_dependencies()
    
    # 2. 创建演示配置
    create_demo_config()
    
    # 3. 创建静默演示脚本
    create_silent_demo_script()
    
    # 4. 更新语音合成器
    update_voice_synthesizer()
    
    print()
    print("🎉 修复完成!")
    print("=" * 50)
    print("现在您可以使用以下方式启动演示:")
    print()
    print("方式1 (静默模式，最少警告):")
    print("  python demo_silent.py")
    print()
    print("方式2 (标准模式):")
    print("  python main.py --mode demo")
    print()
    print("方式3 (完全静默):")
    print("  python main.py --mode demo 2>/dev/null")
    print()
    print("注意: 这些警告不影响系统核心功能，只是提示某些可选功能不可用。")

if __name__ == "__main__":
    main()
