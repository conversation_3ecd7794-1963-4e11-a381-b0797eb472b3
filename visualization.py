"""
交通监控系统可视化模块
处理所有绘图和显示逻辑
"""
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
import time

from config import Config
from detector import Detection
from data_manager import FrameData


class Visualizer:
    """可视化器类，处理所有绘图和显示功能"""
    
    def __init__(self, config: Config):
        """
        初始化可视化器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.window_initialized = False
        
        # 颜色定义 (BGR格式)
        self.colors = {
            'vehicle': config.visualization.bbox_color,
            'person': (255, 0, 0),  # 蓝色
            'accident': config.visualization.accident_color,
            'text': config.visualization.text_color,
            'background': (255, 255, 255),  # 白色
            'grid': (200, 200, 200),  # 浅灰色
            'axis': (0, 0, 0)  # 黑色
        }
        
        # 字体设置
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = config.visualization.font_scale
        self.font_thickness = config.visualization.font_thickness
    
    def setup_windows(self, video_width: int, video_height: int):
        """
        设置显示窗口
        
        Args:
            video_width: 视频宽度
            video_height: 视频高度
        """
        if self.window_initialized:
            return
        
        # 创建可调节大小的窗口
        cv2.namedWindow("Traffic Detection", cv2.WINDOW_NORMAL)
        cv2.namedWindow("Traffic Density Plot", cv2.WINDOW_NORMAL)
        
        # 设置窗口大小
        display_width = int(video_width * self.config.visualization.display_scale)
        display_height = int(video_height * self.config.visualization.display_scale)
        cv2.resizeWindow("Traffic Detection", display_width, display_height)
        cv2.resizeWindow("Traffic Density Plot", *self.config.visualization.window_size)
        
        self.window_initialized = True
    
    def draw_detections(self, 
                       frame: np.ndarray, 
                       vehicles: List[Detection], 
                       persons: List[Detection],
                       accident_bboxes: List[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        在帧上绘制检测结果
        
        Args:
            frame: 输入帧
            vehicles: 车辆检测结果
            persons: 行人检测结果
            accident_bboxes: 事故相关的边界框
        
        Returns:
            绘制后的帧
        """
        result_frame = frame.copy()
        
        # 绘制车辆
        for vehicle in vehicles:
            self._draw_detection(result_frame, vehicle, self.colors['vehicle'])
        
        # 绘制行人
        for person in persons:
            self._draw_detection(result_frame, person, self.colors['person'])
        
        # 绘制事故相关的边界框
        if accident_bboxes:
            for bbox in accident_bboxes:
                x1, y1, x2, y2 = bbox
                cv2.rectangle(result_frame, (x1, y1), (x2, y2), self.colors['accident'], 3)
        
        return result_frame
    
    def _draw_detection(self, frame: np.ndarray, detection: Detection, color: Tuple[int, int, int]):
        """
        绘制单个检测结果
        
        Args:
            frame: 图像帧
            detection: 检测结果
            color: 绘制颜色
        """
        x1, y1, x2, y2 = detection.bbox
        
        # 绘制边界框
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        
        # 绘制标签
        label = f"{detection.class_name} {detection.confidence:.2f}"
        label_size = cv2.getTextSize(label, self.font, self.font_scale, self.font_thickness)[0]
        
        # 标签背景
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), color, -1)
        
        # 标签文字
        cv2.putText(frame, label, (x1, y1 - 5), self.font, 
                   self.font_scale, (255, 255, 255), self.font_thickness)
    
    def draw_statistics(self, 
                       frame: np.ndarray, 
                       frame_data: FrameData,
                       statistics: Dict,
                       accident_info: Optional[Dict] = None) -> np.ndarray:
        """
        在帧上绘制统计信息
        
        Args:
            frame: 输入帧
            frame_data: 帧数据
            statistics: 统计信息
            accident_info: 事故信息
        
        Returns:
            绘制后的帧
        """
        result_frame = frame.copy()
        
        # 准备文本信息
        texts = [
            f"Frame: {frame_data.frame_id}",
            f"Vehicles: {frame_data.vehicle_count} ({frame_data.vehicle_density:.4f}/m²)",
            f"Persons: {frame_data.person_count} ({frame_data.person_density:.4f}/m²)",
            f"Area: {statistics.get('estimated_area_m2', 0):.1f} m²",
            f"FPS: {statistics.get('processing_fps', 0):.1f}"
        ]
        
        # 添加事故信息
        if accident_info and accident_info.get('detected', False):
            texts.append(f"⚠️ {accident_info.get('type', 'Accident')} Detected!")
        
        # 绘制文本
        y_offset = 30
        for i, text in enumerate(texts):
            color = self.colors['accident'] if "Detected" in text else self.colors['text']
            cv2.putText(result_frame, text, (10, y_offset + i * 30), 
                       self.font, self.font_scale, color, self.font_thickness)
        
        return result_frame
    
    def draw_density_plot(self, 
                         vehicle_densities: List[float], 
                         person_densities: List[float]) -> np.ndarray:
        """
        绘制密度曲线图
        
        Args:
            vehicle_densities: 车辆密度历史数据
            person_densities: 行人密度历史数据
        
        Returns:
            密度图像
        """
        window_size = self.config.visualization.window_size
        max_points = self.config.visualization.max_plot_points
        
        # 创建白色背景
        img = np.full((window_size[1], window_size[0], 3), 255, dtype=np.uint8)
        
        # 绘制坐标轴
        self._draw_axes(img, window_size)
        
        # 绘制图例
        self._draw_legend(img, window_size)
        
        # 如果没有数据，返回空图
        if not vehicle_densities and not person_densities:
            return img
        
        # 计算最大密度值用于缩放
        all_densities = vehicle_densities + person_densities
        max_density = max(max(all_densities, default=0.001), 0.01)
        
        # 绘制车辆密度曲线（红色）
        if len(vehicle_densities) > 1:
            self._draw_curve(img, vehicle_densities, max_density, 
                           window_size, max_points, (0, 0, 255))
        
        # 绘制行人密度曲线（蓝色）
        if len(person_densities) > 1:
            self._draw_curve(img, person_densities, max_density, 
                           window_size, max_points, (255, 0, 0))
        
        return img
    
    def _draw_axes(self, img: np.ndarray, window_size: Tuple[int, int]):
        """绘制坐标轴"""
        margin = 50
        
        # X轴
        cv2.line(img, (margin, window_size[1] - margin), 
                (window_size[0] - 10, window_size[1] - margin), 
                self.colors['axis'], 1)
        
        # Y轴
        cv2.line(img, (margin, window_size[1] - margin), 
                (margin, 10), self.colors['axis'], 1)
        
        # 轴标签
        cv2.putText(img, "Time", (window_size[0]//2 - 20, window_size[1] - 10), 
                   self.font, 0.5, self.colors['axis'], 1)
        cv2.putText(img, "Density", (5, 20), self.font, 0.4, self.colors['axis'], 1)
        cv2.putText(img, "(count/m²)", (5, 35), self.font, 0.4, self.colors['axis'], 1)
    
    def _draw_legend(self, img: np.ndarray, window_size: Tuple[int, int]):
        """绘制图例"""
        # 车辆图例
        cv2.putText(img, "Vehicle", (window_size[0] - 80, 30), 
                   self.font, 0.5, (0, 0, 255), 1)
        cv2.line(img, (window_size[0] - 100, 25), (window_size[0] - 85, 25), 
                (0, 0, 255), 2)
        
        # 行人图例
        cv2.putText(img, "Person", (window_size[0] - 80, 50), 
                   self.font, 0.5, (255, 0, 0), 1)
        cv2.line(img, (window_size[0] - 100, 45), (window_size[0] - 85, 45), 
                (255, 0, 0), 2)
    
    def _draw_curve(self, 
                   img: np.ndarray, 
                   densities: List[float], 
                   max_density: float,
                   window_size: Tuple[int, int], 
                   max_points: int, 
                   color: Tuple[int, int, int]):
        """绘制密度曲线"""
        margin = 50
        plot_width = window_size[0] - margin - 10
        plot_height = window_size[1] - margin - 10
        
        points = []
        for i, density in enumerate(densities[-max_points:]):
            x = margin + int(i * plot_width / max_points)
            y = window_size[1] - margin - int((density / max_density) * plot_height)
            y = max(10, min(y, window_size[1] - margin))  # 限制Y坐标范围
            points.append((x, y))
        
        # 绘制连线
        for i in range(1, len(points)):
            cv2.line(img, points[i-1], points[i], color, 2)
    
    def show_frames(self, detection_frame: np.ndarray, plot_frame: np.ndarray):
        """
        显示帧
        
        Args:
            detection_frame: 检测结果帧
            plot_frame: 密度图帧
        """
        cv2.imshow("Traffic Detection", detection_frame)
        cv2.imshow("Traffic Density Plot", plot_frame)
    
    def wait_key(self, delay: int = 1) -> int:
        """
        等待按键
        
        Args:
            delay: 等待时间（毫秒）
        
        Returns:
            按键码
        """
        return cv2.waitKey(delay) & 0xFF
    
    def cleanup(self):
        """清理资源"""
        cv2.destroyAllWindows()
    
    def save_frame(self, frame: np.ndarray, filepath: str):
        """
        保存帧到文件
        
        Args:
            frame: 图像帧
            filepath: 文件路径
        """
        cv2.imwrite(filepath, frame)
    
    def create_video_writer(self, 
                           filepath: str, 
                           width: int, 
                           height: int, 
                           fps: float = 30.0) -> cv2.VideoWriter:
        """
        创建视频写入器
        
        Args:
            filepath: 输出文件路径
            width: 视频宽度
            height: 视频高度
            fps: 帧率
        
        Returns:
            视频写入器对象
        """
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        return cv2.VideoWriter(filepath, fourcc, fps, (width, height))
