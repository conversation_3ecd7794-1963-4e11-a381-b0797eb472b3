"""
简化版交通监控系统使用示例
不依赖复杂的外部库，展示重构后的代码结构
"""

def test_config_module():
    """测试配置模块"""
    print("=== 测试配置模块 ===")
    
    try:
        from config import Config, default_config
        
        # 测试默认配置
        print(f"✓ 默认配置加载成功")
        print(f"  模型路径: {default_config.model.model_path}")
        print(f"  视频路径: {default_config.video.video_path}")
        print(f"  输入尺寸: {default_config.model.input_size}")
        print(f"  置信度阈值: {default_config.model.conf_threshold}")
        
        # 测试自定义配置
        custom_config = Config()
        custom_config.model.conf_threshold = 0.3
        custom_config.video.frame_skip = 2
        print(f"✓ 自定义配置创建成功")
        print(f"  修改后置信度阈值: {custom_config.model.conf_threshold}")
        print(f"  修改后跳帧数: {custom_config.video.frame_skip}")
        
        # 测试配置验证
        is_valid = custom_config.validate()
        print(f"✓ 配置验证: {'通过' if is_valid else '失败'}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False


def test_utils_module():
    """测试工具函数模块"""
    print("\n=== 测试工具函数模块 ===")
    
    try:
        from traffic_utils import (
            compute_iou, compute_distance, get_bbox_center, 
            get_bbox_area, safe_divide, clamp
        )
        
        # 测试IoU计算
        box1 = (10, 10, 50, 50)
        box2 = (30, 30, 70, 70)
        iou = compute_iou(box1, box2)
        print(f"✓ IoU计算: {iou:.3f}")
        
        # 测试距离计算
        point1 = (0, 0)
        point2 = (3, 4)
        distance = compute_distance(point1, point2)
        print(f"✓ 距离计算: {distance:.3f}")
        
        # 测试边界框中心
        bbox = (10, 20, 50, 60)
        center = get_bbox_center(bbox)
        print(f"✓ 边界框中心: {center}")
        
        # 测试边界框面积
        area = get_bbox_area(bbox)
        print(f"✓ 边界框面积: {area}")
        
        # 测试安全除法
        result = safe_divide(10, 2)
        result_zero = safe_divide(10, 0, default=999)
        print(f"✓ 安全除法: {result}, {result_zero}")
        
        # 测试限制函数
        clamped = clamp(15, 0, 10)
        print(f"✓ 限制函数: {clamped}")
        
        return True
    except Exception as e:
        print(f"✗ 工具函数模块测试失败: {e}")
        return False


def test_data_structures():
    """测试数据结构"""
    print("\n=== 测试数据结构 ===")
    
    try:
        from traffic_utils import FrameBuffer, PerformanceMonitor
        import numpy as np
        
        # 测试帧缓冲区
        buffer = FrameBuffer(buffer_size=3)
        fake_frame = np.zeros((100, 100, 3), dtype=np.uint8)
        
        buffer.add_frame(fake_frame, 1)
        buffer.add_frame(fake_frame, 2)
        is_full = buffer.add_frame(fake_frame, 3)
        
        print(f"✓ 帧缓冲区: 大小={buffer.size()}, 已满={is_full}")
        
        # 获取批次数据
        frames, frame_ids = buffer.get_batch()
        print(f"✓ 批次数据: {len(frames)} 帧, IDs={frame_ids}")
        
        # 测试性能监控
        monitor = PerformanceMonitor()
        monitor.record_frame(0.05, 100.0)
        monitor.record_frame(0.03, 105.0)
        monitor.record_frame(0.04, 102.0)
        
        stats = monitor.get_stats()
        print(f"✓ 性能监控:")
        print(f"  平均FPS: {stats.get('avg_fps', 0):.1f}")
        print(f"  平均处理时间: {stats.get('avg_processing_time_ms', 0):.1f} ms")
        print(f"  处理帧数: {stats.get('frames_processed', 0)}")
        
        return True
    except Exception as e:
        print(f"✗ 数据结构测试失败: {e}")
        return False


def test_detection_data_class():
    """测试检测数据类"""
    print("\n=== 测试检测数据类 ===")
    
    try:
        from detector import Detection
        
        # 创建检测对象
        detection = Detection(
            bbox=(100, 100, 200, 200),
            confidence=0.85,
            class_id=0,
            class_name="car",
            center=(150, 150),
            area=10000
        )
        
        print(f"✓ 检测对象创建成功:")
        print(f"  类别: {detection.class_name}")
        print(f"  置信度: {detection.confidence}")
        print(f"  边界框: {detection.bbox}")
        print(f"  中心点: {detection.center}")
        print(f"  面积: {detection.area}")
        
        return True
    except Exception as e:
        print(f"✗ 检测数据类测试失败: {e}")
        return False


def demonstrate_config_flexibility():
    """演示配置系统的灵活性"""
    print("\n=== 演示配置系统灵活性 ===")
    
    try:
        from config import Config
        
        # 创建不同场景的配置
        configs = {
            "高精度模式": {
                "conf_threshold": 0.8,
                "iou_threshold": 0.3,
                "frame_skip": 1
            },
            "高速模式": {
                "conf_threshold": 0.5,
                "iou_threshold": 0.5,
                "frame_skip": 3
            },
            "平衡模式": {
                "conf_threshold": 0.6,
                "iou_threshold": 0.4,
                "frame_skip": 2
            }
        }
        
        for mode_name, settings in configs.items():
            config = Config()
            config.model.conf_threshold = settings["conf_threshold"]
            config.model.iou_threshold = settings["iou_threshold"]
            config.video.frame_skip = settings["frame_skip"]
            
            print(f"✓ {mode_name}:")
            print(f"  置信度阈值: {config.model.conf_threshold}")
            print(f"  IoU阈值: {config.model.iou_threshold}")
            print(f"  跳帧数: {config.video.frame_skip}")
        
        return True
    except Exception as e:
        print(f"✗ 配置灵活性演示失败: {e}")
        return False


def show_architecture_benefits():
    """展示新架构的优势"""
    print("\n=== 新架构优势展示 ===")
    
    print("🏗️ 模块化设计:")
    print("  ✓ 每个模块职责单一，易于理解和维护")
    print("  ✓ 模块间松耦合，便于独立测试和开发")
    print("  ✓ 支持插件式扩展，添加新功能无需修改现有代码")
    
    print("\n⚙️ 配置管理:")
    print("  ✓ 统一的配置接口，避免硬编码")
    print("  ✓ 类型安全，减少配置错误")
    print("  ✓ 支持配置验证，提前发现问题")
    
    print("\n🚀 性能优化:")
    print("  ✓ 内存管理优化，避免内存泄漏")
    print("  ✓ 批处理支持，提高GPU利用率")
    print("  ✓ 性能监控，实时了解系统状态")
    
    print("\n🛠️ 开发体验:")
    print("  ✓ 完整的类型注解，IDE支持更好")
    print("  ✓ 清晰的错误处理，便于调试")
    print("  ✓ 丰富的示例和文档，降低学习成本")


def main():
    """主函数"""
    print("🚀 交通监控系统重构成果展示")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置模块", test_config_module()))
    test_results.append(("工具函数模块", test_utils_module()))
    test_results.append(("数据结构", test_data_structures()))
    test_results.append(("检测数据类", test_detection_data_class()))
    test_results.append(("配置灵活性", demonstrate_config_flexibility()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("🧪 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    # 展示架构优势
    show_architecture_benefits()
    
    if passed == total:
        print("\n🎉 所有测试通过！重构后的代码结构工作正常。")
        print("💡 提示: 安装完整依赖后可运行完整功能版本")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查相关模块。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 重构成功！您现在拥有一个现代化、模块化的交通监控系统！")
    else:
        print("\n🔧 部分功能需要进一步调试。")
