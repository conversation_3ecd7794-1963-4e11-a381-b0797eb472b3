#!/usr/bin/env python3
"""
无人机交通警察系统 - 真实视频检测演示
处理action1.mp4视频并显示实时检测效果
"""

import sys
import cv2
import numpy as np
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QPushButton, QLabel, QTextEdit, 
                                QFrame, QGridLayout, QSlider, QSpinBox)
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
    from PyQt6.QtGui import QFont, QPixmap, QImage
    QT6_AVAILABLE = True
except ImportError:
    QT6_AVAILABLE = False
    print("Qt6未安装，请运行: pip install PyQt6")

import torch
from core.system import SystemManager

class VideoProcessor(QThread):
    """视频处理线程"""
    frame_ready = pyqtSignal(np.ndarray, list, dict)  # 帧数据, 检测结果, 统计信息
    status_update = pyqtSignal(str)
    
    def __init__(self, video_path):
        super().__init__()
        self.video_path = video_path
        self.is_running = False
        self.is_paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.system_manager = None
        
        # 检测统计
        self.total_detections = 0
        self.frame_count = 0
        
    def initialize_system(self):
        """初始化检测系统"""
        try:
            self.status_update.emit("正在初始化AI检测系统...")

            # 尝试加载YOLO模型
            try:
                # 检查是否有可用的YOLO模型文件
                model_paths = [
                    "yolov8s.pt",
                    "yolov8n.pt",
                    "yolov5s.pt",
                    "models/yolov8s.pt",
                    "models/yolov8n.pt"
                ]

                model_loaded = False
                for model_path in model_paths:
                    if Path(model_path).exists():
                        self.status_update.emit(f"正在加载YOLO模型: {model_path}")

                        # 尝试导入ultralytics
                        try:
                            from ultralytics import YOLO
                            self.yolo_model = YOLO(model_path)
                            self.status_update.emit(f"✅ YOLO模型加载成功: {model_path}")
                            model_loaded = True
                            break
                        except ImportError:
                            self.status_update.emit("⚠️ ultralytics未安装，尝试使用torch hub")
                            # 尝试使用torch hub加载YOLOv5
                            try:
                                self.yolo_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                                self.status_update.emit("✅ YOLOv5模型加载成功")
                                model_loaded = True
                                break
                            except Exception as e:
                                self.status_update.emit(f"⚠️ torch hub加载失败: {e}")

                if not model_loaded:
                    self.status_update.emit("⚠️ 未找到YOLO模型，将使用运动检测作为备选")

            except Exception as e:
                self.status_update.emit(f"⚠️ YOLO模型加载失败: {e}")
                self.status_update.emit("将使用运动检测作为备选方案")

            self.status_update.emit("✅ AI检测系统初始化完成")
            return True

        except Exception as e:
            self.status_update.emit(f"❌ 系统初始化失败: {e}")
            return False
    
    def load_video(self):
        """加载视频文件"""
        try:
            self.cap = cv2.VideoCapture(str(self.video_path))
            
            if not self.cap.isOpened():
                self.status_update.emit(f"❌ 无法打开视频文件: {self.video_path}")
                return False
            
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            self.status_update.emit(f"✅ 视频加载成功: {self.total_frames}帧, {self.fps:.1f}FPS")
            return True
            
        except Exception as e:
            self.status_update.emit(f"❌ 视频加载失败: {e}")
            return False
    
    def detect_objects(self, frame):
        """目标检测 - 集成YOLO模型"""
        detections = []

        try:
            # 尝试使用YOLO模型进行检测
            if hasattr(self, 'yolo_model'):
                results = self.yolo_model(frame)

                # 解析YOLO结果
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            # 获取边界框坐标
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = box.conf[0].cpu().numpy()
                            class_id = int(box.cls[0].cpu().numpy())

                            # COCO类别名称
                            class_names = ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck']
                            class_name = class_names[class_id] if class_id < len(class_names) else f'class_{class_id}'

                            # 只保留交通相关的目标
                            if class_name in ['person', 'bicycle', 'car', 'motorcycle', 'bus', 'truck'] and confidence > 0.5:
                                detections.append({
                                    'class': class_name,
                                    'confidence': float(confidence),
                                    'bbox': [int(x1), int(y1), int(x2-x1), int(y2-y1)],
                                    'area': int((x2-x1) * (y2-y1))
                                })

            # 如果YOLO不可用，使用运动检测作为备选
            if not detections and hasattr(self, 'prev_frame'):
                diff = cv2.absdiff(frame, self.prev_frame)
                gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
                _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)

                # 查找轮廓
                contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area > 500:  # 过滤小的运动区域
                        x, y, w, h = cv2.boundingRect(contour)

                        # 根据大小判断目标类型
                        if area > 2000:
                            obj_class = "car"
                            confidence = 0.85
                        else:
                            obj_class = "person"
                            confidence = 0.75

                        detections.append({
                            'class': obj_class,
                            'confidence': confidence,
                            'bbox': [x, y, w, h],
                            'area': area
                        })

            self.prev_frame = frame.copy()

        except Exception as e:
            self.status_update.emit(f"检测错误: {e}")

        return detections
    
    def draw_detections(self, frame, detections):
        """在帧上绘制检测结果"""
        annotated_frame = frame.copy()
        
        for det in detections:
            x, y, w, h = det['bbox']
            class_name = det['class']
            confidence = det['confidence']
            
            # 根据类别选择颜色
            if class_name == 'car':
                color = (0, 255, 0)  # 绿色
            elif class_name == 'person':
                color = (255, 0, 0)  # 蓝色
            else:
                color = (0, 255, 255)  # 黄色
            
            # 绘制边界框
            cv2.rectangle(annotated_frame, (x, y), (x + w, y + h), color, 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(annotated_frame, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            
            # 标签文字
            cv2.putText(annotated_frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return annotated_frame
    
    def run(self):
        """主处理循环"""
        if not self.initialize_system():
            return
        
        if not self.load_video():
            return
        
        self.is_running = True
        self.status_update.emit("🎬 开始视频检测...")
        
        while self.is_running and self.current_frame < self.total_frames:
            if self.is_paused:
                time.sleep(0.1)
                continue
            
            # 读取帧
            ret, frame = self.cap.read()
            if not ret:
                break
            
            self.frame_count += 1
            self.current_frame += 1
            
            # 目标检测
            detections = self.detect_objects(frame)
            self.total_detections += len(detections)
            
            # 绘制检测结果
            annotated_frame = self.draw_detections(frame, detections)
            
            # 统计信息
            stats = {
                'current_frame': self.current_frame,
                'total_frames': self.total_frames,
                'fps': self.fps,
                'detections_count': len(detections),
                'total_detections': self.total_detections,
                'progress': (self.current_frame / self.total_frames) * 100
            }
            
            # 发送结果
            self.frame_ready.emit(annotated_frame, detections, stats)
            
            # 控制播放速度
            time.sleep(1.0 / self.fps)
        
        self.cap.release()
        self.status_update.emit("✅ 视频处理完成")
    
    def pause(self):
        """暂停"""
        self.is_paused = True
    
    def resume(self):
        """继续"""
        self.is_paused = False
    
    def stop(self):
        """停止"""
        self.is_running = False
        if hasattr(self, 'cap'):
            self.cap.release()

class VideoDetectionDemo(QMainWindow):
    """视频检测演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.video_processor = None
        self.setup_ui()
        
        # 统计数据
        self.detection_history = []
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🚁 无人机交通警察系统 - 真实视频检测演示")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: palette(window);
            }
            QWidget {
                font-family: "Arial", sans-serif;
                color: palette(text);
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎬 action1.mp4 实时检测演示")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: palette(text); padding: 15px;")
        main_layout.addWidget(title)
        
        # 内容区域
        content_layout = QHBoxLayout()
        
        # 左侧控制面板
        self.create_control_panel(content_layout)
        
        # 中央视频显示
        self.create_video_display(content_layout)
        
        # 右侧信息面板
        self.create_info_panel(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # 状态栏
        self.statusBar().showMessage("系统就绪 - 点击'开始检测'处理视频")
    
    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFixedWidth(300)
        control_frame.setStyleSheet("""
            QFrame {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(control_frame)
        
        # 控制按钮
        self.start_btn = QPushButton("🎬 开始检测")
        self.pause_btn = QPushButton("⏸️ 暂停")
        self.resume_btn = QPushButton("▶️ 继续")
        self.stop_btn = QPushButton("⏹️ 停止")
        
        # 设置按钮样式
        for btn in [self.start_btn, self.pause_btn, self.resume_btn, self.stop_btn]:
            btn.setMinimumHeight(45)
            btn.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
                QPushButton:disabled {
                    background-color: #BDBDBD;
                    color: #757575;
                }
            """)
        
        # 初始状态
        self.pause_btn.setEnabled(False)
        self.resume_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        layout.addWidget(QLabel("🎮 视频控制"))
        layout.addWidget(self.start_btn)
        layout.addWidget(self.pause_btn)
        layout.addWidget(self.resume_btn)
        layout.addWidget(self.stop_btn)
        
        # 进度显示
        layout.addWidget(QLabel("📊 播放进度"))
        self.progress_label = QLabel("0 / 0 帧 (0.0%)")
        layout.addWidget(self.progress_label)
        
        # 统计信息
        layout.addWidget(QLabel("📈 检测统计"))
        self.stats_label = QLabel("等待开始...")
        self.stats_label.setStyleSheet("padding: 10px; background-color: palette(alternate-base); border-radius: 5px;")
        layout.addWidget(self.stats_label)
        
        layout.addStretch()
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_detection)
        self.pause_btn.clicked.connect(self.pause_detection)
        self.resume_btn.clicked.connect(self.resume_detection)
        self.stop_btn.clicked.connect(self.stop_detection)
        
        parent_layout.addWidget(control_frame)
    
    def create_video_display(self, parent_layout):
        """创建视频显示区域"""
        video_frame = QFrame()
        video_frame.setStyleSheet("""
            QFrame {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(video_frame)
        
        # 视频显示标签
        self.video_label = QLabel()
        self.video_label.setMinimumSize(800, 600)
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #000000;
                border: 1px solid palette(mid);
                border-radius: 5px;
                color: white;
                font-size: 16px;
            }
        """)
        self.video_label.setText("等待视频加载...")
        
        layout.addWidget(self.video_label)
        parent_layout.addWidget(video_frame)
    
    def create_info_panel(self, parent_layout):
        """创建信息面板"""
        info_frame = QFrame()
        info_frame.setFixedWidth(350)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        layout = QVBoxLayout(info_frame)
        
        # 检测结果
        layout.addWidget(QLabel("🎯 实时检测结果"))
        self.detection_text = QTextEdit()
        self.detection_text.setMaximumHeight(200)
        self.detection_text.setStyleSheet("""
            QTextEdit {
                background-color: palette(base);
                border: 1px solid palette(mid);
                border-radius: 5px;
                font-family: "Courier New", monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.detection_text)
        
        # 系统日志
        layout.addWidget(QLabel("📝 系统日志"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: palette(base);
                border: 1px solid palette(mid);
                border-radius: 5px;
                font-family: "Courier New", monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 视频信息
        layout.addWidget(QLabel("📹 视频信息"))
        self.video_info_label = QLabel("文件: action1.mp4\n等待加载...")
        self.video_info_label.setStyleSheet("padding: 10px; background-color: palette(alternate-base); border-radius: 5px;")
        layout.addWidget(self.video_info_label)
        
        parent_layout.addWidget(info_frame)
    
    def start_detection(self):
        """开始检测"""
        video_path = Path("data/videos/action1.mp4")
        
        if not video_path.exists():
            self.log_message("❌ 找不到视频文件: data/videos/action1.mp4")
            return
        
        self.video_processor = VideoProcessor(video_path)
        self.video_processor.frame_ready.connect(self.update_frame)
        self.video_processor.status_update.connect(self.log_message)
        
        self.video_processor.start()
        
        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        self.stop_btn.setEnabled(True)
        
        self.log_message("🚀 开始处理视频...")
    
    def pause_detection(self):
        """暂停检测"""
        if self.video_processor:
            self.video_processor.pause()
            self.pause_btn.setEnabled(False)
            self.resume_btn.setEnabled(True)
            self.log_message("⏸️ 检测已暂停")
    
    def resume_detection(self):
        """继续检测"""
        if self.video_processor:
            self.video_processor.resume()
            self.pause_btn.setEnabled(True)
            self.resume_btn.setEnabled(False)
            self.log_message("▶️ 检测已继续")
    
    def stop_detection(self):
        """停止检测"""
        if self.video_processor:
            self.video_processor.stop()
            self.video_processor.wait()
        
        # 重置按钮状态
        self.start_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        self.resume_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        
        self.log_message("⏹️ 检测已停止")
    
    def update_frame(self, frame, detections, stats):
        """更新显示帧"""
        # 转换OpenCV图像为Qt图像
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
        
        # 缩放图像适应显示区域
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(self.video_label.size(), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
        self.video_label.setPixmap(scaled_pixmap)
        
        # 更新进度
        progress_text = f"{stats['current_frame']} / {stats['total_frames']} 帧 ({stats['progress']:.1f}%)"
        self.progress_label.setText(progress_text)
        
        # 更新统计
        stats_text = f"""当前帧检测: {stats['detections_count']} 个目标
总检测数: {stats['total_detections']} 个
视频FPS: {stats['fps']:.1f}
处理进度: {stats['progress']:.1f}%"""
        self.stats_label.setText(stats_text)
        
        # 更新检测结果
        if detections:
            timestamp = time.strftime("%H:%M:%S")
            detection_text = f"[{timestamp}] 帧 {stats['current_frame']:04d}:\n"
            for det in detections:
                detection_text += f"  - {det['class']}: {det['confidence']:.2f}\n"
            self.detection_text.append(detection_text)
        
        # 更新视频信息
        video_info = f"""文件: action1.mp4
分辨率: {width}x{height}
总帧数: {stats['total_frames']}
帧率: {stats['fps']:.1f} FPS
当前帧: {stats['current_frame']}"""
        self.video_info_label.setText(video_info)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        self.statusBar().showMessage(message)

def main():
    """主函数"""
    if not QT6_AVAILABLE:
        print("❌ Qt6未安装")
        print("请运行: pip install PyQt6")
        return
    
    # 检查视频文件
    video_path = Path("data/videos/action1.mp4")
    if not video_path.exists():
        print(f"❌ 找不到视频文件: {video_path}")
        print("请确保action1.mp4文件在data/videos/目录中")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("无人机交通警察系统 - 视频检测演示")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = VideoDetectionDemo()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
