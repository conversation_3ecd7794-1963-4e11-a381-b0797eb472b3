"""
中文字体显示测试脚本
测试重构后系统的中文显示功能
"""
import cv2
import numpy as np
import sys
import os

def test_matplotlib_chinese():
    """测试matplotlib中文显示"""
    print("=== 测试Matplotlib中文显示 ===")
    
    try:
        import matplotlib.pyplot as plt
        from traffic_utils import setup_chinese_font
        
        # 设置中文字体
        setup_chinese_font()
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试数据
        x = range(10)
        vehicle_data = [2, 3, 5, 4, 6, 7, 5, 8, 6, 4]
        person_data = [1, 2, 1, 3, 2, 3, 4, 2, 3, 2]
        
        # 绘制图表
        ax.plot(x, vehicle_data, 'r-', label='车辆密度', linewidth=2)
        ax.plot(x, person_data, 'b-', label='行人密度', linewidth=2)
        
        # 设置标签和标题
        ax.set_xlabel('时间')
        ax.set_ylabel('密度 (个/m²)')
        ax.set_title('交通密度监控图表')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图片
        plt.savefig('chinese_font_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ Matplotlib中文显示测试成功")
        print("  📁 已保存测试图片: chinese_font_test.png")
        return True
        
    except Exception as e:
        print(f"❌ Matplotlib中文显示测试失败: {e}")
        return False


def test_opencv_chinese():
    """测试OpenCV中文显示"""
    print("\n=== 测试OpenCV中文显示 ===")
    
    try:
        from traffic_utils import put_chinese_text
        
        # 创建测试图像
        img = np.ones((400, 600, 3), dtype=np.uint8) * 255
        
        # 测试中文文本
        test_texts = [
            ("交通监控系统", (50, 50), (0, 0, 255)),
            ("车辆检测: 5辆", (50, 100), (0, 255, 0)),
            ("行人检测: 3人", (50, 150), (255, 0, 0)),
            ("密度: 0.025/m²", (50, 200), (0, 255, 255)),
            ("状态: 正常运行", (50, 250), (255, 0, 255)),
            ("⚠️ 检测到碰撞事故!", (50, 300), (0, 0, 255))
        ]
        
        # 绘制测试文本
        for text, pos, color in test_texts:
            img = put_chinese_text(img, text, pos, font_scale=0.8, 
                                 color=color, thickness=2)
        
        # 添加英文对比
        cv2.putText(img, "English Text (OpenCV default)", (50, 350), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # 保存测试图像
        cv2.imwrite('opencv_chinese_test.png', img)
        
        print("✅ OpenCV中文显示测试成功")
        print("  📁 已保存测试图片: opencv_chinese_test.png")
        return True
        
    except Exception as e:
        print(f"❌ OpenCV中文显示测试失败: {e}")
        return False


def test_visualization_module():
    """测试可视化模块的中文显示"""
    print("\n=== 测试可视化模块中文显示 ===")
    
    try:
        from visualization import Visualizer
        from config import default_config
        from detector import Detection
        from data_manager import FrameData
        
        # 创建可视化器
        visualizer = Visualizer(default_config)
        
        # 创建测试帧
        test_frame = np.ones((480, 640, 3), dtype=np.uint8) * 50
        
        # 创建模拟检测结果
        vehicles = [
            Detection(
                bbox=(100, 100, 200, 200),
                confidence=0.85,
                class_id=0,
                class_name="car",
                center=(150, 150),
                area=10000
            ),
            Detection(
                bbox=(300, 150, 400, 250),
                confidence=0.92,
                class_id=1,
                class_name="truck",
                center=(350, 200),
                area=10000
            )
        ]
        
        persons = [
            Detection(
                bbox=(50, 300, 80, 380),
                confidence=0.78,
                class_id=2,
                class_name="person",
                center=(65, 340),
                area=2400
            )
        ]
        
        # 创建模拟帧数据
        frame_data = FrameData(
            frame_id=123,
            timestamp=1234567890.0,
            vehicle_count=2,
            person_count=1,
            vehicle_density=0.002,
            person_density=0.001,
            vehicles=vehicles,
            persons=persons
        )
        
        # 创建模拟统计信息
        statistics = {
            'estimated_area_m2': 1000.0,
            'processing_fps': 15.3,
            'avg_vehicle_count': 2.5,
            'avg_person_count': 1.2
        }
        
        # 创建模拟事故信息
        accident_info = {
            'detected': True,
            'type': 'collision',
            'confidence': 0.85,
            'description': '检测到车辆碰撞'
        }
        
        # 绘制检测结果
        result_frame = visualizer.draw_detections(test_frame, vehicles, persons)
        
        # 绘制统计信息
        result_frame = visualizer.draw_statistics(result_frame, frame_data, 
                                                statistics, accident_info)
        
        # 保存结果
        cv2.imwrite('visualization_chinese_test.png', result_frame)
        
        # 测试密度图
        vehicle_densities = [0.001 * i + 0.0005 * np.sin(i * 0.1) for i in range(50)]
        person_densities = [0.0005 * i + 0.0002 * np.cos(i * 0.15) for i in range(50)]
        
        plot_img = visualizer.draw_density_plot(vehicle_densities, person_densities)
        cv2.imwrite('density_plot_chinese_test.png', plot_img)
        
        print("✅ 可视化模块中文显示测试成功")
        print("  📁 已保存测试图片: visualization_chinese_test.png")
        print("  📁 已保存密度图: density_plot_chinese_test.png")
        return True
        
    except Exception as e:
        print(f"❌ 可视化模块中文显示测试失败: {e}")
        return False


def check_font_availability():
    """检查系统字体可用性"""
    print("\n=== 检查系统字体可用性 ===")
    
    # 检查字体文件路径
    font_paths = [
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "/System/Library/Fonts/Arial.ttf", 
        "/Windows/Fonts/simhei.ttf",
        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
        "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
    ]
    
    available_fonts = []
    for font_path in font_paths:
        if os.path.exists(font_path):
            print(f"✅ 找到字体: {font_path}")
            available_fonts.append(font_path)
        else:
            print(f"❌ 字体不存在: {font_path}")
    
    if available_fonts:
        print(f"\n📊 总计找到 {len(available_fonts)} 个可用字体文件")
    else:
        print("\n⚠️  未找到任何字体文件，将使用默认字体")
    
    # 检查PIL是否可用
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL库可用，支持高质量中文渲染")
    except ImportError:
        print("❌ PIL库不可用，将使用OpenCV基础文本渲染")
    
    return len(available_fonts) > 0


def generate_font_installation_guide():
    """生成字体安装指南"""
    print("\n=== 字体安装建议 ===")
    
    print("如果中文显示有问题，可以尝试以下解决方案：")
    print()
    print("1. 安装PIL库（推荐）:")
    print("   conda install pillow")
    print("   # 或")
    print("   pip install pillow")
    print()
    print("2. 安装中文字体（Ubuntu/Debian）:")
    print("   sudo apt-get install fonts-noto-cjk")
    print("   sudo apt-get install fonts-wqy-microhei")
    print()
    print("3. 安装中文字体（CentOS/RHEL）:")
    print("   sudo yum install google-noto-cjk-fonts")
    print("   sudo yum install wqy-microhei-fonts")
    print()
    print("4. 手动下载字体文件:")
    print("   - 下载 SimHei.ttf 或其他中文字体")
    print("   - 放置到 /usr/share/fonts/truetype/ 目录")
    print("   - 运行 sudo fc-cache -fv 刷新字体缓存")


def main():
    """主函数"""
    print("🔤 中文字体显示测试")
    print("=" * 50)
    
    # 检查字体可用性
    font_available = check_font_availability()
    
    # 运行测试
    test_results = []
    test_results.append(("Matplotlib中文显示", test_matplotlib_chinese()))
    test_results.append(("OpenCV中文显示", test_opencv_chinese()))
    test_results.append(("可视化模块中文显示", test_visualization_module()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("🧪 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有中文显示测试通过！")
        print("💡 您的系统已正确配置中文字体支持")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败")
        generate_font_installation_guide()
    
    # 列出生成的测试图片
    test_images = [
        'chinese_font_test.png',
        'opencv_chinese_test.png', 
        'visualization_chinese_test.png',
        'density_plot_chinese_test.png'
    ]
    
    print(f"\n📁 生成的测试图片:")
    for img in test_images:
        if os.path.exists(img):
            print(f"  ✅ {img}")
        else:
            print(f"  ❌ {img} (未生成)")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
