#!/usr/bin/env python3
"""
Qt6界面测试脚本
测试系统主题兼容性和按钮显示
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_qt6_availability():
    """测试Qt6是否可用"""
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6可用")
        return True
    except ImportError:
        print("❌ PyQt6不可用，请安装: pip install PyQt6")
        return False

def test_system_theme():
    """测试系统主题兼容性"""
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        app = QApplication([])
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("Qt6系统主题测试")
        window.setGeometry(200, 200, 400, 300)
        
        layout = QVBoxLayout()
        
        # 测试标签
        label = QLabel("系统主题兼容性测试")
        label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: palette(text); padding: 20px;")
        layout.addWidget(label)
        
        # 测试按钮
        button = QPushButton("测试按钮")
        button.setMinimumHeight(50)
        button.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        button.setStyleSheet("""
            QPushButton {
                background-color: palette(button);
                color: palette(button-text);
                border: 2px solid palette(mid);
                border-radius: 10px;
                padding: 12px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: palette(alternate-base);
                border: 2px solid palette(highlight);
            }
            QPushButton:pressed {
                background-color: palette(dark);
            }
        """)
        layout.addWidget(button)
        
        # 系统信息
        info_label = QLabel()
        info_text = f"""
系统信息:
- 应用名称: {app.applicationName() or '未设置'}
- 系统主题: {'深色' if app.palette().color(app.palette().ColorRole.Window).lightness() < 128 else '浅色'}
- 字体: {app.font().family()}
        """
        info_label.setText(info_text.strip())
        info_label.setStyleSheet("color: palette(text); padding: 10px; background-color: palette(base); border: 1px solid palette(mid); border-radius: 5px;")
        layout.addWidget(info_label)
        
        window.setLayout(layout)
        window.setStyleSheet("background-color: palette(window);")
        
        print("✅ 系统主题测试窗口创建成功")
        print("💡 窗口将显示3秒后自动关闭")
        
        window.show()
        
        # 显示3秒后关闭
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3秒
        
        app.exec()
        print("✅ 系统主题测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统主题测试失败: {e}")
        return False

def test_button_sizes():
    """测试按钮大小和文字显示"""
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton
        from PyQt6.QtGui import QFont
        
        app = QApplication([])
        
        window = QWidget()
        window.setWindowTitle("按钮大小测试")
        window.setGeometry(300, 300, 500, 400)
        
        layout = QVBoxLayout()
        
        # 测试不同大小的按钮
        buttons = [
            ("🚀 初始化系统", "success"),
            ("⏹️ 停止系统", "danger"),
            ("🎭 开始模拟演示", "primary"),
            ("⏸️ 停止演示", "warning"),
        ]
        
        for text, color in buttons:
            button = QPushButton(text)
            button.setMinimumHeight(50)
            button.setMinimumWidth(200)
            button.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            
            colors = {
                "primary": "#2196F3",
                "success": "#4CAF50", 
                "danger": "#F44336",
                "warning": "#FF9800",
            }
            
            base_color = colors.get(color, colors["primary"])
            
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {base_color};
                    color: white;
                    border: 2px solid {base_color};
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                }}
                QPushButton:pressed {{
                    background-color: rgba(0, 0, 0, 0.1);
                }}
            """)
            
            layout.addWidget(button)
        
        window.setLayout(layout)
        window.setStyleSheet("background-color: palette(window); padding: 20px;")
        
        print("✅ 按钮大小测试窗口创建成功")
        print("💡 窗口将显示3秒后自动关闭")
        
        window.show()
        
        # 显示3秒后关闭
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3秒
        
        app.exec()
        print("✅ 按钮大小测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 按钮大小测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 Qt6界面兼容性测试")
    print("=" * 50)
    
    # 测试Qt6可用性
    if not test_qt6_availability():
        return False
    
    print("\n🔍 测试系统主题兼容性...")
    theme_test = test_system_theme()
    
    print("\n🔍 测试按钮大小和显示...")
    button_test = test_button_sizes()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"系统主题兼容性: {'✅ 通过' if theme_test else '❌ 失败'}")
    print(f"按钮大小显示: {'✅ 通过' if button_test else '❌ 失败'}")
    
    if theme_test and button_test:
        print("\n🎉 所有测试通过！界面兼容性良好！")
        print("\n💡 现在可以启动改进后的Qt6演示界面:")
        print("   python demo_qt6.py")
    else:
        print("\n❌ 部分测试失败，请检查Qt6安装")
    
    return theme_test and button_test

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
