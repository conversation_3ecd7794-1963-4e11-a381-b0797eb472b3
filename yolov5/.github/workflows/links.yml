# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Continuous Integration (CI) GitHub Actions tests broken link checker using https://github.com/lycheeverse/lychee
# Ignores the following status codes to reduce false positives:
#   - 403(OpenVINO, 'forbidden')
#   - 429(Instagram, 'too many requests')
#   - 500(<PERSON><PERSON><PERSON>, 'cached')
#   - 502(<PERSON><PERSON><PERSON>, 'bad gateway')
#   - 999(LinkedIn, 'unknown status code')

name: Check Broken links

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *" # runs at 00:00 UTC every day

jobs:
  Links:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Download and install lychee
        run: |
          LYCHEE_URL=$(curl -s https://api.github.com/repos/lycheeverse/lychee/releases/latest | grep "browser_download_url" | grep "x86_64-unknown-linux-gnu.tar.gz" | cut -d '"' -f 4)
          curl -L $LYCHEE_URL | tar xz -C /usr/local/bin

      - name: Test Markdown and HTML links with retry
        uses: ultralytics/actions/retry@main
        with:
          timeout_minutes: 5
          retry_delay_seconds: 60
          retries: 2
          run: |
            lychee \
            --scheme 'https' \
            --timeout 60 \
            --insecure \
            --accept 100..=103,200..=299,401,403,429,500,502,999 \
            --exclude-all-private \
            --exclude 'https?://(www\.)?(linkedin\.com|twitter\.com|x\.com|instagram\.com|kaggle\.com|fonts\.gstatic\.com|url\.com)' \
            --exclude-path '**/ci.yaml' \
            --github-token ${{ secrets.GITHUB_TOKEN }} \
            --header "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.183 Safari/537.36" \
            './**/*.md' \
            './**/*.html' | tee -a $GITHUB_STEP_SUMMARY

            # Raise error if broken links found
            if ! grep -q "0 Errors" $GITHUB_STEP_SUMMARY; then
              exit 1
            fi

      - name: Test Markdown, HTML, YAML, Python and Notebook links with retry
        if: github.event_name == 'workflow_dispatch'
        uses: ultralytics/actions/retry@main
        with:
          timeout_minutes: 5
          retry_delay_seconds: 60
          retries: 2
          run: |
            lychee \
            --scheme 'https' \
            --timeout 60 \
            --insecure \
            --accept 100..=103,200..=299,429,999 \
            --exclude-all-private \
            --exclude 'https?://(www\.)?(linkedin\.com|twitter\.com|x\.com|instagram\.com|kaggle\.com|fonts\.gstatic\.com|url\.com)' \
            --exclude-path '**/ci.yaml' \
            --github-token ${{ secrets.GITHUB_TOKEN }} \
            --header "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.183 Safari/537.36" \
            './**/*.md' \
            './**/*.html' \
            './**/*.yml' \
            './**/*.yaml' \
            './**/*.py' \
            './**/*.ipynb' | tee -a $GITHUB_STEP_SUMMARY

            # Raise error if broken links found
            if ! grep -q "0 Errors" $GITHUB_STEP_SUMMARY; then
              exit 1
            fi
