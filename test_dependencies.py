#!/usr/bin/env python3
"""
测试依赖检查功能
"""

def test_dependency_check():
    """测试依赖检查功能"""
    print("🔍 测试依赖检查功能...")
    
    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'torch': 'torch',
        'torchvision': 'torchvision', 
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'fastapi': 'fastapi',
        'uvicorn': 'uvicorn',
        'sqlalchemy': 'sqlalchemy',
        'pydantic': 'pydantic',
        'pydantic-settings': 'pydantic_settings'
    }
    
    missing_packages = []
    
    for pip_name, import_name in package_mapping.items():
        try:
            module = __import__(import_name)
            # 特殊检查opencv版本
            if import_name == 'cv2':
                version = getattr(module, '__version__', 'unknown')
                print(f"  ✅ {pip_name} (版本: {version})")
            else:
                version = getattr(module, '__version__', 'unknown')
                print(f"  ✅ {pip_name} (版本: {version})")
        except ImportError as e:
            print(f"  ❌ {pip_name} (缺失: {e})")
            missing_packages.append(pip_name)
        except Exception as e:
            print(f"  ⚠️  {pip_name} (导入异常: {e})")
    
    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖包: {', '.join(missing_packages)}")
        print("   请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True


def test_config_import():
    """测试配置导入"""
    print("\n🔍 测试配置导入...")
    
    try:
        from config.settings import Settings
        settings = Settings()
        print("✅ 配置导入成功")
        print(f"   项目名称: {settings.PROJECT_NAME}")
        print(f"   项目版本: {settings.PROJECT_VERSION}")
        return True
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试...")
    
    # 测试依赖
    deps_ok = test_dependency_check()
    
    # 测试配置
    config_ok = test_config_import()
    
    print("\n📊 测试结果:")
    print(f"  依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"  配置导入: {'✅ 通过' if config_ok else '❌ 失败'}")
    
    if deps_ok and config_ok:
        print("\n🎉 所有测试通过！系统可以正常启动。")
    else:
        print("\n⚠️  存在问题，请检查上述错误信息。")
