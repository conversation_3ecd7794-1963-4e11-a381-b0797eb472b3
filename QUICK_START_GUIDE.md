# 🚀 无人机交通警察系统 - 快速开始指南

## 📋 **5分钟快速部署**

### **前提条件**
- ✅ Ubuntu 20.04+ / macOS 10.15+ / Windows 10+
- ✅ Python 3.8+
- ✅ 8GB+ 内存
- ✅ 20GB+ 可用磁盘空间
- ✅ NVIDIA GPU (推荐，可选)

---

## 🎯 **方法1: 一键部署脚本 (推荐)**

### **步骤1: 下载项目**
```bash
git clone https://github.com/your-repo/air-traffic-police.git
cd air-traffic-police
```

### **步骤2: 运行部署脚本**
```bash
# 开发环境部署
chmod +x scripts/deploy.sh
./scripts/deploy.sh dev

# 生产环境部署
./scripts/deploy.sh prod
```

### **步骤3: 访问系统**
- 🌐 **Web界面**: http://localhost:8080
- 📡 **API文档**: http://localhost:8000/docs
- 🖥️ **桌面应用**: `python demo_qt6.py`

---

## 🐳 **方法2: Docker 部署**

### **步骤1: 安装Docker**
```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### **步骤2: 启动服务**
```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### **步骤3: 验证部署**
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f traffic-police
```

---

## 💻 **方法3: 手动部署**

### **步骤1: 环境准备**
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows

# 安装依赖
pip install --upgrade pip
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

### **步骤2: 配置环境变量**
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### **步骤3: 启动系统**
```bash
# 启动主程序
python start.py

# 或直接启动特定模式
python demo_qt6.py          # Qt6界面
python demo_interactive.py  # 交互式命令行
python demo_web.py          # Web界面
```

---

## 🔧 **配置指南**

### **1. 无人机配置**

#### **DJI 无人机设置**
```python
# config/drone_config.py
DJI_CONFIG = {
    "app_key": "your_dji_app_key",        # 从DJI开发者平台获取
    "app_secret": "your_dji_app_secret",  # 从DJI开发者平台获取
    "connection": {
        "type": "wifi",  # wifi, usb, lightbridge
        "timeout": 30
    }
}
```

#### **获取DJI开发者密钥**
1. 访问 [DJI开发者平台](https://developer.dji.com/)
2. 注册开发者账号
3. 创建应用获取App Key和App Secret
4. 将密钥填入配置文件

### **2. 大模型配置**

#### **OpenAI GPT配置**
```bash
# 设置环境变量
export OPENAI_API_KEY="sk-your-openai-api-key"

# 或在.env文件中设置
echo "OPENAI_API_KEY=sk-your-openai-api-key" >> .env
```

#### **本地模型配置**
```bash
# 下载LLaMA 2模型
mkdir -p models/llm
wget -O models/llm/llama-2-7b-chat.q4_0.bin \
  https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGML/resolve/main/llama-2-7b-chat.q4_0.bin
```

### **3. 数据库配置**

#### **PostgreSQL设置**
```bash
# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 创建数据库
sudo -u postgres psql
CREATE DATABASE traffic_police;
CREATE USER traffic_admin WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE traffic_police TO traffic_admin;
\q
```

#### **Redis设置**
```bash
# 安装Redis
sudo apt install redis-server

# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis
```

---

## 🎮 **使用指南**

### **1. 启动系统**
```bash
# 使用启动器
python start.py

# 选择演示模式:
# 1. 💬 交互式演示
# 2. 🖥️ 图形界面演示  
# 3. 🎬 视频检测演示 (处理action1.mp4)
# 4. 🌐 Web界面演示
```

### **2. 视频检测演示**
```bash
# 直接启动视频检测
python demo_video_detection.py

# 操作步骤:
# 1. 点击"🎬 开始检测"
# 2. 观察实时检测效果
# 3. 使用暂停/继续控制播放
```

### **3. Web界面使用**
1. 访问 http://localhost:8080
2. 点击"🚀 启动系统"
3. 点击"▶️ 开始演示"
4. 观察实时检测结果

### **4. API调用示例**
```python
import requests

# 获取系统状态
response = requests.get("http://localhost:8000/api/status")
print(response.json())

# 上传视频进行检测
files = {"file": open("video.mp4", "rb")}
response = requests.post("http://localhost:8000/api/detect", files=files)
print(response.json())
```

---

## 🔍 **故障排除**

### **常见问题**

#### **1. 无人机连接失败**
```bash
# 检查无人机连接
python -c "
from djitellopy import Tello
drone = Tello()
try:
    drone.connect()
    print('连接成功')
except Exception as e:
    print(f'连接失败: {e}')
"
```

#### **2. GPU内存不足**
```python
# 在代码中添加
import torch
torch.cuda.empty_cache()

# 或使用CPU模式
export CUDA_VISIBLE_DEVICES=""
```

#### **3. 模型下载失败**
```bash
# 手动下载YOLO模型
wget -O models/yolov8s.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
wget -O models/yolov8n.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
```

#### **4. 端口被占用**
```bash
# 查看端口占用
sudo netstat -tulpn | grep :8000
sudo netstat -tulpn | grep :8080

# 杀死占用进程
sudo kill -9 <PID>
```

### **日志查看**
```bash
# 查看应用日志
tail -f logs/traffic_police.log

# Docker日志
docker-compose logs -f traffic-police

# 系统服务日志
sudo journalctl -u traffic-police -f
```

---

## 📊 **性能优化**

### **1. GPU加速**
```bash
# 检查CUDA可用性
python -c "import torch; print(torch.cuda.is_available())"

# 设置GPU设备
export CUDA_VISIBLE_DEVICES=0
```

### **2. 内存优化**
```python
# 在配置中设置
BATCH_SIZE = 1  # 减小批处理大小
MAX_WORKERS = 2  # 减少工作进程数
```

### **3. 模型优化**
```bash
# 使用轻量级模型
# YOLOv8n (6.2MB) 替代 YOLOv8s (21.5MB)
```

---

## 🎯 **下一步**

### **开发建议**
1. 📖 阅读 [完整部署教程](DEPLOYMENT_TUTORIAL.md)
2. 🔧 查看 [API文档](http://localhost:8000/docs)
3. 🎬 体验 [视频检测功能](VIDEO_DETECTION_GUIDE.md)
4. 🚁 配置真实无人机连接
5. 🤖 集成自定义大模型

### **生产部署**
1. 🔒 配置HTTPS和安全认证
2. 📊 设置监控和告警
3. 💾 配置数据备份策略
4. 🔄 设置CI/CD流水线
5. 📈 性能调优和扩容

---

## 📞 **获取帮助**

- 📚 **文档**: [完整文档](README.md)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- 💬 **技术交流**: 微信群 (扫码加入)
- 📧 **邮件支持**: <EMAIL>

---

**🎉 恭喜！您已成功部署无人机交通警察系统！**

**立即体验**: `python start.py` → 选择您喜欢的演示模式

**祝您使用愉快！** ✨🚁
