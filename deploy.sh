#!/bin/bash

# 🚁 无人机交通警察系统 - 自动部署脚本
# 作者: AI Assistant
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_success "检测到Linux系统"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_success "检测到macOS系统"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
        log_success "检测到Python版本: $PYTHON_VERSION"
        
        if [[ $(echo "$PYTHON_VERSION >= 3.8" | bc -l) -eq 0 ]]; then
            log_error "Python版本过低，需要3.8或更高版本"
            exit 1
        fi
    else
        log_error "未找到Python3，请先安装Python"
        exit 1
    fi
    
    # 检查内存
    if [[ "$OS" == "linux" ]]; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    else
        MEMORY_GB=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    fi
    
    if [[ $MEMORY_GB -lt 8 ]]; then
        log_warning "内存不足8GB，可能影响性能"
    else
        log_success "内存检查通过: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $DISK_SPACE -lt 20 ]]; then
        log_error "磁盘空间不足，需要至少20GB可用空间"
        exit 1
    else
        log_success "磁盘空间检查通过: ${DISK_SPACE}GB可用"
    fi
}

# 安装Conda
install_conda() {
    if command -v conda &> /dev/null; then
        log_success "Conda已安装"
        return
    fi
    
    log_info "安装Miniconda..."
    
    if [[ "$OS" == "linux" ]]; then
        CONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    else
        CONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh"
    fi
    
    wget -O miniconda.sh $CONDA_URL
    bash miniconda.sh -b -p $HOME/miniconda3
    rm miniconda.sh
    
    # 添加到PATH
    echo 'export PATH="$HOME/miniconda3/bin:$PATH"' >> ~/.bashrc
    source ~/.bashrc
    
    log_success "Miniconda安装完成"
}

# 创建虚拟环境
create_environment() {
    log_info "创建Python虚拟环境..."
    
    # 检查环境是否已存在
    if conda env list | grep -q "yolov5"; then
        log_warning "环境yolov5已存在，是否重新创建? (y/n)"
        read -r response
        if [[ "$response" == "y" ]]; then
            conda env remove -n yolov5 -y
        else
            log_info "使用现有环境"
            return
        fi
    fi
    
    conda create -n yolov5 python=3.9 -y
    log_success "虚拟环境创建完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 激活环境
    source $HOME/miniconda3/etc/profile.d/conda.sh
    conda activate yolov5
    
    # 检查GPU支持
    if command -v nvidia-smi &> /dev/null; then
        log_info "检测到NVIDIA GPU，安装GPU版本PyTorch..."
        conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
        
        # 验证GPU支持
        python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
    else
        log_info "未检测到NVIDIA GPU，安装CPU版本PyTorch..."
        conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
    fi
    
    # 安装其他依赖
    pip install ultralytics>=8.0.0
    pip install opencv-python>=4.8.0
    pip install fastapi>=0.104.0
    pip install uvicorn>=0.24.0
    pip install websockets>=12.0
    pip install pydantic>=2.5.0
    pip install pydantic-settings>=2.1.0
    pip install sqlalchemy>=2.0.0
    pip install aiosqlite>=0.19.0
    pip install numpy>=1.21.0
    pip install pillow>=8.3.0
    pip install requests>=2.28.0
    pip install python-multipart>=0.0.5
    pip install jinja2>=3.1.0
    
    # 安装requirements.txt中的依赖（如果存在）
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
    fi
    
    log_success "依赖安装完成"
}

# 下载模型文件
download_models() {
    log_info "下载YOLO模型文件..."
    
    mkdir -p models
    cd models
    
    # 下载YOLOv8模型
    if [[ ! -f "yolov8s.pt" ]]; then
        log_info "下载YOLOv8s模型..."
        wget -q https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
    fi
    
    if [[ ! -f "yolov8m.pt" ]]; then
        log_info "下载YOLOv8m模型..."
        wget -q https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8m.pt
    fi
    
    cd ..
    log_success "模型文件下载完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p data/videos
    mkdir -p data/images
    mkdir -p logs
    mkdir -p temp
    mkdir -p output
    
    log_success "目录结构创建完成"
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    # 激活环境
    source $HOME/miniconda3/etc/profile.d/conda.sh
    conda activate yolov5
    
    # 运行测试
    if python test_final.py; then
        log_success "系统测试通过"
    else
        log_error "系统测试失败，请检查配置"
        exit 1
    fi
}

# 创建启动脚本
create_startup_scripts() {
    log_info "创建启动脚本..."
    
    # 创建启动脚本
    cat > start_system.sh << 'EOF'
#!/bin/bash
# 无人机交通警察系统启动脚本

source $HOME/miniconda3/etc/profile.d/conda.sh
conda activate yolov5

echo "🚁 启动无人机交通警察系统..."
echo "请选择启动模式:"
echo "1. 演示模式 (Demo Mode)"
echo "2. API服务模式 (API Mode)"
echo "3. 完整模式 (Full Mode)"
echo "4. 系统测试 (Test Mode)"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "启动演示模式..."
        python main.py --mode demo
        ;;
    2)
        echo "启动API服务模式..."
        python main.py --mode api
        ;;
    3)
        echo "启动完整模式..."
        python main.py --mode full
        ;;
    4)
        echo "运行系统测试..."
        python test_final.py
        ;;
    *)
        echo "无效选择，启动演示模式..."
        python main.py --mode demo
        ;;
esac
EOF
    
    chmod +x start_system.sh
    
    # 创建停止脚本
    cat > stop_system.sh << 'EOF'
#!/bin/bash
# 停止系统脚本

echo "停止无人机交通警察系统..."
pkill -f "python main.py"
echo "系统已停止"
EOF
    
    chmod +x stop_system.sh
    
    log_success "启动脚本创建完成"
}

# 主函数
main() {
    echo "🚁 无人机交通警察系统 - 自动部署脚本"
    echo "================================================"
    
    # 检查系统要求
    check_requirements
    
    # 安装Conda
    install_conda
    
    # 创建虚拟环境
    create_environment
    
    # 安装依赖
    install_dependencies
    
    # 下载模型
    download_models
    
    # 创建目录
    create_directories
    
    # 创建启动脚本
    create_startup_scripts
    
    # 运行测试
    run_tests
    
    echo ""
    echo "🎉 部署完成！"
    echo "================================================"
    echo "启动系统: ./start_system.sh"
    echo "停止系统: ./stop_system.sh"
    echo "查看文档: cat DEPLOYMENT_GUIDE.md"
    echo ""
    echo "系统已准备就绪，祝您在竞赛中取得好成绩！"
}

# 运行主函数
main "$@"
