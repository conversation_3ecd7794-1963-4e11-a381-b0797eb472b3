#!/usr/bin/env python3
"""
测试OpenCV安装
"""

def test_opencv():
    """测试OpenCV是否正确安装"""
    try:
        import cv2
        print(f"✅ OpenCV 安装成功!")
        print(f"   版本: {cv2.__version__}")
        
        # 测试基本功能
        import numpy as np
        
        # 创建一个测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 0, 0]  # 蓝色
        
        # 测试图像操作
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print(f"   图像处理测试: 成功 (灰度图大小: {gray.shape})")
        
        # 测试编码解码
        success, encoded = cv2.imencode('.jpg', test_image)
        if success:
            decoded = cv2.imdecode(encoded, cv2.IMREAD_COLOR)
            print(f"   编码解码测试: 成功 (解码图像大小: {decoded.shape})")
        
        print("✅ OpenCV 功能测试全部通过!")
        return True
        
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        print("   请尝试安装: pip install opencv-python")
        return False
    except Exception as e:
        print(f"❌ OpenCV 功能测试失败: {e}")
        return False


def test_other_packages():
    """测试其他关键包"""
    packages = {
        'numpy': 'numpy',
        'PIL': 'Pillow',
        'torch': 'torch',
        'ultralytics': 'ultralytics'
    }
    
    results = {}
    
    for import_name, pip_name in packages.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {pip_name}: {version}")
            results[pip_name] = True
        except ImportError:
            print(f"❌ {pip_name}: 未安装")
            results[pip_name] = False
        except Exception as e:
            print(f"⚠️  {pip_name}: 导入异常 - {e}")
            results[pip_name] = False
    
    return results


if __name__ == "__main__":
    print("🔍 测试关键依赖包...")
    print("\n1. 测试 OpenCV:")
    opencv_ok = test_opencv()
    
    print("\n2. 测试其他包:")
    other_results = test_other_packages()
    
    print("\n📊 测试总结:")
    if opencv_ok:
        print("✅ OpenCV: 正常")
    else:
        print("❌ OpenCV: 异常")
    
    for package, status in other_results.items():
        status_text = "正常" if status else "异常"
        icon = "✅" if status else "❌"
        print(f"{icon} {package}: {status_text}")
    
    # 给出建议
    print("\n💡 建议:")
    if not opencv_ok:
        print("   - 重新安装OpenCV: pip uninstall opencv-python && pip install opencv-python")
    
    missing_packages = [pkg for pkg, status in other_results.items() if not status]
    if missing_packages:
        print(f"   - 安装缺失包: pip install {' '.join(missing_packages)}")
    
    if opencv_ok and all(other_results.values()):
        print("   🎉 所有依赖包都正常，可以启动系统!")
