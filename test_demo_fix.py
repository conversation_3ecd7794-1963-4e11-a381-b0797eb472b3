#!/usr/bin/env python3
"""
测试演示修复
验证start_time字段问题是否已解决
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_system_info():
    """测试系统信息获取"""
    print("🔧 测试系统信息获取...")
    
    try:
        from core.system import SystemManager
        
        # 创建系统管理器
        system_manager = SystemManager()
        print("✅ 系统管理器创建成功")
        
        # 测试未初始化状态的系统信息
        print("\n📋 测试未初始化状态...")
        info = await system_manager.get_system_info()
        print(f"系统名称: {info['name']}")
        print(f"版本号: {info['version']}")
        print(f"启动时间: {info.get('start_time', '未启动')}")
        print(f"运行状态: {info.get('status', {}).get('running', False)}")
        
        # 测试初始化后的系统信息
        print("\n📋 测试初始化后状态...")
        success = await system_manager.initialize()
        if success:
            print("✅ 系统初始化成功")
            
            info = await system_manager.get_system_info()
            print(f"系统名称: {info['name']}")
            print(f"版本号: {info['version']}")
            print(f"启动时间: {info.get('start_time', '未启动')}")
            print(f"运行状态: {info.get('status', {}).get('running', False)}")
            
            # 测试启动后的系统信息
            print("\n📋 测试启动后状态...")
            start_success = await system_manager.start()
            if start_success:
                print("✅ 系统启动成功")
                
                info = await system_manager.get_system_info()
                print(f"系统名称: {info['name']}")
                print(f"版本号: {info['version']}")
                print(f"启动时间: {info.get('start_time', '未启动')}")
                print(f"运行状态: {info.get('status', {}).get('running', False)}")
                
                if info.get('uptime_seconds'):
                    print(f"运行时间: {info['uptime_seconds']:.2f} 秒")
                
                # 测试统计信息
                print("\n📈 测试统计信息...")
                stats = await system_manager.get_comprehensive_statistics()
                
                uptime = stats.get('performance', {}).get('uptime')
                if uptime is not None:
                    print(f"运行时间: {uptime:.2f} 秒")
                else:
                    print(f"运行时间: 未启动")
                
                system_stats = stats.get('system', {})
                print(f"处理帧数: {system_stats.get('total_frames', 0)}")
                print(f"检测总数: {system_stats.get('total_detections', 0)}")
                
                # 停止系统
                await system_manager.stop()
                print("✅ 系统停止成功")
            else:
                print("❌ 系统启动失败")
        else:
            print("❌ 系统初始化失败")
        
        # 清理
        await system_manager.shutdown()
        print("✅ 系统关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_demo_interactive():
    """测试交互式演示的关键功能"""
    print("\n🎭 测试交互式演示关键功能...")
    
    try:
        from demo_interactive import InteractiveDemo
        
        # 创建演示实例
        demo = InteractiveDemo()
        print("✅ 演示实例创建成功")
        
        # 测试启动系统
        await demo.start_system()
        
        if demo.system_manager:
            print("✅ 演示系统启动成功")
            
            # 测试显示系统信息
            await demo.show_system_info()
            
            # 测试显示统计信息
            await demo.show_statistics()
            
            # 停止系统
            await demo.stop_system()
            print("✅ 演示系统停止成功")
        else:
            print("❌ 演示系统启动失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 演示修复测试")
    print("=" * 50)
    print("测试start_time字段问题是否已修复")
    print()
    
    async def run_tests():
        # 测试系统信息
        test1_success = await test_system_info()
        
        # 测试交互式演示
        test2_success = await test_demo_interactive()
        
        print("\n" + "=" * 50)
        print("🎯 测试结果总结:")
        print(f"系统信息测试: {'✅ 通过' if test1_success else '❌ 失败'}")
        print(f"交互式演示测试: {'✅ 通过' if test2_success else '❌ 失败'}")
        
        if test1_success and test2_success:
            print("\n🎉 所有测试通过！start_time问题已修复！")
            print("\n💡 现在可以正常使用演示模式:")
            print("   python demo_interactive.py")
            print("   python start.py")
        else:
            print("\n❌ 部分测试失败，请检查错误信息")
        
        return test1_success and test2_success
    
    try:
        success = asyncio.run(run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
