#!/usr/bin/env python3
"""
安全帽检测演示
展示电动车骑行者未戴安全帽检测功能
"""

import sys
import cv2
import time
import asyncio
import numpy as np
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton, QTextEdit, QProgressBar
    from PyQt6.QtCore import QTimer, pyqtSignal, QThread, QObject
    from PyQt6.QtGui import QPixmap, QImage, QFont
    QT6_AVAILABLE = True
except ImportError:
    QT6_AVAILABLE = False

from core.ai.detection.helmet_detector import HelmetDetector, HelmetDetection
from config.ai_config import AIConfig

class HelmetDetectionWorker(QObject):
    """安全帽检测工作线程"""
    
    # 信号定义
    frame_processed = pyqtSignal(np.ndarray, list)  # 处理后的帧和检测结果
    statistics_updated = pyqtSignal(dict)  # 统计信息更新
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        self.helmet_detector = None
        self.video_path = "data/videos/action1.mp4"
        self.running = False
        self.cap = None
        
    def initialize(self):
        """初始化检测器"""
        try:
            # 创建AI配置
            ai_config = AIConfig()
            helmet_config = ai_config.get_helmet_detection_config()
            
            # 初始化安全帽检测器
            self.helmet_detector = HelmetDetector(helmet_config)
            
            # 打开视频文件
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                raise ValueError(f"Cannot open video: {self.video_path}")
            
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"初始化失败: {e}")
            return False
    
    def start_detection(self):
        """开始检测"""
        self.running = True
        self.run_detection()
    
    def stop_detection(self):
        """停止检测"""
        self.running = False
        if self.cap:
            self.cap.release()
    
    def run_detection(self):
        """运行检测循环"""
        frame_count = 0
        
        while self.running and self.cap:
            ret, frame = self.cap.read()
            
            if not ret:
                # 视频结束，重新开始
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            
            frame_count += 1
            
            try:
                # 执行安全帽检测
                helmet_violations = self.helmet_detector.detect_helmet_violations(frame)
                
                # 在帧上绘制检测结果
                annotated_frame = self.draw_detections(frame, helmet_violations)
                
                # 发送结果
                self.frame_processed.emit(annotated_frame, helmet_violations)
                
                # 每30帧发送一次统计信息
                if frame_count % 30 == 0:
                    stats = self.helmet_detector.get_violation_statistics()
                    self.statistics_updated.emit(stats)
                
                # 控制帧率
                time.sleep(0.033)  # 约30 FPS
                
            except Exception as e:
                self.error_occurred.emit(f"检测错误: {e}")
                break
    
    def draw_detections(self, frame: np.ndarray, helmet_violations: List[HelmetDetection]) -> np.ndarray:
        """在帧上绘制检测结果"""
        annotated_frame = frame.copy()
        
        for violation in helmet_violations:
            person = violation.person_detection
            vehicle = violation.vehicle_detection
            
            # 绘制人员边界框
            x1, y1, x2, y2 = person.bbox
            
            # 根据是否佩戴安全帽选择颜色
            if violation.helmet_detected:
                color = (0, 255, 0)  # 绿色 - 佩戴安全帽
                status = "✓ 安全帽"
            else:
                color = (0, 0, 255)  # 红色 - 未佩戴安全帽
                status = "✗ 无安全帽"
            
            # 绘制边界框
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            label = f"{status} ({violation.helmet_confidence:.2f})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 如果有关联的车辆，也绘制出来
            if vehicle:
                vx1, vy1, vx2, vy2 = vehicle.bbox
                cv2.rectangle(annotated_frame, (vx1, vy1), (vx2, vy2), (255, 255, 0), 2)
                cv2.putText(annotated_frame, vehicle.class_name, (vx1, vy1 - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
            
            # 绘制风险等级
            risk_color = {
                "high": (0, 0, 255),    # 红色
                "medium": (0, 165, 255), # 橙色
                "low": (0, 255, 255)     # 黄色
            }.get(violation.risk_level, (255, 255, 255))
            
            cv2.putText(annotated_frame, f"风险: {violation.risk_level}", 
                       (x1, y2 + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, risk_color, 2)
        
        # 添加总体信息
        total_riders = len(helmet_violations)
        violations = sum(1 for v in helmet_violations if not v.helmet_detected)
        
        info_text = f"骑行者: {total_riders}, 违规: {violations}"
        cv2.putText(annotated_frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return annotated_frame

class HelmetDetectionDemo(QMainWindow):
    """安全帽检测演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.worker = HelmetDetectionWorker()
        self.worker_thread = QThread()
        self.init_ui()
        self.setup_worker()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🛡️ 安全帽检测演示 - 无人机交通警察系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧：视频显示
        left_layout = QVBoxLayout()
        
        # 视频标签
        self.video_label = QLabel("等待视频加载...")
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("border: 2px solid #333; background-color: #000;")
        left_layout.addWidget(self.video_label)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("🎬 开始检测")
        self.stop_button = QPushButton("⏹️ 停止检测")
        self.reset_button = QPushButton("🔄 重置统计")
        
        self.start_button.clicked.connect(self.start_detection)
        self.stop_button.clicked.connect(self.stop_detection)
        self.reset_button.clicked.connect(self.reset_statistics)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.reset_button)
        left_layout.addLayout(button_layout)
        
        main_layout.addLayout(left_layout, 2)
        
        # 右侧：信息面板
        right_layout = QVBoxLayout()
        
        # 统计信息
        stats_label = QLabel("📊 检测统计")
        stats_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        right_layout.addWidget(stats_label)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(200)
        self.stats_text.setReadOnly(True)
        right_layout.addWidget(self.stats_text)
        
        # 检测结果
        results_label = QLabel("🎯 检测结果")
        results_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        right_layout.addWidget(results_label)
        
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        right_layout.addWidget(self.results_text)
        
        # 状态信息
        status_label = QLabel("ℹ️ 系统状态")
        status_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        right_layout.addWidget(status_label)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        right_layout.addWidget(self.status_text)
        
        main_layout.addLayout(right_layout, 1)
        
        # 初始状态
        self.stop_button.setEnabled(False)
        self.update_status("系统已就绪，点击开始检测")
    
    def setup_worker(self):
        """设置工作线程"""
        self.worker.moveToThread(self.worker_thread)
        
        # 连接信号
        self.worker.frame_processed.connect(self.update_video)
        self.worker.statistics_updated.connect(self.update_statistics)
        self.worker.error_occurred.connect(self.handle_error)
        
        # 启动线程
        self.worker_thread.start()
    
    def start_detection(self):
        """开始检测"""
        if self.worker.initialize():
            self.worker.start_detection()
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.update_status("检测已开始...")
        else:
            self.update_status("初始化失败，请检查视频文件")
    
    def stop_detection(self):
        """停止检测"""
        self.worker.stop_detection()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.update_status("检测已停止")
    
    def reset_statistics(self):
        """重置统计"""
        if self.worker.helmet_detector:
            self.worker.helmet_detector.reset_statistics()
            self.update_status("统计信息已重置")
            self.stats_text.clear()
            self.results_text.clear()
    
    def update_video(self, frame: np.ndarray, violations: List[HelmetDetection]):
        """更新视频显示"""
        # 转换为Qt图像格式
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
        
        # 缩放图像以适应标签
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(self.video_label.size(), aspectRatioMode=1)
        self.video_label.setPixmap(scaled_pixmap)
        
        # 更新检测结果
        self.update_detection_results(violations)
    
    def update_detection_results(self, violations: List[HelmetDetection]):
        """更新检测结果显示"""
        if not violations:
            return
        
        timestamp = time.strftime("%H:%M:%S")
        result_text = f"[{timestamp}] 检测到 {len(violations)} 个骑行者:\n"
        
        for i, violation in enumerate(violations, 1):
            status = "✅ 佩戴安全帽" if violation.helmet_detected else "❌ 未佩戴安全帽"
            risk = violation.risk_level
            vehicle_type = violation.vehicle_detection.class_name if violation.vehicle_detection else "未知"
            
            result_text += f"  {i}. {status} (置信度: {violation.helmet_confidence:.2f})\n"
            result_text += f"     车辆类型: {vehicle_type}, 风险等级: {risk}\n"
        
        result_text += "─" * 40 + "\n"
        
        # 添加到结果显示
        self.results_text.append(result_text)
        
        # 保持最新结果在视图中
        cursor = self.results_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.results_text.setTextCursor(cursor)
    
    def update_statistics(self, stats: Dict[str, Any]):
        """更新统计信息"""
        stats_text = f"""📊 安全帽检测统计

🚴 总骑行者数: {stats.get('total_riders', 0)}
❌ 违规数量: {stats.get('violation_count', 0)}
📈 违规率: {stats.get('violation_rate', 0):.1f}%
✅ 合规率: {stats.get('compliance_rate', 0):.1f}%

🎯 检测参数:
   置信度阈值: {stats.get('helmet_confidence_threshold', 0):.2f}

💡 提示: 
   - 绿色框表示佩戴安全帽
   - 红色框表示未佩戴安全帽
   - 黄色框表示检测到的车辆
"""
        
        self.stats_text.setText(stats_text)
    
    def update_status(self, message: str):
        """更新状态信息"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 保持最新状态在视图中
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)
    
    def handle_error(self, error_message: str):
        """处理错误"""
        self.update_status(f"错误: {error_message}")
        self.stop_detection()
    
    def closeEvent(self, event):
        """关闭事件"""
        self.worker.stop_detection()
        self.worker_thread.quit()
        self.worker_thread.wait()
        event.accept()

def main():
    """主函数"""
    print("🛡️ 安全帽检测演示启动")
    
    if not QT6_AVAILABLE:
        print("❌ PyQt6不可用，无法启动图形界面")
        print("💡 请安装PyQt6: pip install PyQt6")
        return
    
    # 检查视频文件
    video_path = Path("data/videos/action1.mp4")
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        print("💡 请确保action1.mp4文件在data/videos/目录中")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QLabel {
            color: #ffffff;
            font-size: 14px;
        }
        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:disabled {
            background-color: #666666;
        }
        QTextEdit {
            background-color: #1e1e1e;
            color: #ffffff;
            border: 1px solid #555555;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # 创建并显示主窗口
    demo = HelmetDetectionDemo()
    demo.show()
    
    print("✅ 安全帽检测演示已启动")
    print("💡 使用说明:")
    print("   1. 点击'开始检测'开始处理视频")
    print("   2. 绿色框表示佩戴安全帽的骑行者")
    print("   3. 红色框表示未佩戴安全帽的骑行者")
    print("   4. 黄色框表示检测到的车辆")
    print("   5. 右侧面板显示详细统计和结果")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
