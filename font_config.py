
# matplotlib中文字体配置
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = [
    'SimHei',           # 黑体
    'Microsoft YaHei',  # 微软雅黑
    'WenQuanYi Zen Hei', # 文泉驿正黑
    'Noto Sans CJK SC', # Noto Sans 中文
    'AR PL UMing CN',   # 文鼎PL明体
    'DejaVu Sans'       # 后备字体
]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
plt.rcParams['font.family'] = 'sans-serif'

print("✅ 中文字体配置已加载")
