# 核心依赖
numpy>=1.21.0
opencv-python>=4.5.0
Pillow>=8.3.0

# 深度学习框架
torch>=1.9.0
torchvision>=0.10.0
ultralytics>=8.0.0

# 计算机视觉
scikit-image>=0.18.0
imageio>=2.9.0

# 数据处理
pandas>=1.3.0
scipy>=1.7.0

# Web框架
fastapi>=0.68.0
uvicorn[standard]>=0.15.0
websockets>=10.0
python-multipart>=0.0.5

# 数据库
sqlalchemy>=1.4.0
alembic>=1.7.0

# 缓存
redis>=4.0.0

# HTTP客户端
httpx>=0.24.0
requests>=2.25.0

# 配置管理
pydantic>=1.8.0
python-dotenv>=0.19.0
PyYAML>=5.4.0

# 日志和监控
loguru>=0.5.0

# 异步支持
asyncio-mqtt>=0.10.0
aiofiles>=0.7.0

# 大语言模型
openai>=0.27.0

# 语音合成 (可选)
# azure-cognitiveservices-speech>=1.19.0
# pyttsx3>=2.90

# 中文分词 (可选)
# jieba>=0.42.1

# 图像处理增强
# albumentations>=1.1.0

# 可视化
matplotlib>=3.4.0
seaborn>=0.11.0

# 工具库
tqdm>=4.62.0
click>=8.0.0

# 开发工具
pytest>=6.2.0
pytest-asyncio>=0.15.0
black>=21.0.0
flake8>=3.9.0

# 部署相关
gunicorn>=20.1.0
docker>=5.0.0

# 硬件接口 (可选)
# pyserial>=3.5
# pymavlink>=2.4.0

# 其他工具
python-dateutil>=2.8.0
pytz>=2021.1
