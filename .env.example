# 空中交警系统环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ================================
# 基础配置
# ================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-change-this-in-production

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=1

# ================================
# 硬件配置
# ================================
# 无人机配置
DRONE_ENABLED=false
DRONE_TYPE=simulator
DRONE_COUNT=1
DRONE_IP_ADDRESS=*************
DRONE_PORT=8080
DRONE_USERNAME=admin
DRONE_PASSWORD=password

# 摄像头配置
CAMERA_ENABLED=true
CAMERA_TYPE=file_source
CAMERA_COUNT=1
CAMERA_RESOLUTION_WIDTH=1920
CAMERA_RESOLUTION_HEIGHT=1080
CAMERA_FPS=30

# 视频文件配置
VIDEO_FILE_PATH=action1.mp4
VIDEO_LOOP=true

# ================================
# AI模型配置
# ================================
MODEL_TYPE=yolov8s
MODEL_PATH=yolov8s.pt
DEVICE=auto
CONFIDENCE_THRESHOLD=0.25
IOU_THRESHOLD=0.45

# 跟踪配置
TRACKING_ENABLED=true
TRACKER_TYPE=deepsort
MAX_TRACKING_AGE=30

# 功能开关
BEHAVIOR_ANALYSIS_ENABLED=true
ACCIDENT_DETECTION_ENABLED=true
VIOLATION_DETECTION_ENABLED=true
HELMET_DETECTION_ENABLED=true

# ================================
# 数据库配置
# ================================
DATABASE_TYPE=sqlite
SQLITE_PATH=data/air_traffic_police.db

# PostgreSQL配置（如果使用）
# DATABASE_TYPE=postgresql
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5432
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=password
# POSTGRES_DB=air_traffic_police

# 缓存配置
CACHE_TYPE=redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
CACHE_ENABLED=true
CACHE_TTL=3600

# ================================
# 第三方API配置
# ================================
# OpenAI配置
LLM_PROVIDER=openai
LLM_ENABLED=true
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000

# Azure OpenAI配置（可选）
# LLM_PROVIDER=azure_openai
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_DEPLOYMENT=gpt-35-turbo

# 百度文心一言配置（可选）
# LLM_PROVIDER=baidu
# BAIDU_API_KEY=your-baidu-api-key
# BAIDU_SECRET_KEY=your-baidu-secret-key

# Ollama 本地模型配置（推荐）
# LLM_PROVIDER=ollama
# OLLAMA_BASE_URL=http://localhost:11434
# OLLAMA_MODEL=qwen:7b-chat
# OLLAMA_TIMEOUT=60

# 语音合成配置
TTS_PROVIDER=azure_speech
TTS_ENABLED=true
AZURE_SPEECH_KEY=your-azure-speech-key
AZURE_SPEECH_REGION=eastus
AZURE_SPEECH_VOICE=zh-CN-XiaoxiaoNeural

# 百度语音配置（可选）
# TTS_PROVIDER=baidu_tts
# BAIDU_TTS_API_KEY=your-baidu-tts-key
# BAIDU_TTS_SECRET_KEY=your-baidu-tts-secret

# 地图服务配置
MAP_PROVIDER=baidu_map
MAP_ENABLED=true
BAIDU_MAP_API_KEY=your-baidu-map-key

# ================================
# 通知服务配置
# ================================
# 邮件配置
EMAIL_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_USE_TLS=true

# 短信配置
SMS_ENABLED=false
SMS_API_KEY=your-sms-api-key
SMS_TEMPLATE_ID=your-template-id

# 微信推送配置
WECHAT_ENABLED=false
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 钉钉推送配置
DINGTALK_ENABLED=false
DINGTALK_WEBHOOK=your-dingtalk-webhook
DINGTALK_SECRET=your-dingtalk-secret

# ================================
# 性能配置
# ================================
MAX_CONCURRENT_STREAMS=4
PROCESSING_TIMEOUT=30
MAX_MEMORY_USAGE_MB=4096

# 数据库连接池
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# 功能开关
# ================================
ENABLE_VOICE=true
ENABLE_RECORDING=false
ENABLE_NOTIFICATIONS=true
ENABLE_METRICS=true

# 数据保留配置
DATA_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7
VIDEO_RETENTION_DAYS=3

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=7

# ================================
# 安全配置
# ================================
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_HOSTS=["*"]

# API限流
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ================================
# 监控配置
# ================================
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# ================================
# 开发配置
# ================================
# 仅在开发环境使用
ENABLE_QUERY_LOGGING=false
AUTO_RELOAD=true
