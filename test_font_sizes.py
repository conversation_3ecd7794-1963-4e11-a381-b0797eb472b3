#!/usr/bin/env python3
"""
字体大小测试脚本
测试状态卡片的字体大小调整
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_status_card_fonts():
    """测试状态卡片字体大小"""
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout
        from PyQt6.QtGui import QFont
        
        # 导入状态卡片类
        from demo_qt6 import StatusCard
        
        app = QApplication([])
        
        # 创建测试窗口
        window = QWidget()
        window.setWindowTitle("状态卡片字体大小测试")
        window.setGeometry(200, 200, 600, 400)
        
        layout = QVBoxLayout()
        
        # 标题
        from PyQt6.QtWidgets import QLabel
        from PyQt6.QtCore import Qt
        title = QLabel("状态卡片字体大小测试")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: palette(text); padding: 20px; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 创建状态卡片网格
        grid_layout = QGridLayout()
        
        # 测试不同的状态卡片
        cards = [
            ("系统状态", "运行中", "🔧"),
            ("处理速度", "30 FPS", "⚡"),
            ("检测总数", "1,234", "🎯"),
            ("运行时间", "15.3m", "⏱️"),
        ]
        
        test_cards = []
        for i, (title, value, icon) in enumerate(cards):
            card = StatusCard(title, value, icon)
            test_cards.append(card)
            row = i // 2
            col = i % 2
            grid_layout.addWidget(card, row, col)
        
        layout.addLayout(grid_layout)
        
        # 字体信息显示
        info_label = QLabel()
        info_text = """
字体大小调整说明:
- 图标字体: 18px → 14px (减小22%)
- 标题字体: 11px → 10px (减小9%)
- 数值字体: 18px → 14px (减小22%)
- 卡片高度: 120px → 100px (减小17%)

这样可以让左下角状态卡片更加紧凑，字体大小更合适。
        """
        info_label.setText(info_text.strip())
        info_label.setStyleSheet("""
            color: palette(text); 
            padding: 15px; 
            background-color: palette(base); 
            border: 2px solid palette(mid); 
            border-radius: 8px;
            font-size: 12px;
        """)
        layout.addWidget(info_label)
        
        window.setLayout(layout)
        window.setStyleSheet("background-color: palette(window); padding: 20px;")
        
        print("✅ 状态卡片字体测试窗口创建成功")
        print("💡 窗口将显示5秒后自动关闭")
        print("📏 字体大小已调整:")
        print("   - 图标: 18px → 14px")
        print("   - 标题: 11px → 10px") 
        print("   - 数值: 18px → 14px")
        print("   - 高度: 120px → 100px")
        
        window.show()
        
        # 显示5秒后关闭
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5秒
        
        app.exec()
        print("✅ 字体大小测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 字体大小测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_font_sizes():
    """比较字体大小调整前后的效果"""
    print("📏 字体大小调整对比:")
    print("=" * 50)
    
    print("🔧 状态卡片组件:")
    print("   组件     | 调整前  | 调整后  | 变化")
    print("   ---------|---------|---------|--------")
    print("   图标字体 | 18px    | 14px    | -22%")
    print("   标题字体 | 11px    | 10px    | -9%")
    print("   数值字体 | 18px    | 14px    | -22%")
    print("   卡片高度 | 120px   | 100px   | -17%")
    
    print("\n✅ 调整效果:")
    print("   - 字体大小更合适，不会显得过大")
    print("   - 卡片更紧凑，节省界面空间")
    print("   - 保持良好的可读性")
    print("   - 整体视觉效果更协调")
    
    print("\n🎯 适用场景:")
    print("   - 左下角状态卡片区域")
    print("   - 需要显示多个状态指标")
    print("   - 界面空间有限的情况")
    print("   - 追求简洁美观的设计")

def main():
    """主函数"""
    print("🎨 Qt6状态卡片字体大小调整测试")
    print("=" * 50)
    
    # 检查Qt6可用性
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6可用")
    except ImportError:
        print("❌ PyQt6不可用，请安装: pip install PyQt6")
        return False
    
    # 显示调整对比
    compare_font_sizes()
    
    print("\n🔍 测试状态卡片字体大小...")
    font_test = test_status_card_fonts()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果:")
    print(f"字体大小调整: {'✅ 成功' if font_test else '❌ 失败'}")
    
    if font_test:
        print("\n🎉 字体大小调整完成！")
        print("\n💡 现在可以启动改进后的Qt6界面:")
        print("   python demo_qt6.py")
        print("\n📋 改进内容:")
        print("   - 状态卡片字体更合适")
        print("   - 界面更加紧凑美观")
        print("   - 保持良好的可读性")
    else:
        print("\n❌ 字体调整测试失败")
    
    return font_test

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
