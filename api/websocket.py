"""
WebSocket接口
提供实时数据推送
"""
import logging
import asyncio
import json
from typing import Dict, List, Set
from datetime import datetime

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from fastapi.websockets import WebSocketState

from core.system import SystemManager


router = APIRouter()
websocket_router = router  # 为了兼容app.py中的导入
logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接
        self.active_connections: Dict[str, Set[WebSocket]] = {
            "system": set(),
            "detection": set(),
            "tracking": set(),
            "analysis": set(),
            "alerts": set(),
            "all": set()
        }
        self.connection_info: Dict[WebSocket, Dict] = {}
    
    async def connect(self, websocket: WebSocket, channel: str = "all"):
        """接受WebSocket连接"""
        await websocket.accept()
        
        # 添加到相应频道
        if channel in self.active_connections:
            self.active_connections[channel].add(websocket)
        self.active_connections["all"].add(websocket)
        
        # 记录连接信息
        self.connection_info[websocket] = {
            "channel": channel,
            "connected_at": datetime.utcnow(),
            "client_ip": websocket.client.host if websocket.client else "unknown"
        }
        
        logger.info(f"WebSocket connected to channel '{channel}' from {websocket.client.host if websocket.client else 'unknown'}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        # 从所有频道移除
        for channel_connections in self.active_connections.values():
            channel_connections.discard(websocket)
        
        # 移除连接信息
        if websocket in self.connection_info:
            info = self.connection_info.pop(websocket)
            logger.info(f"WebSocket disconnected from channel '{info.get('channel', 'unknown')}'")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """发送个人消息"""
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast_to_channel(self, message: str, channel: str = "all"):
        """向指定频道广播消息"""
        if channel not in self.active_connections:
            return
        
        disconnected = set()
        for websocket in self.active_connections[channel].copy():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(message)
                else:
                    disconnected.add(websocket)
            except Exception as e:
                logger.error(f"Failed to broadcast to {websocket.client.host if websocket.client else 'unknown'}: {e}")
                disconnected.add(websocket)
        
        # 清理断开的连接
        for websocket in disconnected:
            self.disconnect(websocket)
    
    async def broadcast_json(self, data: dict, channel: str = "all"):
        """广播JSON数据"""
        message = json.dumps(data)
        await self.broadcast_to_channel(message, channel)
    
    def get_connection_count(self, channel: str = "all") -> int:
        """获取连接数"""
        return len(self.active_connections.get(channel, set()))
    
    def get_all_connections_info(self) -> List[Dict]:
        """获取所有连接信息"""
        return [
            {
                "channel": info["channel"],
                "connected_at": info["connected_at"].isoformat(),
                "client_ip": info["client_ip"],
                "state": websocket.client_state.name
            }
            for websocket, info in self.connection_info.items()
        ]


# 全局连接管理器
manager = ConnectionManager()


async def get_system_manager() -> SystemManager:
    """获取系统管理器（这里需要从应用状态获取）"""
    # 注意：在实际使用中，需要从应用状态获取system_manager
    # 这里暂时返回None，在实际部署时需要修改
    return None


@router.websocket("/connect/{channel}")
async def websocket_endpoint(websocket: WebSocket, channel: str):
    """WebSocket连接端点"""
    await manager.connect(websocket, channel)
    
    try:
        # 发送连接确认消息
        await manager.send_personal_message(
            json.dumps({
                "type": "connection",
                "status": "connected",
                "channel": channel,
                "timestamp": datetime.utcnow().isoformat(),
                "message": f"Connected to channel '{channel}'"
            }),
            websocket
        )
        
        # 保持连接并处理消息
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理客户端请求
                await handle_client_message(websocket, message_data)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    json.dumps({
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.utcnow().isoformat()
                    }),
                    websocket
                )
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await manager.send_personal_message(
                    json.dumps({
                        "type": "error",
                        "message": str(e),
                        "timestamp": datetime.utcnow().isoformat()
                    }),
                    websocket
                )
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        manager.disconnect(websocket)


async def handle_client_message(websocket: WebSocket, message_data: dict):
    """处理客户端消息"""
    message_type = message_data.get("type")
    
    if message_type == "ping":
        # 心跳检测
        await manager.send_personal_message(
            json.dumps({
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            }),
            websocket
        )
    
    elif message_type == "subscribe":
        # 订阅特定数据类型
        data_types = message_data.get("data_types", [])
        await manager.send_personal_message(
            json.dumps({
                "type": "subscription",
                "status": "subscribed",
                "data_types": data_types,
                "timestamp": datetime.utcnow().isoformat()
            }),
            websocket
        )
    
    elif message_type == "get_status":
        # 获取当前状态
        # 这里需要实际的系统管理器实例
        status_data = {
            "type": "status",
            "data": {
                "connections": manager.get_connection_count(),
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        await manager.send_personal_message(json.dumps(status_data), websocket)
    
    else:
        await manager.send_personal_message(
            json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}",
                "timestamp": datetime.utcnow().isoformat()
            }),
            websocket
        )


# 数据推送函数
async def push_system_status(status_data: dict):
    """推送系统状态"""
    message = {
        "type": "system_status",
        "data": status_data,
        "timestamp": datetime.utcnow().isoformat()
    }
    await manager.broadcast_json(message, "system")


async def push_detection_results(detections: list):
    """推送检测结果"""
    message = {
        "type": "detection_results",
        "data": detections,
        "timestamp": datetime.utcnow().isoformat()
    }
    await manager.broadcast_json(message, "detection")


async def push_tracking_updates(tracks: list):
    """推送跟踪更新"""
    message = {
        "type": "tracking_updates",
        "data": tracks,
        "timestamp": datetime.utcnow().isoformat()
    }
    await manager.broadcast_json(message, "tracking")


async def push_analysis_results(analysis: dict):
    """推送分析结果"""
    message = {
        "type": "analysis_results",
        "data": analysis,
        "timestamp": datetime.utcnow().isoformat()
    }
    await manager.broadcast_json(message, "analysis")


async def push_safety_alert(alert: dict):
    """推送安全警报"""
    message = {
        "type": "safety_alert",
        "data": alert,
        "timestamp": datetime.utcnow().isoformat(),
        "priority": "high"
    }
    await manager.broadcast_json(message, "alerts")


async def push_traffic_metrics(metrics: dict):
    """推送交通指标"""
    message = {
        "type": "traffic_metrics",
        "data": metrics,
        "timestamp": datetime.utcnow().isoformat()
    }
    await manager.broadcast_json(message, "analysis")


# WebSocket管理API
@router.get("/connections")
async def get_connections_info():
    """获取连接信息"""
    return {
        "success": True,
        "timestamp": datetime.utcnow().isoformat(),
        "connections": manager.get_all_connections_info(),
        "connection_counts": {
            channel: manager.get_connection_count(channel)
            for channel in manager.active_connections.keys()
        }
    }


@router.post("/broadcast/{channel}")
async def broadcast_message(channel: str, message: dict):
    """广播消息到指定频道"""
    try:
        await manager.broadcast_json(message, channel)
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "channel": channel,
            "message": "Message broadcasted successfully"
        }
    except Exception as e:
        logger.error(f"Failed to broadcast message: {e}")
        return {
            "success": False,
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


# 获取连接管理器实例的函数（供其他模块使用）
def get_connection_manager() -> ConnectionManager:
    """获取连接管理器实例"""
    return manager
