"""
API中间件
处理请求日志、认证、限流等
"""
import time
import logging
import json
from typing import Dict, Any, Callable
from datetime import datetime

from fastapi import FastAPI, Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    def __init__(self, app: FastAPI, logger: logging.Logger = None):
        super().__init__(app)
        self.logger = logger or logging.getLogger("api.requests")
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取客户端信息
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # 记录请求信息
        self.logger.info(
            f"Request started: {request.method} {request.url} "
            f"from {client_ip} ({user_agent})"
        )
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            self.logger.info(
                f"Request completed: {request.method} {request.url} "
                f"-> {response.status_code} in {process_time:.3f}s"
            )
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录错误
            process_time = time.time() - start_time
            self.logger.error(
                f"Request failed: {request.method} {request.url} "
                f"-> Error: {str(e)} in {process_time:.3f}s"
            )
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests = {}  # {client_ip: [(timestamp, count), ...]}
        self.logger = logging.getLogger("api.ratelimit")
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期记录
        self._cleanup_old_requests(current_time)
        
        # 检查当前客户端的请求频率
        if self._is_rate_limited(client_ip, current_time):
            self.logger.warning(f"Rate limit exceeded for {client_ip}")
            from fastapi import HTTPException
            raise HTTPException(
                status_code=429,
                detail="Too many requests. Please try again later."
            )
        
        # 记录请求
        self._record_request(client_ip, current_time)
        
        return await call_next(request)
    
    def _cleanup_old_requests(self, current_time: float):
        """清理过期的请求记录"""
        cutoff_time = current_time - 60  # 1分钟前
        
        for client_ip in list(self.requests.keys()):
            self.requests[client_ip] = [
                (timestamp, count) for timestamp, count in self.requests[client_ip]
                if timestamp > cutoff_time
            ]
            
            if not self.requests[client_ip]:
                del self.requests[client_ip]
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """检查是否超过限流"""
        if client_ip not in self.requests:
            return False
        
        # 计算最近1分钟的请求数
        cutoff_time = current_time - 60
        recent_requests = sum(
            count for timestamp, count in self.requests[client_ip]
            if timestamp > cutoff_time
        )
        
        return recent_requests >= self.requests_per_minute
    
    def _record_request(self, client_ip: str, current_time: float):
        """记录请求"""
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        self.requests[client_ip].append((current_time, 1))


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response


class MetricsMiddleware(BaseHTTPMiddleware):
    """指标收集中间件"""
    
    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.metrics = {
            "total_requests": 0,
            "requests_by_method": {},
            "requests_by_status": {},
            "requests_by_endpoint": {},
            "total_response_time": 0.0,
            "avg_response_time": 0.0
        }
        self.logger = logging.getLogger("api.metrics")
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # 计算响应时间
            response_time = time.time() - start_time
            
            # 更新指标
            self._update_metrics(request, response, response_time)
            
            return response
            
        except Exception as e:
            # 记录错误请求
            response_time = time.time() - start_time
            self._update_error_metrics(request, response_time)
            raise
    
    def _update_metrics(self, request: Request, response: Response, response_time: float):
        """更新指标"""
        # 总请求数
        self.metrics["total_requests"] += 1
        
        # 按方法统计
        method = request.method
        self.metrics["requests_by_method"][method] = self.metrics["requests_by_method"].get(method, 0) + 1
        
        # 按状态码统计
        status_code = response.status_code
        self.metrics["requests_by_status"][status_code] = self.metrics["requests_by_status"].get(status_code, 0) + 1
        
        # 按端点统计
        endpoint = str(request.url.path)
        self.metrics["requests_by_endpoint"][endpoint] = self.metrics["requests_by_endpoint"].get(endpoint, 0) + 1
        
        # 响应时间统计
        self.metrics["total_response_time"] += response_time
        self.metrics["avg_response_time"] = self.metrics["total_response_time"] / self.metrics["total_requests"]
    
    def _update_error_metrics(self, request: Request, response_time: float):
        """更新错误指标"""
        self.metrics["total_requests"] += 1
        
        method = request.method
        self.metrics["requests_by_method"][method] = self.metrics["requests_by_method"].get(method, 0) + 1
        
        # 错误状态码
        self.metrics["requests_by_status"][500] = self.metrics["requests_by_status"].get(500, 0) + 1
        
        endpoint = str(request.url.path)
        self.metrics["requests_by_endpoint"][endpoint] = self.metrics["requests_by_endpoint"].get(endpoint, 0) + 1
        
        self.metrics["total_response_time"] += response_time
        self.metrics["avg_response_time"] = self.metrics["total_response_time"] / self.metrics["total_requests"]
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标"""
        return self.metrics.copy()


def setup_middleware(app: FastAPI, config: Dict[str, Any]):
    """设置中间件"""
    
    # 指标收集中间件
    metrics_middleware = MetricsMiddleware(app)
    app.add_middleware(BaseHTTPMiddleware, dispatch=metrics_middleware.dispatch)
    
    # 将指标中间件添加到应用状态，以便其他地方访问
    app.state.metrics_middleware = metrics_middleware
    
    # 安全头中间件
    if config.get("enable_security_headers", True):
        app.add_middleware(SecurityHeadersMiddleware)
    
    # 限流中间件
    if config.get("enable_rate_limiting", True):
        rate_limit = config.get("rate_limit", 60)
        app.add_middleware(RateLimitMiddleware, requests_per_minute=rate_limit)
    
    # 请求日志中间件
    if config.get("enable_request_logging", True):
        app.add_middleware(RequestLoggingMiddleware)


# 工具函数
def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 回退到直接连接IP
    return request.client.host if request.client else "unknown"


def get_request_info(request: Request) -> Dict[str, Any]:
    """获取请求信息"""
    return {
        "method": request.method,
        "url": str(request.url),
        "path": request.url.path,
        "query_params": dict(request.query_params),
        "headers": dict(request.headers),
        "client_ip": get_client_ip(request),
        "user_agent": request.headers.get("user-agent", "unknown"),
        "timestamp": datetime.utcnow().isoformat()
    }
