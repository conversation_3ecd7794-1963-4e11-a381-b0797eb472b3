"""
数据管理API路由
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class DataStatistics(BaseModel):
    success: bool
    timestamp: str
    database_stats: Dict[str, Any]
    cache_stats: Dict[str, Any]
    data_manager_stats: Dict[str, Any]


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.get("/statistics", response_model=DataStatistics)
async def get_data_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取数据统计信息"""
    try:
        stats = await system_manager.data_manager.get_comprehensive_stats()
        
        return DataStatistics(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            database_stats=stats.get("database", {}),
            cache_stats=stats.get("cache", {}),
            data_manager_stats=stats.get("data_manager", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to get data statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def data_health_check(system_manager: SystemManager = Depends(get_system_manager)):
    """数据层健康检查"""
    try:
        health = await system_manager.data_manager.health_check()
        
        status_code = 200 if health.get("status") == "healthy" else 503
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "health": health
        }
        
    except Exception as e:
        logger.error(f"Data health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sessions")
async def get_sessions(
    limit: int = Query(10, description="返回数量限制"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取监控会话列表"""
    try:
        # 这里需要实现获取会话列表的逻辑
        sessions = []  # 临时空列表
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "sessions": sessions,
            "count": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"Failed to get sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sessions")
async def create_session(
    config: Optional[Dict[str, Any]] = None,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """创建新的监控会话"""
    try:
        session_id = await system_manager.data_manager.create_session(config)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "session_id": session_id,
            "message": "Session created successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/sessions/{session_id}")
async def end_session(
    session_id: str,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """结束监控会话"""
    try:
        success = await system_manager.data_manager.end_session(session_id)
        
        if success:
            return {
                "success": True,
                "timestamp": datetime.utcnow().isoformat(),
                "session_id": session_id,
                "message": "Session ended successfully"
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to end session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/traffic-statistics")
async def get_traffic_statistics(
    hours: int = Query(24, description="时间范围（小时）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取交通统计数据"""
    try:
        stats = await system_manager.data_manager.get_traffic_statistics(hours)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get traffic statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/accidents")
async def get_accident_data(
    hours: int = Query(24, description="时间范围（小时）"),
    limit: int = Query(100, description="返回数量限制"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取事故数据"""
    try:
        accidents = await system_manager.data_manager.get_recent_accidents(hours)
        
        # 限制返回数量
        if len(accidents) > limit:
            accidents = accidents[:limit]
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "accidents": accidents,
            "count": len(accidents)
        }
        
    except Exception as e:
        logger.error(f"Failed to get accident data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/violations")
async def get_violation_data(
    hours: int = Query(24, description="时间范围（小时）"),
    limit: int = Query(100, description="返回数量限制"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取违法数据"""
    try:
        violations = await system_manager.data_manager.get_recent_violations(hours)
        
        # 限制返回数量
        if len(violations) > limit:
            violations = violations[:limit]
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "violations": violations,
            "count": len(violations)
        }
        
    except Exception as e:
        logger.error(f"Failed to get violation data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup")
async def cleanup_old_data(
    retention_days: int = Query(30, description="数据保留天数"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """清理旧数据"""
    try:
        if retention_days < 1:
            raise HTTPException(status_code=400, detail="Retention days must be at least 1")
        
        deleted_count = await system_manager.data_manager.cleanup_old_data(retention_days)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "retention_days": retention_days,
            "deleted_records": deleted_count,
            "message": f"Cleaned up {deleted_count} old records"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup old data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache/stats")
async def get_cache_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取缓存统计信息"""
    try:
        stats = await system_manager.data_manager.cache.get_stats()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "cache_stats": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get cache statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cache/clear")
async def clear_cache(
    pattern: Optional[str] = Query(None, description="清理模式"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """清空缓存"""
    try:
        await system_manager.data_manager.cache.clear(pattern)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "pattern": pattern,
            "message": "Cache cleared successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/stats")
async def get_database_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取数据库统计信息"""
    try:
        stats = await system_manager.data_manager.database.get_database_stats()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "database_stats": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get database statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/database/optimize")
async def optimize_database(system_manager: SystemManager = Depends(get_system_manager)):
    """优化数据库"""
    try:
        await system_manager.data_manager.database.optimize_database()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Database optimization completed"
        }
        
    except Exception as e:
        logger.error(f"Failed to optimize database: {e}")
        raise HTTPException(status_code=500, detail=str(e))
