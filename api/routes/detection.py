"""
目标检测API路由
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, File
from pydantic import BaseModel
import numpy as np

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class DetectionResult(BaseModel):
    bbox: List[float]  # [x1, y1, x2, y2]
    confidence: float
    class_id: int
    class_name: str
    track_id: Optional[int] = None


class DetectionResponse(BaseModel):
    success: bool
    timestamp: str
    frame_id: Optional[int] = None
    detections: List[DetectionResult]
    processing_time: float
    metadata: Dict[str, Any] = {}


class DetectionConfigRequest(BaseModel):
    confidence_threshold: Optional[float] = None
    nms_threshold: Optional[float] = None
    max_detections: Optional[int] = None
    target_classes: Optional[List[str]] = None


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.post("/detect", response_model=DetectionResponse)
async def detect_objects(
    file: UploadFile = File(...),
    frame_id: Optional[int] = None,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """检测图像中的目标"""
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # 读取图像数据
        image_data = await file.read()
        
        # 转换为numpy数组
        import cv2
        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise HTTPException(status_code=400, detail="Invalid image format")
        
        # 执行检测
        start_time = datetime.utcnow()
        detections = await system_manager.ai_engine.detect_objects(image)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        # 转换检测结果
        detection_results = []
        for detection in detections:
            detection_results.append(DetectionResult(
                bbox=detection.bbox,
                confidence=detection.confidence,
                class_id=detection.class_id,
                class_name=detection.class_name,
                track_id=getattr(detection, 'track_id', None)
            ))
        
        return DetectionResponse(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            frame_id=frame_id,
            detections=detection_results,
            processing_time=processing_time,
            metadata={
                "image_shape": image.shape,
                "detection_count": len(detections)
            }
        )
        
    except Exception as e:
        logger.error(f"Detection failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_detection_config(system_manager: SystemManager = Depends(get_system_manager)):
    """获取检测配置"""
    try:
        config = await system_manager.ai_engine.get_detection_config()
        return {
            "success": True,
            "config": config
        }
    except Exception as e:
        logger.error(f"Failed to get detection config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config")
async def update_detection_config(
    request: DetectionConfigRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """更新检测配置"""
    try:
        config_dict = request.dict(exclude_unset=True)
        success = await system_manager.ai_engine.update_detection_config(config_dict)
        
        return {
            "success": success,
            "message": "Detection configuration updated" if success else "Failed to update configuration",
            "config": config_dict
        }
    except Exception as e:
        logger.error(f"Failed to update detection config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/classes")
async def get_detection_classes(system_manager: SystemManager = Depends(get_system_manager)):
    """获取检测类别"""
    try:
        classes = await system_manager.ai_engine.get_detection_classes()
        return {
            "success": True,
            "classes": classes
        }
    except Exception as e:
        logger.error(f"Failed to get detection classes: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_detection_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取检测统计信息"""
    try:
        stats = await system_manager.ai_engine.get_detection_statistics()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats
        }
    except Exception as e:
        logger.error(f"Failed to get detection statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance")
async def get_detection_performance(system_manager: SystemManager = Depends(get_system_manager)):
    """获取检测性能指标"""
    try:
        performance = await system_manager.ai_engine.get_detection_performance()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "performance": performance
        }
    except Exception as e:
        logger.error(f"Failed to get detection performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/benchmark")
async def run_detection_benchmark(
    iterations: int = 10,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """运行检测性能基准测试"""
    try:
        if iterations < 1 or iterations > 100:
            raise HTTPException(status_code=400, detail="Iterations must be between 1 and 100")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (640, 480, 3), dtype=np.uint8)
        
        # 运行基准测试
        start_time = datetime.utcnow()
        times = []
        
        for i in range(iterations):
            iter_start = datetime.utcnow()
            await system_manager.ai_engine.detect_objects(test_image)
            iter_time = (datetime.utcnow() - iter_start).total_seconds()
            times.append(iter_time)
        
        total_time = (datetime.utcnow() - start_time).total_seconds()
        
        # 计算统计信息
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        fps = 1.0 / avg_time if avg_time > 0 else 0
        
        return {
            "success": True,
            "benchmark_results": {
                "iterations": iterations,
                "total_time": total_time,
                "average_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "fps": fps,
                "times": times
            }
        }
        
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset")
async def reset_detection_engine(system_manager: SystemManager = Depends(get_system_manager)):
    """重置检测引擎"""
    try:
        success = await system_manager.ai_engine.reset_detection()
        
        return {
            "success": success,
            "message": "Detection engine reset" if success else "Failed to reset detection engine",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to reset detection engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))
