"""
业务分析API路由
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, Request, Query
from pydantic import BaseModel

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class AnalysisResult(BaseModel):
    success: bool
    timestamp: str
    traffic_metrics: Dict[str, Any]
    accidents: List[Dict[str, Any]]
    violations: List[Dict[str, Any]]
    safety_alerts: List[Dict[str, Any]]
    decisions: List[Dict[str, Any]]
    risk_level: str
    performance: Dict[str, Any]


class TrafficMetricsResponse(BaseModel):
    success: bool
    timestamp: str
    metrics: Dict[str, Any]
    trends: Dict[str, Any]


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.get("/current", response_model=AnalysisResult)
async def get_current_analysis(system_manager: SystemManager = Depends(get_system_manager)):
    """获取当前分析结果"""
    try:
        analysis = await system_manager.business_engine.get_current_status()
        
        return AnalysisResult(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            traffic_metrics=analysis.get("traffic_metrics", {}),
            accidents=analysis.get("accidents", []),
            violations=analysis.get("violations", []),
            safety_alerts=analysis.get("safety_alerts", []),
            decisions=analysis.get("decisions", []),
            risk_level=analysis.get("risk_level", "low"),
            performance=analysis.get("performance", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to get current analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/traffic", response_model=TrafficMetricsResponse)
async def get_traffic_metrics(
    time_window: int = Query(300, description="时间窗口（秒）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取交通指标"""
    try:
        metrics = await system_manager.business_engine.get_traffic_trends(time_window)
        
        return TrafficMetricsResponse(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            metrics=metrics.get("current", {}),
            trends=metrics.get("trends", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to get traffic metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/accidents")
async def get_recent_accidents(
    hours: int = Query(24, description="时间范围（小时）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取最近事故"""
    try:
        accidents = await system_manager.business_engine.get_recent_accidents(hours * 3600)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "accidents": accidents,
            "count": len(accidents)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent accidents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/violations")
async def get_recent_violations(
    hours: int = Query(24, description="时间范围（小时）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取最近违法行为"""
    try:
        violations = await system_manager.business_engine.get_recent_violations(hours * 3600)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "violations": violations,
            "count": len(violations)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent violations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts")
async def get_recent_alerts(
    hours: int = Query(24, description="时间范围（小时）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取最近安全警报"""
    try:
        alerts = await system_manager.business_engine.get_recent_alerts(hours * 3600)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "alerts": alerts,
            "count": len(alerts)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/decisions")
async def get_recent_decisions(
    hours: int = Query(24, description="时间范围（小时）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取最近决策"""
    try:
        decisions = await system_manager.business_engine.get_recent_decisions(hours * 3600)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "time_range_hours": hours,
            "decisions": decisions,
            "count": len(decisions)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent decisions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_analysis_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取分析统计信息"""
    try:
        stats = await system_manager.business_engine.get_comprehensive_statistics()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get analysis statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/congestion/prediction")
async def predict_congestion(
    horizon: int = Query(300, description="预测时间范围（秒）"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """预测拥堵情况"""
    try:
        prediction = await system_manager.business_engine.predict_congestion(horizon)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "prediction_horizon": horizon,
            "prediction": prediction
        }
        
    except Exception as e:
        logger.error(f"Failed to predict congestion: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/congestion/hotspots")
async def get_congestion_hotspots(
    grid_size: int = Query(10, description="网格大小"),
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取拥堵热点"""
    try:
        hotspots = await system_manager.business_engine.get_congestion_hotspots(grid_size)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "grid_size": grid_size,
            "hotspots": hotspots
        }
        
    except Exception as e:
        logger.error(f"Failed to get congestion hotspots: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset")
async def reset_analysis_engine(system_manager: SystemManager = Depends(get_system_manager)):
    """重置分析引擎"""
    try:
        await system_manager.business_engine.reset()
        
        return {
            "success": True,
            "message": "Analysis engine reset successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to reset analysis engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))
