"""
目标跟踪API路由
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class TrackInfo(BaseModel):
    track_id: int
    class_name: str
    confidence: float
    bbox: List[float]  # [x1, y1, x2, y2]
    center: List[float]  # [x, y]
    velocity: List[float]  # [vx, vy]
    age: int
    hits: int
    time_since_update: int
    is_confirmed: bool


class TrackingResponse(BaseModel):
    success: bool
    timestamp: str
    frame_id: Optional[int] = None
    tracks: List[TrackInfo]
    active_tracks: int
    total_tracks: int
    processing_time: float


class TrackingConfigRequest(BaseModel):
    max_age: Optional[int] = None
    min_hits: Optional[int] = None
    iou_threshold: Optional[float] = None
    max_tracks: Optional[int] = None


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.get("/tracks", response_model=TrackingResponse)
async def get_current_tracks(system_manager: SystemManager = Depends(get_system_manager)):
    """获取当前活跃的跟踪目标"""
    try:
        start_time = datetime.utcnow()
        tracks = await system_manager.ai_engine.get_current_tracks()
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        # 转换跟踪结果
        track_infos = []
        active_count = 0
        
        for track in tracks:
            track_info = TrackInfo(
                track_id=track.track_id,
                class_name=track.class_name,
                confidence=track.confidence,
                bbox=track.bbox,
                center=track.center,
                velocity=track.velocity,
                age=track.age,
                hits=track.hits,
                time_since_update=track.time_since_update,
                is_confirmed=track.is_confirmed
            )
            track_infos.append(track_info)
            
            if track.is_confirmed and track.time_since_update == 0:
                active_count += 1
        
        return TrackingResponse(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            tracks=track_infos,
            active_tracks=active_count,
            total_tracks=len(tracks),
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Failed to get current tracks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tracks/{track_id}")
async def get_track_details(
    track_id: int,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取特定跟踪目标的详细信息"""
    try:
        track = await system_manager.ai_engine.get_track_by_id(track_id)
        
        if not track:
            raise HTTPException(status_code=404, detail=f"Track {track_id} not found")
        
        # 获取轨迹历史
        trajectory = await system_manager.ai_engine.get_track_trajectory(track_id)
        
        return {
            "success": True,
            "track_id": track_id,
            "track_info": {
                "track_id": track.track_id,
                "class_name": track.class_name,
                "confidence": track.confidence,
                "bbox": track.bbox,
                "center": track.center,
                "velocity": track.velocity,
                "age": track.age,
                "hits": track.hits,
                "time_since_update": track.time_since_update,
                "is_confirmed": track.is_confirmed
            },
            "trajectory": trajectory,
            "statistics": {
                "total_detections": len(trajectory),
                "avg_confidence": track.confidence,
                "distance_traveled": getattr(track, 'distance_traveled', 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get track details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_tracking_config(system_manager: SystemManager = Depends(get_system_manager)):
    """获取跟踪配置"""
    try:
        config = await system_manager.ai_engine.get_tracking_config()
        return {
            "success": True,
            "config": config
        }
    except Exception as e:
        logger.error(f"Failed to get tracking config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config")
async def update_tracking_config(
    request: TrackingConfigRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """更新跟踪配置"""
    try:
        config_dict = request.dict(exclude_unset=True)
        success = await system_manager.ai_engine.update_tracking_config(config_dict)
        
        return {
            "success": success,
            "message": "Tracking configuration updated" if success else "Failed to update configuration",
            "config": config_dict
        }
    except Exception as e:
        logger.error(f"Failed to update tracking config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_tracking_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取跟踪统计信息"""
    try:
        stats = await system_manager.ai_engine.get_tracking_statistics()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats
        }
    except Exception as e:
        logger.error(f"Failed to get tracking statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance")
async def get_tracking_performance(system_manager: SystemManager = Depends(get_system_manager)):
    """获取跟踪性能指标"""
    try:
        performance = await system_manager.ai_engine.get_tracking_performance()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "performance": performance
        }
    except Exception as e:
        logger.error(f"Failed to get tracking performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tracks/{track_id}")
async def delete_track(
    track_id: int,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """删除特定的跟踪目标"""
    try:
        success = await system_manager.ai_engine.delete_track(track_id)
        
        if success:
            return {
                "success": True,
                "message": f"Track {track_id} deleted successfully"
            }
        else:
            raise HTTPException(status_code=404, detail=f"Track {track_id} not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete track: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset")
async def reset_tracking_engine(system_manager: SystemManager = Depends(get_system_manager)):
    """重置跟踪引擎"""
    try:
        success = await system_manager.ai_engine.reset_tracking()
        
        return {
            "success": success,
            "message": "Tracking engine reset" if success else "Failed to reset tracking engine",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to reset tracking engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trajectories")
async def get_all_trajectories(
    limit: int = 100,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取所有轨迹数据"""
    try:
        trajectories = await system_manager.ai_engine.get_all_trajectories(limit=limit)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "trajectories": trajectories,
            "count": len(trajectories)
        }
        
    except Exception as e:
        logger.error(f"Failed to get trajectories: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/heatmap")
async def get_tracking_heatmap(
    width: int = 100,
    height: int = 100,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取跟踪热力图数据"""
    try:
        heatmap = await system_manager.ai_engine.get_tracking_heatmap(width, height)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "heatmap": heatmap.tolist() if heatmap is not None else [],
            "dimensions": [width, height]
        }
        
    except Exception as e:
        logger.error(f"Failed to get tracking heatmap: {e}")
        raise HTTPException(status_code=500, detail=str(e))
