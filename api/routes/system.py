"""
系统管理API路由
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class SystemStatusResponse(BaseModel):
    status: str
    timestamp: str
    components: Dict[str, Any]
    performance: Dict[str, Any]


class SystemConfigRequest(BaseModel):
    config: Dict[str, Any]


class SystemConfigResponse(BaseModel):
    success: bool
    message: str
    config: Dict[str, Any]


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(system_manager: SystemManager = Depends(get_system_manager)):
    """获取系统状态"""
    try:
        status = await system_manager.get_system_status()
        return SystemStatusResponse(
            status=status.get("status", "unknown"),
            timestamp=datetime.utcnow().isoformat(),
            components=status.get("components", {}),
            performance=status.get("performance", {})
        )
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check(system_manager: SystemManager = Depends(get_system_manager)):
    """系统健康检查"""
    try:
        health_status = await system_manager.get_health_status()
        
        # 根据健康状态设置HTTP状态码
        status_code = 200
        if health_status.get("overall_status") != "healthy":
            status_code = 503
        
        return {
            "status": health_status.get("overall_status", "unknown"),
            "timestamp": health_status.get("timestamp"),
            "details": health_status
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/info")
async def get_system_info(system_manager: SystemManager = Depends(get_system_manager)):
    """获取系统信息"""
    try:
        info = await system_manager.get_system_info()
        return {
            "system_name": "Air Traffic Police",
            "version": "1.0.0",
            "description": "无人机交通警察系统",
            "components": info.get("components", {}),
            "capabilities": info.get("capabilities", []),
            "startup_time": info.get("startup_time"),
            "uptime": info.get("uptime")
        }
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_system_config(system_manager: SystemManager = Depends(get_system_manager)):
    """获取系统配置"""
    try:
        config = await system_manager.get_config()
        return {
            "success": True,
            "config": config
        }
    except Exception as e:
        logger.error(f"Failed to get system config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config", response_model=SystemConfigResponse)
async def update_system_config(
    request: SystemConfigRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """更新系统配置"""
    try:
        success = await system_manager.update_config(request.config)
        
        if success:
            return SystemConfigResponse(
                success=True,
                message="Configuration updated successfully",
                config=request.config
            )
        else:
            return SystemConfigResponse(
                success=False,
                message="Failed to update configuration",
                config={}
            )
    except Exception as e:
        logger.error(f"Failed to update system config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/start")
async def start_system(system_manager: SystemManager = Depends(get_system_manager)):
    """启动系统"""
    try:
        success = await system_manager.start()
        
        if success:
            return {
                "success": True,
                "message": "System started successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Failed to start system",
                "timestamp": datetime.utcnow().isoformat()
            }
    except Exception as e:
        logger.error(f"Failed to start system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_system(system_manager: SystemManager = Depends(get_system_manager)):
    """停止系统"""
    try:
        success = await system_manager.stop()
        
        if success:
            return {
                "success": True,
                "message": "System stopped successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Failed to stop system",
                "timestamp": datetime.utcnow().isoformat()
            }
    except Exception as e:
        logger.error(f"Failed to stop system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/restart")
async def restart_system(system_manager: SystemManager = Depends(get_system_manager)):
    """重启系统"""
    try:
        success = await system_manager.restart()
        
        if success:
            return {
                "success": True,
                "message": "System restarted successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Failed to restart system",
                "timestamp": datetime.utcnow().isoformat()
            }
    except Exception as e:
        logger.error(f"Failed to restart system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_system_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取系统统计信息"""
    try:
        stats = await system_manager.get_comprehensive_statistics()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats
        }
    except Exception as e:
        logger.error(f"Failed to get system statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance")
async def get_performance_metrics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取性能指标"""
    try:
        performance = await system_manager.get_performance_metrics()
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "performance": performance
        }
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_system_logs(
    level: Optional[str] = None,
    limit: int = 100,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """获取系统日志"""
    try:
        logs = await system_manager.get_logs(level=level, limit=limit)
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "logs": logs
        }
    except Exception as e:
        logger.error(f"Failed to get system logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset")
async def reset_system(system_manager: SystemManager = Depends(get_system_manager)):
    """重置系统"""
    try:
        success = await system_manager.reset()
        
        if success:
            return {
                "success": True,
                "message": "System reset successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Failed to reset system",
                "timestamp": datetime.utcnow().isoformat()
            }
    except Exception as e:
        logger.error(f"Failed to reset system: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics")
async def get_api_metrics(request: Request):
    """获取API指标"""
    try:
        if hasattr(request.app.state, 'metrics_middleware'):
            metrics = request.app.state.metrics_middleware.get_metrics()
            return {
                "success": True,
                "timestamp": datetime.utcnow().isoformat(),
                "metrics": metrics
            }
        else:
            return {
                "success": False,
                "message": "Metrics not available"
            }
    except Exception as e:
        logger.error(f"Failed to get API metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))
