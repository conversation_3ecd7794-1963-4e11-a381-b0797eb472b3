"""
大语言模型API路由
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import io

from core.system import SystemManager


router = APIRouter()
logger = logging.getLogger(__name__)


# 请求/响应模型
class ReportRequest(BaseModel):
    report_type: str  # accident, violation, traffic, alert, decision
    data: Dict[str, Any]


class ReportResponse(BaseModel):
    success: bool
    timestamp: str
    report_type: str
    report_content: str
    generation_time: float


class VoiceRequest(BaseModel):
    text: str
    voice_type: Optional[str] = "announcement"
    urgency_level: Optional[str] = "normal"


class TextAnalysisRequest(BaseModel):
    texts: List[str]


class TextAnalysisResponse(BaseModel):
    success: bool
    timestamp: str
    results: List[Dict[str, Any]]


# 依赖注入
def get_system_manager(request: Request) -> SystemManager:
    """获取系统管理器实例"""
    if not hasattr(request.app.state, 'system_manager'):
        raise HTTPException(status_code=503, detail="System manager not available")
    return request.app.state.system_manager


@router.post("/reports/generate", response_model=ReportResponse)
async def generate_report(
    request: ReportRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """生成报告"""
    try:
        start_time = datetime.utcnow()
        
        # 根据报告类型生成相应报告
        if request.report_type == "accident":
            report_content = await system_manager.llm_engine.report_generator.generate_accident_report(request.data)
        elif request.report_type == "violation":
            report_content = await system_manager.llm_engine.report_generator.generate_violation_report(request.data)
        elif request.report_type == "traffic":
            report_content = await system_manager.llm_engine.report_generator.generate_traffic_analysis(request.data)
        elif request.report_type == "alert":
            report_content = await system_manager.llm_engine.report_generator.generate_safety_alert(request.data)
        elif request.report_type == "decision":
            report_content = await system_manager.llm_engine.report_generator.generate_decision_summary(request.data)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported report type: {request.report_type}")
        
        generation_time = (datetime.utcnow() - start_time).total_seconds()
        
        return ReportResponse(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            report_type=request.report_type,
            report_content=report_content,
            generation_time=generation_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to generate report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/voice/synthesize")
async def synthesize_voice(
    request: VoiceRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """合成语音"""
    try:
        audio_data = await system_manager.llm_engine.voice_synthesizer.synthesize_text(request.text)
        
        if audio_data:
            # 返回音频流
            audio_stream = io.BytesIO(audio_data)
            return StreamingResponse(
                audio_stream,
                media_type="audio/wav",
                headers={"Content-Disposition": "attachment; filename=synthesized_voice.wav"}
            )
        else:
            raise HTTPException(status_code=500, detail="Voice synthesis failed")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to synthesize voice: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/voice/announcement")
async def synthesize_announcement(
    announcement_type: str,
    content: str,
    urgency_level: str = "normal",
    system_manager: SystemManager = Depends(get_system_manager)
):
    """合成交通播报"""
    try:
        audio_data = await system_manager.llm_engine.voice_synthesizer.synthesize_announcement(
            announcement_type, content, urgency_level
        )
        
        if audio_data:
            audio_stream = io.BytesIO(audio_data)
            return StreamingResponse(
                audio_stream,
                media_type="audio/wav",
                headers={"Content-Disposition": f"attachment; filename={announcement_type}_announcement.wav"}
            )
        else:
            raise HTTPException(status_code=500, detail="Announcement synthesis failed")
            
    except Exception as e:
        logger.error(f"Failed to synthesize announcement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/text/analyze", response_model=TextAnalysisResponse)
async def analyze_text(
    request: TextAnalysisRequest,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """分析文本"""
    try:
        results = await system_manager.llm_engine.analyze_text_content(request.texts)
        
        # 转换结果为字典格式
        analysis_results = [result.to_dict() for result in results]
        
        return TextAnalysisResponse(
            success=True,
            timestamp=datetime.utcnow().isoformat(),
            results=analysis_results
        )
        
    except Exception as e:
        logger.error(f"Failed to analyze text: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/voice/voices")
async def get_available_voices(system_manager: SystemManager = Depends(get_system_manager)):
    """获取可用的语音列表"""
    try:
        voices = system_manager.llm_engine.voice_synthesizer.get_available_voices()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "voices": voices
        }
        
    except Exception as e:
        logger.error(f"Failed to get available voices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/voice/set-voice")
async def set_voice(
    voice_name: str,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """设置语音"""
    try:
        system_manager.llm_engine.voice_synthesizer.set_voice(voice_name)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "voice_name": voice_name,
            "message": "Voice set successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to set voice: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_llm_statistics(system_manager: SystemManager = Depends(get_system_manager)):
    """获取LLM统计信息"""
    try:
        stats = await system_manager.llm_engine.get_comprehensive_statistics()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get LLM statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reports/batch")
async def generate_batch_reports(
    traffic_data: Optional[Dict[str, Any]] = None,
    accidents: Optional[List[Dict[str, Any]]] = None,
    violations: Optional[List[Dict[str, Any]]] = None,
    alerts: Optional[List[Dict[str, Any]]] = None,
    decisions: Optional[List[Dict[str, Any]]] = None,
    system_manager: SystemManager = Depends(get_system_manager)
):
    """批量生成报告"""
    try:
        reports = await system_manager.llm_engine.generate_comprehensive_report(
            traffic_data or {},
            accidents or [],
            violations or [],
            alerts or [],
            decisions or []
        )
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "reports": reports,
            "report_count": len(reports)
        }
        
    except Exception as e:
        logger.error(f"Failed to generate batch reports: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/voice/batch")
async def synthesize_batch_announcements(
    announcements: List[Dict[str, Any]],
    system_manager: SystemManager = Depends(get_system_manager)
):
    """批量合成语音播报"""
    try:
        voice_data = await system_manager.llm_engine.synthesize_voice_announcements(announcements)
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "voice_files": list(voice_data.keys()),
            "synthesis_count": len(voice_data)
        }
        
    except Exception as e:
        logger.error(f"Failed to synthesize batch announcements: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/smart-announcement")
async def generate_smart_announcement(
    event_type: str,
    event_data: Dict[str, Any],
    target_audience: str = "drivers",
    system_manager: SystemManager = Depends(get_system_manager)
):
    """生成智能播报"""
    try:
        audio_data = await system_manager.llm_engine.generate_smart_announcement(
            event_type, event_data, target_audience
        )
        
        if audio_data:
            audio_stream = io.BytesIO(audio_data)
            return StreamingResponse(
                audio_stream,
                media_type="audio/wav",
                headers={"Content-Disposition": f"attachment; filename=smart_{event_type}_announcement.wav"}
            )
        else:
            raise HTTPException(status_code=500, detail="Smart announcement generation failed")
            
    except Exception as e:
        logger.error(f"Failed to generate smart announcement: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reset")
async def reset_llm_engine(system_manager: SystemManager = Depends(get_system_manager)):
    """重置LLM引擎"""
    try:
        await system_manager.llm_engine.reset()
        
        return {
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "LLM engine reset successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to reset LLM engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))
