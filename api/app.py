"""
FastAPI应用程序
主要的API应用程序入口
"""
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

from config import api_config
from core.system import SystemManager
from .routes import (
    system_router, 
    detection_router, 
    tracking_router,
    analysis_router,
    data_router,
    llm_router
)
from .websocket import websocket_router
from .middleware import setup_middleware


# 全局系统管理器实例
system_manager: SystemManager = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    global system_manager
    
    # 启动时初始化
    try:
        logging.info("Initializing system manager...")
        system_manager = SystemManager()
        success = await system_manager.initialize()
        
        if not success:
            logging.error("Failed to initialize system manager")
            raise RuntimeError("System initialization failed")
        
        logging.info("System manager initialized successfully")
        
        # 将系统管理器添加到应用状态
        app.state.system_manager = system_manager
        
        yield
        
    except Exception as e:
        logging.error(f"Error during startup: {e}")
        raise
    
    finally:
        # 关闭时清理
        try:
            if system_manager:
                logging.info("Shutting down system manager...")
                await system_manager.shutdown()
                logging.info("System manager shut down successfully")
        except Exception as e:
            logging.error(f"Error during shutdown: {e}")


def create_app(config: Dict[str, Any] = None) -> FastAPI:
    """创建FastAPI应用程序"""
    
    # 获取配置
    app_config = config or api_config.get_api_config()
    
    # 创建FastAPI实例
    app = FastAPI(
        title="Air Traffic Police API",
        description="无人机交通警察系统API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # 设置中间件
    setup_middleware(app, app_config)
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=app_config.get("cors_origins", ["*"]),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加Gzip压缩中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 注册路由
    app.include_router(system_router, prefix="/api/v1/system", tags=["系统管理"])
    app.include_router(detection_router, prefix="/api/v1/detection", tags=["目标检测"])
    app.include_router(tracking_router, prefix="/api/v1/tracking", tags=["目标跟踪"])
    app.include_router(analysis_router, prefix="/api/v1/analysis", tags=["业务分析"])
    app.include_router(data_router, prefix="/api/v1/data", tags=["数据管理"])
    app.include_router(llm_router, prefix="/api/v1/llm", tags=["大模型服务"])
    app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])
    
    # 静态文件服务
    try:
        app.mount("/static", StaticFiles(directory="static"), name="static")
    except RuntimeError:
        # 静态文件目录不存在时忽略
        pass
    
    # 全局异常处理器
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "status_code": exc.status_code,
                "path": str(request.url)
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logging.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": True,
                "message": "Internal server error",
                "status_code": 500,
                "path": str(request.url)
            }
        )
    
    # 根路径
    @app.get("/")
    async def root():
        return {
            "message": "Air Traffic Police API",
            "version": "1.0.0",
            "status": "running",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    
    # 健康检查
    @app.get("/health")
    async def health_check():
        try:
            if hasattr(app.state, 'system_manager') and app.state.system_manager:
                health_status = await app.state.system_manager.get_health_status()
                return {
                    "status": "healthy" if health_status.get("overall_status") == "healthy" else "unhealthy",
                    "timestamp": health_status.get("timestamp"),
                    "details": health_status
                }
            else:
                return {
                    "status": "unhealthy",
                    "message": "System manager not initialized"
                }
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": str(e)
            }
    
    # API信息
    @app.get("/api/v1/info")
    async def api_info():
        return {
            "api_version": "1.0.0",
            "system_name": "Air Traffic Police",
            "description": "无人机交通警察系统",
            "endpoints": {
                "system": "/api/v1/system",
                "detection": "/api/v1/detection",
                "tracking": "/api/v1/tracking",
                "analysis": "/api/v1/analysis",
                "data": "/api/v1/data",
                "llm": "/api/v1/llm",
                "websocket": "/ws"
            },
            "documentation": {
                "swagger": "/docs",
                "redoc": "/redoc",
                "openapi": "/openapi.json"
            }
        }
    
    return app


def run_server(host: str = "0.0.0.0", port: int = 8000, **kwargs):
    """运行服务器"""
    config = uvicorn.Config(
        "api.app:create_app",
        factory=True,
        host=host,
        port=port,
        log_level="info",
        **kwargs
    )
    server = uvicorn.Server(config)
    server.run()


if __name__ == "__main__":
    # 直接运行时的配置
    import argparse
    
    parser = argparse.ArgumentParser(description="Air Traffic Police API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 运行服务器
    run_server(
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers
    )
