#!/usr/bin/env python3
"""
无人机交通警察系统 - 可视化演示界面
提供图形化界面展示系统功能
"""

import sys
import asyncio
import threading
import time
from pathlib import Path
import cv2
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox
    from PIL import Image, ImageTk
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("GUI库未安装，将使用Web界面模式")

from core.system import SystemManager

class TrafficPoliceDemo:
    """交通警察系统演示界面"""
    
    def __init__(self):
        self.system_manager = None
        self.running = False
        self.current_frame = None
        self.detection_results = []
        
        if GUI_AVAILABLE:
            self.setup_gui()
        else:
            self.setup_console()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("🚁 无人机交通警察系统 - 演示界面")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # 创建主框架
        self.create_main_frame()
        self.create_control_panel()
        self.create_video_panel()
        self.create_info_panel()
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建主框架"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#34495e', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🚁 无人机交通警察系统", 
            font=('Arial', 20, 'bold'),
            fg='white', 
            bg='#34495e'
        )
        title_label.pack(expand=True)
        
        # 主内容框架
        self.main_frame = tk.Frame(self.root, bg='#2c3e50')
        self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
    
    def create_control_panel(self):
        """创建控制面板"""
        control_frame = tk.Frame(self.main_frame, bg='#34495e', width=200)
        control_frame.pack(side='left', fill='y', padx=(0, 5))
        control_frame.pack_propagate(False)
        
        # 控制按钮
        tk.Label(control_frame, text="系统控制", font=('Arial', 14, 'bold'), 
                fg='white', bg='#34495e').pack(pady=10)
        
        self.start_btn = tk.Button(
            control_frame, text="🚀 启动系统", 
            command=self.start_system,
            bg='#27ae60', fg='white', font=('Arial', 12),
            width=15, height=2
        )
        self.start_btn.pack(pady=5)
        
        self.stop_btn = tk.Button(
            control_frame, text="⏹️ 停止系统", 
            command=self.stop_system,
            bg='#e74c3c', fg='white', font=('Arial', 12),
            width=15, height=2, state='disabled'
        )
        self.stop_btn.pack(pady=5)
        
        # 演示选项
        tk.Label(control_frame, text="演示选项", font=('Arial', 12, 'bold'), 
                fg='white', bg='#34495e').pack(pady=(20, 10))
        
        self.demo_var = tk.StringVar(value="video")
        demo_options = [
            ("📹 视频演示", "video"),
            ("📷 图片演示", "image"),
            ("🎥 摄像头演示", "camera"),
            ("📊 数据演示", "data")
        ]
        
        for text, value in demo_options:
            tk.Radiobutton(
                control_frame, text=text, variable=self.demo_var, value=value,
                fg='white', bg='#34495e', selectcolor='#2c3e50',
                font=('Arial', 10)
            ).pack(anchor='w', padx=10)
        
        # 文件选择
        tk.Button(
            control_frame, text="📁 选择文件", 
            command=self.select_file,
            bg='#3498db', fg='white', font=('Arial', 10),
            width=15
        ).pack(pady=10)
        
        # 系统信息
        tk.Label(control_frame, text="系统信息", font=('Arial', 12, 'bold'), 
                fg='white', bg='#34495e').pack(pady=(20, 10))
        
        self.info_text = tk.Text(
            control_frame, height=8, width=25, 
            bg='#2c3e50', fg='white', font=('Courier', 9)
        )
        self.info_text.pack(pady=5, padx=5, fill='both', expand=True)
    
    def create_video_panel(self):
        """创建视频显示面板"""
        video_frame = tk.Frame(self.main_frame, bg='#34495e')
        video_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        # 视频标题
        tk.Label(video_frame, text="实时检测画面", font=('Arial', 14, 'bold'), 
                fg='white', bg='#34495e').pack(pady=5)
        
        # 视频显示区域
        self.video_label = tk.Label(video_frame, bg='black', text="等待视频输入...", 
                                   fg='white', font=('Arial', 16))
        self.video_label.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 检测统计
        stats_frame = tk.Frame(video_frame, bg='#34495e', height=60)
        stats_frame.pack(fill='x', padx=5, pady=5)
        stats_frame.pack_propagate(False)
        
        self.stats_label = tk.Label(
            stats_frame, text="检测统计: 等待启动...", 
            font=('Arial', 12), fg='white', bg='#34495e'
        )
        self.stats_label.pack(expand=True)
    
    def create_info_panel(self):
        """创建信息面板"""
        info_frame = tk.Frame(self.main_frame, bg='#34495e', width=300)
        info_frame.pack(side='right', fill='y')
        info_frame.pack_propagate(False)
        
        # 检测结果
        tk.Label(info_frame, text="检测结果", font=('Arial', 14, 'bold'), 
                fg='white', bg='#34495e').pack(pady=5)
        
        self.results_text = tk.Text(
            info_frame, height=15, width=35,
            bg='#2c3e50', fg='white', font=('Courier', 9)
        )
        self.results_text.pack(pady=5, padx=5, fill='both')
        
        # 系统日志
        tk.Label(info_frame, text="系统日志", font=('Arial', 14, 'bold'), 
                fg='white', bg='#34495e').pack(pady=(10, 5))
        
        self.log_text = tk.Text(
            info_frame, height=10, width=35,
            bg='#2c3e50', fg='#00ff00', font=('Courier', 8)
        )
        self.log_text.pack(pady=5, padx=5, fill='both', expand=True)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = tk.Frame(self.root, bg='#34495e', height=30)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_bar, text="系统就绪", 
            font=('Arial', 10), fg='white', bg='#34495e'
        )
        self.status_label.pack(side='left', padx=10, expand=True)
        
        self.fps_label = tk.Label(
            self.status_bar, text="FPS: 0", 
            font=('Arial', 10), fg='white', bg='#34495e'
        )
        self.fps_label.pack(side='right', padx=10)
    
    def setup_console(self):
        """设置控制台模式"""
        print("🚁 无人机交通警察系统 - 控制台演示模式")
        print("=" * 50)
        print("GUI库未安装，使用控制台模式")
        print("建议安装: pip install tkinter pillow")
    
    def start_system(self):
        """启动系统"""
        if self.running:
            return
        
        self.log("🚀 正在启动系统...")
        self.start_btn.config(state='disabled')
        
        # 在后台线程中启动系统
        threading.Thread(target=self._start_system_async, daemon=True).start()
    
    def _start_system_async(self):
        """异步启动系统"""
        try:
            # 创建系统管理器
            self.system_manager = SystemManager()
            
            # 初始化系统
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            success = loop.run_until_complete(self.system_manager.initialize())
            
            if success:
                self.running = True
                self.log("✅ 系统启动成功")
                self.update_status("系统运行中")
                
                # 更新按钮状态
                self.root.after(0, lambda: [
                    self.start_btn.config(state='disabled'),
                    self.stop_btn.config(state='normal')
                ])
                
                # 开始演示
                self.start_demo()
            else:
                self.log("❌ 系统启动失败")
                self.start_btn.config(state='normal')
                
        except Exception as e:
            self.log(f"❌ 启动错误: {e}")
            self.start_btn.config(state='normal')
    
    def stop_system(self):
        """停止系统"""
        if not self.running:
            return
        
        self.log("⏹️ 正在停止系统...")
        self.running = False
        
        if self.system_manager:
            threading.Thread(target=self._stop_system_async, daemon=True).start()
    
    def _stop_system_async(self):
        """异步停止系统"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.system_manager:
                loop.run_until_complete(self.system_manager.shutdown())
            
            self.log("✅ 系统已停止")
            self.update_status("系统已停止")
            
            # 更新按钮状态
            self.root.after(0, lambda: [
                self.start_btn.config(state='normal'),
                self.stop_btn.config(state='disabled')
            ])
            
        except Exception as e:
            self.log(f"❌ 停止错误: {e}")
    
    def start_demo(self):
        """开始演示"""
        demo_type = self.demo_var.get()
        
        if demo_type == "video":
            self.demo_video()
        elif demo_type == "image":
            self.demo_image()
        elif demo_type == "camera":
            self.demo_camera()
        elif demo_type == "data":
            self.demo_data()
    
    def demo_video(self):
        """视频演示"""
        video_path = "data/videos/action1.mp4"
        if not Path(video_path).exists():
            self.log("⚠️ 演示视频不存在，使用模拟数据")
            self.demo_simulation()
            return
        
        self.log(f"📹 开始视频演示: {video_path}")
        threading.Thread(target=self._process_video, args=(video_path,), daemon=True).start()
    
    def demo_simulation(self):
        """模拟演示"""
        self.log("🎭 开始模拟演示")
        threading.Thread(target=self._simulation_loop, daemon=True).start()
    
    def _simulation_loop(self):
        """模拟演示循环"""
        frame_count = 0
        while self.running:
            # 创建模拟图像
            img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # 添加一些模拟检测框
            cv2.rectangle(img, (100, 100), (200, 200), (0, 255, 0), 2)
            cv2.putText(img, "Car: 0.85", (100, 95), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            cv2.rectangle(img, (300, 150), (380, 250), (255, 0, 0), 2)
            cv2.putText(img, "Person: 0.92", (300, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
            
            # 更新显示
            self.update_video_display(img)
            
            # 更新统计
            frame_count += 1
            self.update_stats(f"帧数: {frame_count} | 检测: 2 | FPS: 30")
            
            # 模拟检测结果
            if frame_count % 30 == 0:  # 每秒更新一次
                self.add_detection_result(f"检测到车辆 (置信度: 0.85)")
                self.add_detection_result(f"检测到行人 (置信度: 0.92)")
            
            time.sleep(1/30)  # 30 FPS
    
    def update_video_display(self, img):
        """更新视频显示"""
        if not GUI_AVAILABLE:
            return
        
        try:
            # 转换为PIL图像
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_pil = Image.fromarray(img_rgb)
            
            # 调整大小适应显示区域
            display_size = (640, 480)
            img_pil = img_pil.resize(display_size, Image.Resampling.LANCZOS)
            
            # 转换为Tkinter格式
            img_tk = ImageTk.PhotoImage(img_pil)
            
            # 更新显示
            self.root.after(0, lambda: self.video_label.config(image=img_tk))
            self.video_label.image = img_tk  # 保持引用
            
        except Exception as e:
            self.log(f"显示更新错误: {e}")
    
    def select_file(self):
        """选择文件"""
        if not GUI_AVAILABLE:
            return
        
        file_path = filedialog.askopenfilename(
            title="选择演示文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov"),
                ("图片文件", "*.jpg *.jpeg *.png"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.log(f"📁 选择文件: {Path(file_path).name}")
    
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        if GUI_AVAILABLE and hasattr(self, 'log_text'):
            self.root.after(0, lambda: [
                self.log_text.insert(tk.END, log_message),
                self.log_text.see(tk.END)
            ])
        else:
            print(log_message.strip())
    
    def update_status(self, status):
        """更新状态"""
        if GUI_AVAILABLE and hasattr(self, 'status_label'):
            self.root.after(0, lambda: self.status_label.config(text=status))
    
    def update_stats(self, stats):
        """更新统计信息"""
        if GUI_AVAILABLE and hasattr(self, 'stats_label'):
            self.root.after(0, lambda: self.stats_label.config(text=stats))
    
    def add_detection_result(self, result):
        """添加检测结果"""
        timestamp = time.strftime("%H:%M:%S")
        result_message = f"[{timestamp}] {result}\n"
        
        if GUI_AVAILABLE and hasattr(self, 'results_text'):
            self.root.after(0, lambda: [
                self.results_text.insert(tk.END, result_message),
                self.results_text.see(tk.END)
            ])
    
    def run(self):
        """运行演示"""
        if GUI_AVAILABLE:
            self.log("🎯 演示界面已启动")
            self.log("💡 点击'启动系统'开始演示")
            self.root.mainloop()
        else:
            self.run_console_demo()
    
    def run_console_demo(self):
        """运行控制台演示"""
        print("\n🎮 控制台演示模式")
        print("1. 启动系统")
        print("2. 运行模拟检测")
        print("3. 退出")
        
        while True:
            choice = input("\n请选择操作 (1-3): ").strip()
            
            if choice == "1":
                print("🚀 启动系统...")
                # 这里可以添加系统启动逻辑
                print("✅ 系统启动成功")
                
            elif choice == "2":
                print("🎭 运行模拟检测...")
                for i in range(10):
                    print(f"帧 {i+1}: 检测到 2 个目标 (车辆: 1, 行人: 1)")
                    time.sleep(0.5)
                print("✅ 模拟检测完成")
                
            elif choice == "3":
                print("👋 退出演示")
                break
            else:
                print("❌ 无效选择")

def main():
    """主函数"""
    print("🚁 无人机交通警察系统 - 可视化演示")
    
    try:
        demo = TrafficPoliceDemo()
        demo.run()
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"❌ 演示运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
