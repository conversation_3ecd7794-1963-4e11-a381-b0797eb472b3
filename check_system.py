#!/usr/bin/env python3
"""
系统配置检查脚本
检查系统环境、依赖和配置是否正确
"""

import sys
import os
import platform
import subprocess
import importlib
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_status(item, status, details=""):
    """打印状态"""
    status_symbol = "✅" if status else "❌"
    print(f"{status_symbol} {item:<30} {details}")

def check_system_info():
    """检查系统信息"""
    print_header("系统信息")
    
    # 操作系统
    os_name = platform.system()
    os_version = platform.release()
    print_status("操作系统", True, f"{os_name} {os_version}")
    
    # CPU架构
    architecture = platform.machine()
    print_status("CPU架构", True, architecture)
    
    # Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    python_ok = sys.version_info >= (3, 8)
    print_status("Python版本", python_ok, python_version)
    
    # 内存信息
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        memory_ok = memory_gb >= 8
        print_status("系统内存", memory_ok, f"{memory_gb:.1f} GB")
    except ImportError:
        print_status("系统内存", False, "无法检测 (需要psutil)")
    
    # 磁盘空间
    try:
        import shutil
        disk_space = shutil.disk_usage('.').free / (1024**3)
        disk_ok = disk_space >= 20
        print_status("可用磁盘空间", disk_ok, f"{disk_space:.1f} GB")
    except:
        print_status("可用磁盘空间", False, "无法检测")

def check_gpu_support():
    """检查GPU支持"""
    print_header("GPU支持检查")
    
    # 检查NVIDIA驱动
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        nvidia_ok = result.returncode == 0
        if nvidia_ok:
            # 提取GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'NVIDIA' in line and 'Driver Version' in line:
                    driver_info = line.strip()
                    print_status("NVIDIA驱动", True, "已安装")
                    break
        else:
            print_status("NVIDIA驱动", False, "未安装或不可用")
    except FileNotFoundError:
        print_status("NVIDIA驱动", False, "nvidia-smi未找到")
    
    # 检查CUDA
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        cuda_ok = result.returncode == 0
        if cuda_ok:
            # 提取CUDA版本
            for line in result.stdout.split('\n'):
                if 'release' in line:
                    cuda_version = line.split('release')[1].split(',')[0].strip()
                    print_status("CUDA工具包", True, f"版本 {cuda_version}")
                    break
        else:
            print_status("CUDA工具包", False, "未安装")
    except FileNotFoundError:
        print_status("CUDA工具包", False, "nvcc未找到")
    
    # 检查PyTorch CUDA支持
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        if cuda_available:
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
            print_status("PyTorch CUDA", True, f"{device_count}个设备, {device_name}")
        else:
            print_status("PyTorch CUDA", False, "CUDA不可用")
    except ImportError:
        print_status("PyTorch CUDA", False, "PyTorch未安装")

def check_dependencies():
    """检查依赖包"""
    print_header("依赖包检查")
    
    required_packages = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'ultralytics': 'YOLOv8',
        'cv2': 'OpenCV',
        'fastapi': 'FastAPI',
        'uvicorn': 'Uvicorn',
        'websockets': 'WebSockets',
        'pydantic': 'Pydantic',
        'sqlalchemy': 'SQLAlchemy',
        'aiosqlite': 'AsyncSQLite',
        'numpy': 'NumPy',
        'PIL': 'Pillow',
        'requests': 'Requests'
    }
    
    for package, name in required_packages.items():
        try:
            if package == 'cv2':
                import cv2
                version = cv2.__version__
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'Unknown')
            print_status(name, True, f"版本 {version}")
        except ImportError:
            print_status(name, False, "未安装")

def check_project_structure():
    """检查项目结构"""
    print_header("项目结构检查")
    
    required_dirs = [
        'core',
        'config', 
        'api',
        'models',
        'data',
        'logs'
    ]
    
    required_files = [
        'main.py',
        'start.py',
        'test_final.py',
        'requirements.txt',
        'config/ai_config.py',
        'config/api_config.py',
        'core/system.py',
        'core/ai/ai_engine.py'
    ]
    
    # 检查目录
    for dir_name in required_dirs:
        exists = os.path.isdir(dir_name)
        print_status(f"目录 {dir_name}/", exists)
    
    # 检查文件
    for file_name in required_files:
        exists = os.path.isfile(file_name)
        print_status(f"文件 {file_name}", exists)

def check_models():
    """检查模型文件"""
    print_header("模型文件检查")
    
    model_files = [
        'models/yolov8s.pt',
        'models/yolov8m.pt',
        'models/yolov8l.pt'
    ]
    
    for model_file in model_files:
        exists = os.path.isfile(model_file)
        if exists:
            size_mb = os.path.getsize(model_file) / (1024*1024)
            print_status(f"模型 {os.path.basename(model_file)}", True, f"{size_mb:.1f} MB")
        else:
            print_status(f"模型 {os.path.basename(model_file)}", False, "未找到")

def check_configuration():
    """检查配置文件"""
    print_header("配置文件检查")
    
    try:
        from config.ai_config import AIConfig
        ai_config = AIConfig()
        print_status("AI配置", True, f"模型: {ai_config.MODEL_TYPE}")
    except Exception as e:
        print_status("AI配置", False, f"错误: {e}")
    
    try:
        from config.api_config import APIConfig
        api_config = APIConfig()
        print_status("API配置", True, f"端口: {api_config.PORT}")
    except Exception as e:
        print_status("API配置", False, f"错误: {e}")
    
    try:
        from config.hardware_config import HardwareConfig
        hw_config = HardwareConfig()
        print_status("硬件配置", True, f"摄像头: {hw_config.CAMERA_COUNT}")
    except Exception as e:
        print_status("硬件配置", False, f"错误: {e}")

def run_basic_tests():
    """运行基础测试"""
    print_header("基础功能测试")
    
    # 测试YOLO模型加载
    try:
        from ultralytics import YOLO
        model = YOLO('models/yolov8s.pt')
        print_status("YOLO模型加载", True, "成功")
    except Exception as e:
        print_status("YOLO模型加载", False, f"错误: {e}")
    
    # 测试数据库连接
    try:
        from core.data.database import Database
        import asyncio
        
        async def test_db():
            db = Database()
            return await db.initialize()
        
        result = asyncio.run(test_db())
        print_status("数据库连接", result, "SQLite")
    except Exception as e:
        print_status("数据库连接", False, f"错误: {e}")
    
    # 测试API导入
    try:
        from api.app import create_app
        app = create_app()
        print_status("API应用", True, "FastAPI")
    except Exception as e:
        print_status("API应用", False, f"错误: {e}")

def generate_report():
    """生成检查报告"""
    print_header("系统检查完成")
    
    print("\n📋 检查总结:")
    print("1. 如果所有项目都显示 ✅，说明系统配置正确")
    print("2. 如果有 ❌ 项目，请根据错误信息进行修复")
    print("3. 运行 'python test_final.py' 进行完整系统测试")
    print("4. 查看 'DEPLOYMENT_GUIDE.md' 获取详细部署指南")
    
    print("\n🚀 下一步:")
    print("- 启动演示模式: python main.py --mode demo")
    print("- 启动API服务: python main.py --mode api") 
    print("- 查看API文档: http://localhost:8000/docs")

def main():
    """主函数"""
    print("🔍 无人机交通警察系统 - 配置检查工具")
    print("此工具将检查系统环境、依赖和配置是否正确")
    
    try:
        check_system_info()
        check_gpu_support()
        check_dependencies()
        check_project_structure()
        check_models()
        check_configuration()
        run_basic_tests()
        generate_report()
        
    except KeyboardInterrupt:
        print("\n\n❌ 检查被用户中断")
    except Exception as e:
        print(f"\n\n❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
