[Learn](https://learn.microsoft.com/en-us/) [Azure](https://learn.microsoft.com/en-us/azure/index/) [AI Foundry](https://learn.microsoft.com/en-us/azure/ai-foundry/) [Speech Service](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/) 

# Quickstart: Install the Speech SDK

- 03/13/2025

Choose a programming language or tool

C#C++GoJavaJavaScriptObjective-CPythonSwift

[Reference documentation](https://learn.microsoft.com/en-us/python/api/azure-cognitiveservices-speech/) | [Package (PyPi)](https://pypi.org/project/azure-cognitiveservices-speech/) | [Additional samples on GitHub](https://aka.ms/speech/github-python)

In this quickstart, you install the [Speech SDK](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/speech-sdk) for Python.



## Platform requirements

The Speech SDK for Python is compatible with Windows, Linux, and macOS.

- [Windows](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_windows)
- [Linux](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_linux)
- [macOS](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_macos)

The Speech SDK for Python supports the following distributions on the x64 and ARM64 architectures:

- Ubuntu 20.04/22.04/24.04
- Debian 11/12
- Amazon Linux 2023
- Azure Linux 3.0

 Important

Use the most recent LTS release of the Linux distribution. For example, if you are using Ubuntu 20.04 LTS, use the latest release of Ubuntu 20.04.X.

The Speech SDK depends on the following Linux system libraries:

- The shared libraries of the GNU C library, including the POSIX Threads Programming library, `libpthreads`.
- The OpenSSL library, version 1.x (`libssl1`) or 3.x (`libssl3`), and certificates (`ca-certificates`).
- The shared library for ALSA applications (`libasound2`).

- [Ubuntu 20.04/22.04/24.04](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_ubuntu)
- [Debian 11/12](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_debian)
- [Amazon Linux 2023](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_amazon)
- [Azure Linux 3.0](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_azure)

Run these commands:

Bash

```Bash
sudo apt-get update
sudo apt-get install build-essential ca-certificates libasound2-dev libssl-dev wget
```

Install a version of [Python from 3.8 or later](https://www.python.org/downloads/).

- To check your installation, open a terminal and run the command `python --version`. If Python installed properly, you get a response like `Python 3.8.10`.

- If you're using macOS or Linux, you might need to run the command `python3 --version` instead.

  To enable use of `python` instead of `python3`, run `alias python='python3'` to set up an alias. The Speech SDK quickstart samples specify `python` usage.



## Install the Speech SDK for Python

Before you install the Speech SDK for Python, make sure to satisfy the [platform requirements](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#platform-requirements).

- [PyPI](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_pypi)
- [VS Code](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/quickstarts/setup-platform?pivots=programming-language-python&tabs=linux%2Cubuntu%2Cdotnetcli%2Cjre%2Cmaven%2Cnodejs%2Cmac%2Cpypi#tabpanel_1_vscode)



### Install from PyPI

To install the Speech SDK for Python, run this command in a console window:

Console

```console
pip install azure-cognitiveservices-speech
```



### Upgrade to the latest Speech SDK

To upgrade to the latest Speech SDK, run this command in console window:

Console

```console
pip install --upgrade azure-cognitiveservices-speech
```

You can check which Speech SDK for Python version is currently installed by inspecting the `azure.cognitiveservices.speech.__version__` variable. For example, run this command in a console window:

Console

```console
pip list
```



## Use the Speech SDK

Add the following import statement to use the Speech SDK in your Python project:

Python

```python
import azure.cognitiveservices.speech as speechsdk
```



## Code samples

In depth samples are available in the [Azure-Samples/cognitive-services-speech-sdk](https://aka.ms/csspeech/samples) repository on GitHub. There are samples for C# (including UWP and Unity), C++, Java, JavaScript (including Browser and Node.js), Objective-C, Python, and Swift. Code samples for Go are available in the [Microsoft/cognitive-services-speech-sdk-go](https://github.com/Microsoft/cognitive-services-speech-sdk-go) repository on GitHub.



## Related content

- [Speech to text quickstart](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/get-started-speech-to-text)
- [Text to speech quickstart](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/get-started-text-to-speech)
- [Speech translation quickstart](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/get-started-speech-translation)

------

## Feedback

Was this page helpful?

YesNo

[Provide product feedback](https://feedback.azure.com/d365community/forum/09041fae-0b25-ec11-b6e6-000d3a4f0858?c=21041fae-0b25-ec11-b6e6-000d3a4f0858)|[Get help at Microsoft Q&A](https://learn.microsoft.com/answers/tags/55/azure-speech/)

------

## Additional resources

Documentation

- [Generate a REST API client library - Speech service - Azure AI services](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/swagger-documentation?source=recommendations)

  The Swagger documentation can be used to auto-generate SDKs for many programming languages.

- [About the Speech SDK - Speech service - Azure AI services](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/speech-sdk?source=recommendations)

  The Speech software development kit (SDK) exposes many of the Speech service capabilities, making it easier to develop speech-enabled applications.

- [Speech to text quickstart - Azure AI services](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/get-started-speech-to-text?source=recommendations)

  In this quickstart, learn how to use the Speech service for real-time speech to text conversion.

Show 2 more

Training

Module

[Add chat to a Microsoft Teams app by using the Teams JavaScript client library - Training](https://learn.microsoft.com/en-us/training/modules/teams-toolkit-vsc-integrate-chat/?source=recommendations)

The Microsoft Teams JavaScript client library can help you integrate native Teams features right in your application. In this module, you'll learn how to integrate the Teams chat capability in your app by using the Teams JavaScript client library.