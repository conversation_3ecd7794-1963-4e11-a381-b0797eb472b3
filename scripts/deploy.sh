#!/bin/bash
# 无人机交通警察系统部署脚本
# 支持开发环境、测试环境和生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
无人机交通警察系统部署脚本

用法: $0 [选项] <环境>

环境:
    dev         开发环境
    test        测试环境
    prod        生产环境

选项:
    -h, --help              显示帮助信息
    -v, --verbose           详细输出
    -f, --force             强制重新部署
    --skip-deps             跳过依赖安装
    --skip-models           跳过模型下载
    --docker                使用Docker部署
    --k8s                   使用Kubernetes部署

示例:
    $0 dev                  部署开发环境
    $0 prod --docker        使用Docker部署生产环境
    $0 test --k8s           使用Kubernetes部署测试环境

EOF
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_success "操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        log_success "操作系统: macOS"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查Python版本
    if command -v python3.9 &> /dev/null; then
        PYTHON_CMD="python3.9"
    elif command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        if [[ $(echo "$PYTHON_VERSION 3.8" | awk '{print ($1 >= $2)}') == 1 ]]; then
            log_success "Python版本: $PYTHON_VERSION"
        else
            log_error "Python版本过低，需要3.8+，当前: $PYTHON_VERSION"
            exit 1
        fi
    else
        log_error "未找到Python 3.8+，请先安装Python"
        exit 1
    fi
    
    # 检查GPU (可选)
    if command -v nvidia-smi &> /dev/null; then
        GPU_INFO=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
        log_success "检测到GPU: $GPU_INFO"
        export CUDA_AVAILABLE=true
    else
        log_warning "未检测到NVIDIA GPU，将使用CPU模式"
        export CUDA_AVAILABLE=false
    fi
    
    # 检查内存
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        MEMORY_GB=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
    fi
    
    if [[ $MEMORY_GB -lt 8 ]]; then
        log_warning "内存不足8GB，可能影响性能"
    else
        log_success "内存: ${MEMORY_GB}GB"
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Ubuntu/Debian
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y \
                build-essential \
                cmake \
                git \
                curl \
                wget \
                ffmpeg \
                libopencv-dev \
                libpq-dev \
                redis-server \
                nginx
        # CentOS/RHEL
        elif command -v yum &> /dev/null; then
            sudo yum update -y
            sudo yum install -y \
                gcc \
                gcc-c++ \
                cmake \
                git \
                curl \
                wget \
                ffmpeg \
                opencv-devel \
                postgresql-devel \
                redis \
                nginx
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew update
            brew install \
                cmake \
                git \
                curl \
                wget \
                ffmpeg \
                opencv \
                postgresql \
                redis \
                nginx
        else
            log_error "请先安装Homebrew: https://brew.sh/"
            exit 1
        fi
    fi
    
    log_success "系统依赖安装完成"
}

# 创建Python虚拟环境
setup_python_env() {
    log_info "设置Python环境..."
    
    # 创建虚拟环境
    if [[ ! -d "venv" ]]; then
        $PYTHON_CMD -m venv venv
        log_success "创建虚拟环境"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip setuptools wheel
    
    # 安装PyTorch (根据CUDA可用性)
    if [[ "$CUDA_AVAILABLE" == "true" ]]; then
        log_info "安装CUDA版本的PyTorch..."
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    else
        log_info "安装CPU版本的PyTorch..."
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    fi
    
    # 安装项目依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        log_success "Python依赖安装完成"
    else
        log_error "未找到requirements.txt文件"
        exit 1
    fi
}

# 下载AI模型
download_models() {
    log_info "下载AI模型..."
    
    # 创建模型目录
    mkdir -p models/{yolo,llm,tts}
    
    # 下载YOLO模型
    if [[ ! -f "models/yolov8s.pt" ]]; then
        log_info "下载YOLOv8s模型..."
        wget -O models/yolov8s.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
    fi
    
    if [[ ! -f "models/yolov8n.pt" ]]; then
        log_info "下载YOLOv8n模型..."
        wget -O models/yolov8n.pt https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt
    fi
    
    # 下载本地LLM模型 (可选)
    if [[ "$ENVIRONMENT" == "prod" ]] && [[ ! -f "models/llm/llama-2-7b-chat.q4_0.bin" ]]; then
        log_info "下载LLaMA 2模型..."
        mkdir -p models/llm
        wget -O models/llm/llama-2-7b-chat.q4_0.bin \
            https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGML/resolve/main/llama-2-7b-chat.q4_0.bin
    fi
    
    log_success "模型下载完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # PostgreSQL配置
    if command -v psql &> /dev/null; then
        # 创建数据库和用户
        sudo -u postgres psql << EOF
CREATE DATABASE traffic_police;
CREATE USER traffic_admin WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE traffic_police TO traffic_admin;
\q
EOF
        log_success "PostgreSQL数据库配置完成"
    else
        log_warning "PostgreSQL未安装，跳过数据库配置"
    fi
    
    # Redis配置
    if command -v redis-server &> /dev/null; then
        # 启动Redis服务
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo systemctl start redis
            sudo systemctl enable redis
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            brew services start redis
        fi
        log_success "Redis配置完成"
    else
        log_warning "Redis未安装，跳过Redis配置"
    fi
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 创建环境配置文件
    cat > .env << EOF
# 环境配置
ENVIRONMENT=$ENVIRONMENT
DEBUG=$([ "$ENVIRONMENT" == "dev" ] && echo "true" || echo "false")

# 数据库配置
DATABASE_URL=postgresql://traffic_admin:your_secure_password@localhost:5432/traffic_police
REDIS_URL=redis://localhost:6379

# API密钥 (请替换为实际密钥)
OPENAI_API_KEY=sk-your-openai-api-key-here
AZURE_SPEECH_KEY=your-azure-speech-key
DJI_APP_KEY=your-dji-app-key

# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
WEB_PORT=8080

# 日志配置
LOG_LEVEL=$([ "$ENVIRONMENT" == "dev" ] && echo "DEBUG" || echo "INFO")
LOG_FILE=logs/traffic_police.log
EOF
    
    log_success "环境变量配置完成"
}

# Docker部署
deploy_docker() {
    log_info "使用Docker部署..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker build -t traffic-police:$ENVIRONMENT .
    
    # 启动服务
    log_info "启动Docker服务..."
    docker-compose -f docker-compose.$ENVIRONMENT.yml up -d
    
    log_success "Docker部署完成"
}

# Kubernetes部署
deploy_k8s() {
    log_info "使用Kubernetes部署..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装，请先安装kubectl"
        exit 1
    fi
    
    # 应用配置
    log_info "应用Kubernetes配置..."
    kubectl apply -f k8s/namespace.yaml
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/secret.yaml
    kubectl apply -f k8s/pvc.yaml
    kubectl apply -f k8s/deployment.yaml
    kubectl apply -f k8s/service.yaml
    kubectl apply -f k8s/ingress.yaml
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl rollout status deployment/traffic-police-system -n traffic-police
    
    log_success "Kubernetes部署完成"
}

# 标准部署
deploy_standard() {
    log_info "标准部署..."
    
    # 创建必要目录
    mkdir -p {logs,data/videos,data/reports,temp}
    
    # 设置权限
    chmod +x scripts/*.sh
    
    # 初始化数据库
    if [[ -f "scripts/init_db.py" ]]; then
        source venv/bin/activate
        python scripts/init_db.py
        log_success "数据库初始化完成"
    fi
    
    # 配置系统服务
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        log_info "配置系统服务..."
        
        # 创建systemd服务文件
        sudo tee /etc/systemd/system/traffic-police.service > /dev/null << EOF
[Unit]
Description=Traffic Police System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
Environment=PYTHONPATH=$(pwd)
ExecStart=$(pwd)/venv/bin/python main.py --mode production
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
        
        # 启用服务
        sudo systemctl daemon-reload
        sudo systemctl enable traffic-police
        sudo systemctl start traffic-police
        
        log_success "系统服务配置完成"
    fi
    
    log_success "标准部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if [[ "$USE_DOCKER" == "true" ]]; then
        docker-compose ps
    elif [[ "$USE_K8S" == "true" ]]; then
        kubectl get pods -n traffic-police
    else
        if [[ "$ENVIRONMENT" == "prod" ]]; then
            sudo systemctl status traffic-police
        fi
    fi
    
    # 测试API
    log_info "测试API连接..."
    sleep 5  # 等待服务启动
    
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "API服务正常"
    else
        log_warning "API服务可能未启动"
    fi
    
    # 测试Web界面
    if curl -f http://localhost:8080 &> /dev/null; then
        log_success "Web界面正常"
    else
        log_warning "Web界面可能未启动"
    fi
    
    log_success "部署验证完成"
}

# 主函数
main() {
    # 解析命令行参数
    ENVIRONMENT=""
    VERBOSE=false
    FORCE=false
    SKIP_DEPS=false
    SKIP_MODELS=false
    USE_DOCKER=false
    USE_K8S=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                set -x
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-models)
                SKIP_MODELS=true
                shift
                ;;
            --docker)
                USE_DOCKER=true
                shift
                ;;
            --k8s)
                USE_K8S=true
                shift
                ;;
            dev|test|prod)
                ENVIRONMENT=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [[ -z "$ENVIRONMENT" ]]; then
        log_error "请指定部署环境: dev, test, 或 prod"
        show_help
        exit 1
    fi
    
    log_info "开始部署无人机交通警察系统 - 环境: $ENVIRONMENT"
    
    # 执行部署步骤
    check_requirements
    
    if [[ "$SKIP_DEPS" != "true" ]]; then
        install_system_deps
        setup_python_env
    fi
    
    if [[ "$SKIP_MODELS" != "true" ]]; then
        download_models
    fi
    
    setup_database
    setup_environment
    
    # 选择部署方式
    if [[ "$USE_DOCKER" == "true" ]]; then
        deploy_docker
    elif [[ "$USE_K8S" == "true" ]]; then
        deploy_k8s
    else
        deploy_standard
    fi
    
    verify_deployment
    
    log_success "部署完成！"
    log_info "访问地址:"
    log_info "  - API: http://localhost:8000"
    log_info "  - Web界面: http://localhost:8080"
    log_info "  - API文档: http://localhost:8000/docs"
}

# 执行主函数
main "$@"
