#!/usr/bin/env python3
"""
Qt6警告修复测试脚本
测试是否还有"Unknown property transform"警告
"""

import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_qt6_warnings():
    """测试Qt6界面是否还有警告"""
    print("🔍 测试Qt6界面警告修复...")
    
    try:
        # 启动Qt6演示界面并捕获输出
        process = subprocess.Popen(
            [sys.executable, "demo_qt6.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=str(Path(__file__).parent)
        )
        
        # 等待一段时间让界面启动
        time.sleep(3)
        
        # 终止进程
        process.terminate()
        
        # 获取输出
        stdout, stderr = process.communicate(timeout=5)
        
        # 检查是否有transform警告
        transform_warnings = stderr.count("Unknown property transform")
        
        print(f"📊 检测结果:")
        print(f"   - Transform警告数量: {transform_warnings}")
        
        if transform_warnings == 0:
            print("✅ 没有发现transform警告！修复成功！")
            return True
        else:
            print(f"❌ 仍有 {transform_warnings} 个transform警告")
            print("🔍 错误输出:")
            print(stderr[:500] + "..." if len(stderr) > 500 else stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时，强制终止进程")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_qt6_imports():
    """测试Qt6导入是否正常"""
    print("🔍 测试Qt6导入...")
    
    try:
        from demo_qt6 import QT6_AVAILABLE, ModernButton, StatusCard
        
        if QT6_AVAILABLE:
            print("✅ Qt6导入成功")
            
            # 测试创建组件
            from PyQt6.QtWidgets import QApplication
            app = QApplication([])
            
            # 测试ModernButton
            button = ModernButton("测试按钮", "primary")
            print("✅ ModernButton创建成功")
            
            # 测试StatusCard
            card = StatusCard("测试状态", "正常", "🔧")
            print("✅ StatusCard创建成功")
            
            app.quit()
            return True
        else:
            print("❌ Qt6不可用")
            return False
            
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def check_style_issues():
    """检查样式表中的问题"""
    print("🔍 检查样式表问题...")
    
    try:
        with open("demo_qt6.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否还有transform属性
        transform_count = content.count("transform:")
        
        # 检查是否有其他可能的问题
        issues = []
        
        if transform_count > 0:
            issues.append(f"发现 {transform_count} 个transform属性")
        
        # 检查其他可能不支持的CSS属性
        unsupported_props = [
            "box-shadow:", "text-shadow:", "transition:", 
            "animation:", "opacity:", "filter:"
        ]
        
        for prop in unsupported_props:
            count = content.count(prop)
            if count > 0:
                issues.append(f"发现 {count} 个 {prop.rstrip(':')} 属性")
        
        if issues:
            print("⚠️ 发现潜在问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ 样式表检查通过，没有发现问题")
            return True
            
    except Exception as e:
        print(f"❌ 样式表检查失败: {e}")
        return False

def show_fix_summary():
    """显示修复总结"""
    print("\n🛠️ Qt6警告修复总结:")
    print("=" * 50)
    
    print("🎯 修复的问题:")
    print("   - 移除了不支持的 transform 属性")
    print("   - 清理了不需要的导入")
    print("   - 优化了样式表代码")
    
    print("\n📋 修复内容:")
    print("   1. 移除按钮悬停/按下的 transform 效果")
    print("   2. 移除不需要的 QPropertyAnimation, QEasingCurve 导入")
    print("   3. 移除不需要的 cv2, numpy 导入")
    print("   4. 清理了未使用的 QProgressBar, QGroupBox 等导入")
    
    print("\n✅ 修复效果:")
    print("   - 消除了所有 'Unknown property transform' 警告")
    print("   - 减少了不必要的依赖")
    print("   - 提高了代码质量")
    print("   - 保持了界面功能完整性")

def main():
    """主函数"""
    print("🔧 Qt6界面警告修复测试")
    print("=" * 50)
    
    # 检查样式表问题
    style_check = check_style_issues()
    
    # 测试导入
    import_test = test_qt6_imports()
    
    # 测试警告
    warning_test = test_qt6_warnings()
    
    # 显示修复总结
    show_fix_summary()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"样式表检查: {'✅ 通过' if style_check else '❌ 失败'}")
    print(f"导入测试: {'✅ 通过' if import_test else '❌ 失败'}")
    print(f"警告测试: {'✅ 通过' if warning_test else '❌ 失败'}")
    
    all_passed = style_check and import_test and warning_test
    
    if all_passed:
        print("\n🎉 所有测试通过！Qt6界面警告已修复！")
        print("\n💡 现在可以正常使用Qt6演示界面:")
        print("   python demo_qt6.py")
        print("   (不会再有transform警告)")
    else:
        print("\n❌ 部分测试失败，请检查问题")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
