#!/usr/bin/env python3
"""
简单的演示模式测试
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_system_manager():
    """测试SystemManager"""
    try:
        logger.info("开始测试SystemManager...")
        
        from core.system import SystemManager
        logger.info("✅ SystemManager导入成功")
        
        # 创建系统管理器
        system_manager = SystemManager()
        logger.info("✅ SystemManager创建成功")
        
        # 测试初始化
        logger.info("开始初始化系统...")
        success = await system_manager.initialize()
        
        if success:
            logger.info("✅ 系统初始化成功")
            
            # 获取系统信息
            info = await system_manager.get_system_info()
            logger.info(f"系统信息: {info}")
            
            # 获取系统状态
            status = await system_manager.get_system_status()
            logger.info(f"系统状态: {status}")
            
        else:
            logger.error("❌ 系统初始化失败")
        
        # 清理
        await system_manager.shutdown()
        logger.info("✅ 系统关闭完成")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)
        return False


async def main():
    """主函数"""
    logger.info("🚀 开始简单演示模式测试...")
    
    try:
        # 测试SystemManager
        success = await test_system_manager()
        
        if success:
            logger.info("🎉 演示模式测试成功！")
        else:
            logger.error("❌ 演示模式测试失败")
            
    except Exception as e:
        logger.error(f"❌ 主函数执行失败: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
