/* 仪表盘样式 */

/* 仪表盘网格布局 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 概览卡片 */
.overview-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.overview-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.overview-card .card-header h3 {
    color: white;
    font-weight: 300;
}

.overview-card .card-body {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 不同类型的概览卡片颜色 */
.overview-card:nth-child(1) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-card:nth-child(2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-card:nth-child(3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-card:nth-child(4) {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    min-height: 400px;
}

.chart-card .card-body {
    position: relative;
    height: 350px;
}

.chart-card canvas {
    max-height: 100%;
}

/* 最近事件 */
.recent-events {
    margin-bottom: 20px;
}

.recent-events .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.events-list {
    max-height: 400px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.event-item:hover {
    background-color: #f8f9fa;
}

.event-item:last-child {
    border-bottom: none;
}

.event-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
    color: white;
}

.event-icon.detection {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.event-icon.tracking {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.event-icon.accident {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.event-icon.violation {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.event-icon.alert {
    background: linear-gradient(135deg, #ff9ff3 0%, #feca57 100%);
}

.event-content {
    flex: 1;
}

.event-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.event-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.event-time {
    color: #999;
    font-size: 0.8rem;
}

.event-actions {
    display: flex;
    gap: 5px;
}

.event-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* 状态指示器 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.online {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.offline {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* 统计数字动画 */
.metric-value {
    transition: all 0.3s ease;
}

.metric-value.updating {
    transform: scale(1.1);
    color: #ffd700;
}

/* 实时数据指示器 */
.live-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    color: #28a745;
}

.live-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-action:hover {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
}

.quick-action i {
    font-size: 1.2rem;
    color: #667eea;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .chart-card {
        min-height: 300px;
    }
    
    .chart-card .card-body {
        height: 250px;
    }
    
    .overview-card .card-body {
        flex-direction: column;
        gap: 15px;
    }
    
    .metric-value {
        font-size: 2rem;
    }
    
    .event-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .event-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .quick-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .metric-value {
        font-size: 1.8rem;
    }
    
    .overview-card .card-body {
        padding: 15px;
    }
    
    .event-item {
        padding: 10px;
    }
    
    .quick-action {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }
}
