/* 组件样式 */

/* 检测页面样式 */
.detection-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.image-upload-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.image-upload-area:hover {
    border-color: #667eea;
    background: #f0f2ff;
}

.image-upload-area.dragover {
    border-color: #667eea;
    background: #e8ebff;
    transform: scale(1.02);
}

.image-upload-area i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 15px;
    display: block;
}

.image-upload-area p {
    color: #666;
    margin: 0;
    font-size: 1.1rem;
}

.detection-config {
    margin-top: 20px;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.config-item label {
    font-weight: 600;
    color: #333;
}

.config-item input[type="range"] {
    margin: 0;
}

.config-item span {
    font-weight: bold;
    color: #667eea;
    text-align: center;
}

.detection-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detection-info h4 {
    margin-bottom: 10px;
    color: #333;
}

.detection-list {
    list-style: none;
    padding: 0;
}

.detection-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.detection-item:last-child {
    border-bottom: none;
}

.detection-class {
    font-weight: 600;
    color: #333;
}

.detection-confidence {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

/* 跟踪页面样式 */
.tracking-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tracks-list .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.header-controls input {
    width: 200px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.header-controls select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.tracks-table-container {
    overflow-x: auto;
}

.tracks-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.tracks-table th,
.tracks-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.tracks-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.tracks-table tr:hover {
    background: #f8f9fa;
}

.track-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.track-status.active {
    background: #d4edda;
    color: #155724;
}

.track-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.track-actions {
    display: flex;
    gap: 5px;
}

.track-actions .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* 热力图样式 */
.heatmap-card .card-body {
    position: relative;
    height: 400px;
}

#heatmapCanvas {
    width: 100%;
    height: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
}

/* 分析页面样式 */
.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analysis-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.analysis-card h3 {
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.analysis-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.analysis-metric:last-child {
    border-bottom: none;
}

.metric-name {
    color: #666;
}

.metric-value {
    font-weight: bold;
    color: #333;
}

/* 警报样式 */
.alerts-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.alert-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.alert-item.critical {
    border-left-color: #dc3545;
}

.alert-item.high {
    border-left-color: #fd7e14;
}

.alert-item.medium {
    border-left-color: #ffc107;
}

.alert-item.low {
    border-left-color: #28a745;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.alert-title {
    font-weight: bold;
    color: #333;
    margin: 0;
}

.alert-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.alert-level.critical {
    background: #f8d7da;
    color: #721c24;
}

.alert-level.high {
    background: #ffeaa7;
    color: #8b4513;
}

.alert-level.medium {
    background: #fff3cd;
    color: #856404;
}

.alert-level.low {
    background: #d4edda;
    color: #155724;
}

.alert-description {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.alert-time {
    color: #999;
    font-size: 0.9rem;
}

.alert-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* 报告样式 */
.report-generator {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
}

.report-config {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
}

.report-preview {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 500px;
}

.report-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.report-type {
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.report-type:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.report-type.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.report-type i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
}

.report-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    overflow-y: auto;
}

/* 设置页面样式 */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 30px;
}

.settings-sidebar {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: fit-content;
}

.settings-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.settings-nav li {
    margin-bottom: 5px;
}

.settings-nav a {
    display: block;
    padding: 10px 15px;
    color: #666;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.settings-nav a:hover,
.settings-nav a.active {
    background: #667eea;
    color: white;
}

.settings-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.settings-section {
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e0e0e0;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .detection-container {
        grid-template-columns: 1fr;
    }
    
    .tracking-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .header-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .header-controls input,
    .header-controls select {
        width: 100%;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
    }
    
    .report-generator {
        grid-template-columns: 1fr;
    }
    
    .settings-container {
        grid-template-columns: 1fr;
    }
    
    .settings-sidebar {
        order: 2;
    }
    
    .settings-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .settings-nav li {
        margin-bottom: 0;
    }
}

@media (max-width: 480px) {
    .tracking-stats {
        grid-template-columns: 1fr;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .report-type-selector {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}
