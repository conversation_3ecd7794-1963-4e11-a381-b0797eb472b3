<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无人机交通警察系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <!-- 引入图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-drone"></i>
                <span>无人机交通警察系统</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                </a>
                <a href="#detection" class="nav-link" data-tab="detection">
                    <i class="fas fa-search"></i> 目标检测
                </a>
                <a href="#tracking" class="nav-link" data-tab="tracking">
                    <i class="fas fa-route"></i> 目标跟踪
                </a>
                <a href="#analysis" class="nav-link" data-tab="analysis">
                    <i class="fas fa-chart-line"></i> 业务分析
                </a>
                <a href="#alerts" class="nav-link" data-tab="alerts">
                    <i class="fas fa-exclamation-triangle"></i> 安全警报
                </a>
                <a href="#reports" class="nav-link" data-tab="reports">
                    <i class="fas fa-file-alt"></i> 报告生成
                </a>
                <a href="#settings" class="nav-link" data-tab="settings">
                    <i class="fas fa-cog"></i> 系统设置
                </a>
            </div>
            <div class="nav-status">
                <div class="status-indicator" id="systemStatus">
                    <i class="fas fa-circle"></i>
                    <span>系统状态</span>
                </div>
                <div class="connection-indicator" id="connectionStatus">
                    <i class="fas fa-wifi"></i>
                    <span>连接状态</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 仪表盘页面 -->
        <div id="dashboard" class="tab-content active">
            <div class="dashboard-header">
                <h1>系统仪表盘</h1>
                <div class="dashboard-controls">
                    <button class="btn btn-primary" id="startSystem">
                        <i class="fas fa-play"></i> 启动系统
                    </button>
                    <button class="btn btn-secondary" id="stopSystem">
                        <i class="fas fa-stop"></i> 停止系统
                    </button>
                    <button class="btn btn-info" id="refreshDashboard">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 系统概览卡片 -->
            <div class="dashboard-grid">
                <div class="card overview-card">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> 实时检测</h3>
                    </div>
                    <div class="card-body">
                        <div class="metric">
                            <span class="metric-value" id="detectionCount">0</span>
                            <span class="metric-label">检测目标数</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value" id="detectionFPS">0</span>
                            <span class="metric-label">检测帧率</span>
                        </div>
                    </div>
                </div>

                <div class="card overview-card">
                    <div class="card-header">
                        <h3><i class="fas fa-route"></i> 目标跟踪</h3>
                    </div>
                    <div class="card-body">
                        <div class="metric">
                            <span class="metric-value" id="activeTracksCount">0</span>
                            <span class="metric-label">活跃轨迹</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value" id="totalTracksCount">0</span>
                            <span class="metric-label">总轨迹数</span>
                        </div>
                    </div>
                </div>

                <div class="card overview-card">
                    <div class="card-header">
                        <h3><i class="fas fa-car"></i> 交通状况</h3>
                    </div>
                    <div class="card-body">
                        <div class="metric">
                            <span class="metric-value" id="vehicleCount">0</span>
                            <span class="metric-label">车辆数量</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value" id="congestionLevel">正常</span>
                            <span class="metric-label">拥堵等级</span>
                        </div>
                    </div>
                </div>

                <div class="card overview-card">
                    <div class="card-header">
                        <h3><i class="fas fa-shield-alt"></i> 安全状况</h3>
                    </div>
                    <div class="card-body">
                        <div class="metric">
                            <span class="metric-value" id="riskLevel">低</span>
                            <span class="metric-label">风险等级</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value" id="alertCount">0</span>
                            <span class="metric-label">活跃警报</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-grid">
                <div class="card chart-card">
                    <div class="card-header">
                        <h3>交通流量趋势</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="trafficChart"></canvas>
                    </div>
                </div>

                <div class="card chart-card">
                    <div class="card-header">
                        <h3>检测性能</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 最近事件 -->
            <div class="card recent-events">
                <div class="card-header">
                    <h3>最近事件</h3>
                    <button class="btn btn-sm btn-outline" id="clearEvents">清空</button>
                </div>
                <div class="card-body">
                    <div class="events-list" id="recentEventsList">
                        <!-- 事件列表将通过JavaScript动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 目标检测页面 -->
        <div id="detection" class="tab-content">
            <div class="page-header">
                <h1>目标检测</h1>
                <div class="page-controls">
                    <button class="btn btn-primary" id="uploadImage">
                        <i class="fas fa-upload"></i> 上传图像
                    </button>
                    <button class="btn btn-secondary" id="detectObjects">
                        <i class="fas fa-search"></i> 开始检测
                    </button>
                </div>
            </div>

            <div class="detection-container">
                <div class="detection-input">
                    <div class="card">
                        <div class="card-header">
                            <h3>输入图像</h3>
                        </div>
                        <div class="card-body">
                            <div class="image-upload-area" id="imageUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击或拖拽上传图像</p>
                                <input type="file" id="imageInput" accept="image/*" hidden>
                            </div>
                            <canvas id="inputCanvas" style="display: none;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="detection-output">
                    <div class="card">
                        <div class="card-header">
                            <h3>检测结果</h3>
                        </div>
                        <div class="card-body">
                            <canvas id="outputCanvas"></canvas>
                            <div class="detection-info" id="detectionInfo">
                                <!-- 检测信息将通过JavaScript填充 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 检测配置 -->
            <div class="card detection-config">
                <div class="card-header">
                    <h3>检测配置</h3>
                </div>
                <div class="card-body">
                    <div class="config-grid">
                        <div class="config-item">
                            <label>置信度阈值</label>
                            <input type="range" id="confidenceThreshold" min="0" max="1" step="0.01" value="0.5">
                            <span id="confidenceValue">0.5</span>
                        </div>
                        <div class="config-item">
                            <label>NMS阈值</label>
                            <input type="range" id="nmsThreshold" min="0" max="1" step="0.01" value="0.4">
                            <span id="nmsValue">0.4</span>
                        </div>
                        <div class="config-item">
                            <label>最大检测数</label>
                            <input type="number" id="maxDetections" min="1" max="100" value="50">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 目标跟踪页面 -->
        <div id="tracking" class="tab-content">
            <div class="page-header">
                <h1>目标跟踪</h1>
                <div class="page-controls">
                    <button class="btn btn-primary" id="startTracking">
                        <i class="fas fa-play"></i> 开始跟踪
                    </button>
                    <button class="btn btn-secondary" id="stopTracking">
                        <i class="fas fa-stop"></i> 停止跟踪
                    </button>
                    <button class="btn btn-info" id="resetTracking">
                        <i class="fas fa-redo"></i> 重置
                    </button>
                </div>
            </div>

            <!-- 跟踪统计 -->
            <div class="tracking-stats">
                <div class="stat-card">
                    <div class="stat-value" id="trackingActiveCount">0</div>
                    <div class="stat-label">活跃轨迹</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="trackingTotalCount">0</div>
                    <div class="stat-label">总轨迹数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="trackingFPS">0</div>
                    <div class="stat-label">跟踪帧率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="trackingAccuracy">0%</div>
                    <div class="stat-label">跟踪精度</div>
                </div>
            </div>

            <!-- 轨迹列表 -->
            <div class="card tracks-list">
                <div class="card-header">
                    <h3>当前轨迹</h3>
                    <div class="header-controls">
                        <input type="text" placeholder="搜索轨迹..." id="trackSearch">
                        <select id="trackFilter">
                            <option value="all">全部</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tracks-table-container">
                        <table class="tracks-table" id="tracksTable">
                            <thead>
                                <tr>
                                    <th>轨迹ID</th>
                                    <th>类别</th>
                                    <th>置信度</th>
                                    <th>位置</th>
                                    <th>速度</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 轨迹数据将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 轨迹热力图 -->
            <div class="card heatmap-card">
                <div class="card-header">
                    <h3>轨迹热力图</h3>
                </div>
                <div class="card-body">
                    <canvas id="heatmapCanvas"></canvas>
                </div>
            </div>
        </div>

        <!-- 其他页面内容将在后续添加 -->
        <div id="analysis" class="tab-content">
            <h1>业务分析页面开发中...</h1>
        </div>

        <div id="alerts" class="tab-content">
            <h1>安全警报页面开发中...</h1>
        </div>

        <div id="reports" class="tab-content">
            <h1>报告生成页面开发中...</h1>
        </div>

        <div id="settings" class="tab-content">
            <h1>系统设置页面开发中...</h1>
        </div>
    </main>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">取消</button>
                <button class="btn btn-primary" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="notifications-container">
        <!-- 通知将通过JavaScript动态添加 -->
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/detection.js"></script>
    <script src="js/tracking.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
