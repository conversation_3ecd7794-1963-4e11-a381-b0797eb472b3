// API接口管理

class APIClient {
    constructor(baseURL = '/api/v1') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.blob();
            }
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.request(url.pathname + url.search, { method: 'GET' });
    }

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT请求
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // 上传文件
    async upload(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.request(endpoint, {
            method: 'POST',
            headers: {}, // 让浏览器自动设置Content-Type
            body: formData
        });
    }

    // 系统管理API
    async getSystemStatus() {
        return this.get('/system/status');
    }

    async getSystemHealth() {
        return this.get('/system/health');
    }

    async getSystemInfo() {
        return this.get('/system/info');
    }

    async startSystem() {
        return this.post('/system/start');
    }

    async stopSystem() {
        return this.post('/system/stop');
    }

    async restartSystem() {
        return this.post('/system/restart');
    }

    async getSystemStatistics() {
        return this.get('/system/statistics');
    }

    async getSystemConfig() {
        return this.get('/system/config');
    }

    async updateSystemConfig(config) {
        return this.post('/system/config', { config });
    }

    // 目标检测API
    async detectObjects(file, frameId = null) {
        return this.upload('/detection/detect', file, { frame_id: frameId });
    }

    async getDetectionConfig() {
        return this.get('/detection/config');
    }

    async updateDetectionConfig(config) {
        return this.post('/detection/config', config);
    }

    async getDetectionClasses() {
        return this.get('/detection/classes');
    }

    async getDetectionStatistics() {
        return this.get('/detection/statistics');
    }

    async runDetectionBenchmark(iterations = 10) {
        return this.post('/detection/benchmark', { iterations });
    }

    // 目标跟踪API
    async getCurrentTracks() {
        return this.get('/tracking/tracks');
    }

    async getTrackDetails(trackId) {
        return this.get(`/tracking/tracks/${trackId}`);
    }

    async deleteTrack(trackId) {
        return this.delete(`/tracking/tracks/${trackId}`);
    }

    async getTrackingConfig() {
        return this.get('/tracking/config');
    }

    async updateTrackingConfig(config) {
        return this.post('/tracking/config', config);
    }

    async getTrackingStatistics() {
        return this.get('/tracking/statistics');
    }

    async getAllTrajectories(limit = 100) {
        return this.get('/tracking/trajectories', { limit });
    }

    async getTrackingHeatmap(width = 100, height = 100) {
        return this.get('/tracking/heatmap', { width, height });
    }

    // 业务分析API
    async getCurrentAnalysis() {
        return this.get('/analysis/current');
    }

    async getTrafficMetrics(timeWindow = 300) {
        return this.get('/analysis/traffic', { time_window: timeWindow });
    }

    async getRecentAccidents(hours = 24) {
        return this.get('/analysis/accidents', { hours });
    }

    async getRecentViolations(hours = 24) {
        return this.get('/analysis/violations', { hours });
    }

    async getRecentAlerts(hours = 24) {
        return this.get('/analysis/alerts', { hours });
    }

    async getRecentDecisions(hours = 24) {
        return this.get('/analysis/decisions', { hours });
    }

    async getAnalysisStatistics() {
        return this.get('/analysis/statistics');
    }

    async predictCongestion(horizon = 300) {
        return this.get('/analysis/congestion/prediction', { horizon });
    }

    async getCongestionHotspots(gridSize = 10) {
        return this.get('/analysis/congestion/hotspots', { grid_size: gridSize });
    }

    // 数据管理API
    async getDataStatistics() {
        return this.get('/data/statistics');
    }

    async getDataHealth() {
        return this.get('/data/health');
    }

    async createSession(config = null) {
        return this.post('/data/sessions', config);
    }

    async endSession(sessionId) {
        return this.delete(`/data/sessions/${sessionId}`);
    }

    async getTrafficStatistics(hours = 24) {
        return this.get('/data/traffic-statistics', { hours });
    }

    async getAccidentData(hours = 24, limit = 100) {
        return this.get('/data/accidents', { hours, limit });
    }

    async getViolationData(hours = 24, limit = 100) {
        return this.get('/data/violations', { hours, limit });
    }

    async cleanupOldData(retentionDays = 30) {
        return this.post('/data/cleanup', { retention_days: retentionDays });
    }

    // LLM服务API
    async generateReport(reportType, data) {
        return this.post('/llm/reports/generate', {
            report_type: reportType,
            data: data
        });
    }

    async synthesizeVoice(text, voiceType = 'announcement', urgencyLevel = 'normal') {
        return this.post('/llm/voice/synthesize', {
            text: text,
            voice_type: voiceType,
            urgency_level: urgencyLevel
        });
    }

    async synthesizeAnnouncement(announcementType, content, urgencyLevel = 'normal') {
        const params = new URLSearchParams({
            announcement_type: announcementType,
            content: content,
            urgency_level: urgencyLevel
        });
        return this.request(`/llm/voice/announcement?${params}`, { method: 'POST' });
    }

    async analyzeText(texts) {
        return this.post('/llm/text/analyze', { texts });
    }

    async getAvailableVoices() {
        return this.get('/llm/voice/voices');
    }

    async setVoice(voiceName) {
        return this.post('/llm/voice/set-voice', { voice_name: voiceName });
    }

    async getLLMStatistics() {
        return this.get('/llm/statistics');
    }

    async generateBatchReports(trafficData, accidents, violations, alerts, decisions) {
        return this.post('/llm/reports/batch', {
            traffic_data: trafficData,
            accidents: accidents,
            violations: violations,
            alerts: alerts,
            decisions: decisions
        });
    }

    async generateSmartAnnouncement(eventType, eventData, targetAudience = 'drivers') {
        const params = new URLSearchParams({
            event_type: eventType,
            target_audience: targetAudience
        });
        return this.post(`/llm/smart-announcement?${params}`, eventData);
    }
}

// 创建全局API客户端实例
const api = new APIClient();

// 错误处理装饰器
function withErrorHandling(apiMethod) {
    return async function(...args) {
        try {
            utils.showLoading('main-content', '请求中...');
            const result = await apiMethod.apply(this, args);
            utils.hideLoading('main-content');
            return result;
        } catch (error) {
            utils.hideLoading('main-content');
            utils.showNotification(`请求失败: ${error.message}`, 'error');
            throw error;
        }
    };
}

// 为常用API方法添加错误处理
const apiWithErrorHandling = {
    // 系统API
    getSystemStatus: withErrorHandling(api.getSystemStatus.bind(api)),
    getSystemHealth: withErrorHandling(api.getSystemHealth.bind(api)),
    startSystem: withErrorHandling(api.startSystem.bind(api)),
    stopSystem: withErrorHandling(api.stopSystem.bind(api)),
    
    // 检测API
    detectObjects: withErrorHandling(api.detectObjects.bind(api)),
    getDetectionStatistics: withErrorHandling(api.getDetectionStatistics.bind(api)),
    
    // 跟踪API
    getCurrentTracks: withErrorHandling(api.getCurrentTracks.bind(api)),
    getTrackingStatistics: withErrorHandling(api.getTrackingStatistics.bind(api)),
    
    // 分析API
    getCurrentAnalysis: withErrorHandling(api.getCurrentAnalysis.bind(api)),
    getTrafficMetrics: withErrorHandling(api.getTrafficMetrics.bind(api)),
    
    // 原始API对象
    raw: api
};

// 导出API客户端
window.api = apiWithErrorHandling;
