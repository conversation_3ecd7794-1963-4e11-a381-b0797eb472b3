#!/usr/bin/env python3
"""
修复版安全帽检测演示
解决OpenCV rectangle坐标类型问题
"""

import sys
import cv2
import time
import numpy as np
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def safe_int(value):
    """安全转换为整数"""
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return 0

def draw_detections_safe(frame: np.ndarray, helmet_violations: List) -> np.ndarray:
    """安全绘制检测结果，确保所有坐标都是整数"""
    annotated_frame = frame.copy()
    
    for violation in helmet_violations:
        try:
            person = violation.person_detection
            vehicle = violation.vehicle_detection
            
            # 获取人员边界框并安全转换为整数
            x1, y1, x2, y2 = person.bbox
            x1, y1, x2, y2 = safe_int(x1), safe_int(y1), safe_int(x2), safe_int(y2)
            
            # 确保坐标在图像范围内
            height, width = frame.shape[:2]
            x1 = max(0, min(x1, width-1))
            y1 = max(0, min(y1, height-1))
            x2 = max(0, min(x2, width-1))
            y2 = max(0, min(y2, height-1))
            
            # 根据是否佩戴安全帽选择颜色
            if violation.helmet_detected:
                color = (0, 255, 0)  # 绿色 - 佩戴安全帽
                status = "✓ 安全帽"
            else:
                color = (0, 0, 255)  # 红色 - 未佩戴安全帽
                status = "✗ 无安全帽"
            
            # 绘制边界框
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 3)
            
            # 绘制标签背景
            label = f"{status} ({violation.helmet_confidence:.2f})"
            try:
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                label_width, label_height = label_size
                
                # 确保标签位置在图像范围内
                label_y1 = max(label_height + 5, y1 - 5)
                label_y2 = label_y1 - label_height - 10
                label_x2 = min(x1 + label_width + 10, width)
                
                cv2.rectangle(annotated_frame, (x1, label_y2), (label_x2, label_y1), color, -1)
                
                # 绘制标签文字
                cv2.putText(annotated_frame, label, (x1 + 5, label_y1 - 8), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            except Exception as e:
                print(f"标签绘制错误: {e}")
            
            # 如果有关联的车辆，也绘制出来
            if vehicle:
                try:
                    vx1, vy1, vx2, vy2 = vehicle.bbox
                    vx1, vy1, vx2, vy2 = safe_int(vx1), safe_int(vy1), safe_int(vx2), safe_int(vy2)
                    
                    # 确保车辆坐标在图像范围内
                    vx1 = max(0, min(vx1, width-1))
                    vy1 = max(0, min(vy1, height-1))
                    vx2 = max(0, min(vx2, width-1))
                    vy2 = max(0, min(vy2, height-1))
                    
                    cv2.rectangle(annotated_frame, (vx1, vy1), (vx2, vy2), (255, 255, 0), 2)
                    cv2.putText(annotated_frame, vehicle.class_name, (vx1, vy1 - 5),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
                except Exception as e:
                    print(f"车辆绘制错误: {e}")
            
            # 绘制风险等级
            try:
                risk_colors = {
                    "high": (0, 0, 255),    # 红色
                    "medium": (0, 165, 255), # 橙色
                    "low": (0, 255, 255)     # 黄色
                }
                risk_color = risk_colors.get(violation.risk_level, (255, 255, 255))
                
                risk_y = min(y2 + 25, height - 10)
                cv2.putText(annotated_frame, f"风险: {violation.risk_level}", 
                           (x1, risk_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, risk_color, 2)
            except Exception as e:
                print(f"风险等级绘制错误: {e}")
                
        except Exception as e:
            print(f"检测结果绘制错误: {e}")
            continue
    
    # 添加总体信息
    try:
        total_riders = len(helmet_violations)
        violations = sum(1 for v in helmet_violations if not v.helmet_detected)
        
        info_text = f"骑行者: {total_riders}, 违规: {violations}"
        cv2.putText(annotated_frame, info_text, (20, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
        
        # 添加时间戳
        timestamp = time.strftime("%H:%M:%S")
        cv2.putText(annotated_frame, f"时间: {timestamp}", (20, 80), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    except Exception as e:
        print(f"信息绘制错误: {e}")
    
    return annotated_frame

def main():
    """主函数"""
    print("🛡️ 修复版安全帽检测演示")
    print("=" * 50)
    
    print("🎯 这个演示解决了OpenCV坐标类型问题")
    print("💡 按 'q' 键退出，按 'r' 键重置统计，按 's' 键显示统计")
    
    try:
        # 导入必要模块
        from core.ai.detection.helmet_detector import HelmetDetector
        from config.ai_config import AIConfig
        
        print("✅ 模块导入成功")
        
        # 创建检测器
        ai_config = AIConfig()
        helmet_config = ai_config.get_helmet_detection_config()
        detector = HelmetDetector(helmet_config)
        
        print("✅ 安全帽检测器初始化成功")
        
        # 检查视频文件
        video_path = "data/videos/action1.mp4"
        if not Path(video_path).exists():
            print(f"❌ 视频文件不存在: {video_path}")
            return
        
        print("✅ 视频文件检查通过")
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ 无法打开视频文件")
            return
        
        print("✅ 视频文件打开成功")
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📹 视频信息: {width}x{height}, {fps:.1f}FPS, {frame_count}帧")
        print("\n🎬 开始安全帽检测演示...")
        print("   - 绿色框: 佩戴安全帽")
        print("   - 红色框: 未佩戴安全帽")
        print("   - 黄色框: 检测到的车辆")
        
        # 创建窗口
        cv2.namedWindow('修复版安全帽检测演示', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('修复版安全帽检测演示', 1200, 800)
        
        frame_num = 0
        last_stats_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                # 视频结束，重新开始
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            
            frame_num += 1
            
            try:
                # 执行安全帽检测
                helmet_violations = detector.detect_helmet_violations(frame)
                
                # 在帧上绘制检测结果（使用安全的绘制函数）
                annotated_frame = draw_detections_safe(frame, helmet_violations)
                
                # 添加帧数信息
                cv2.putText(annotated_frame, f"帧: {frame_num}", (20, 120), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                
                # 显示结果
                cv2.imshow('修复版安全帽检测演示', annotated_frame)
                
                # 每5秒显示一次统计信息
                current_time = time.time()
                if current_time - last_stats_time >= 5.0:
                    stats = detector.get_violation_statistics()
                    print(f"\n📊 统计: 骑行者{stats['total_riders']}, 违规{stats['violation_count']}, 违规率{stats['violation_rate']:.1f}%")
                    last_stats_time = current_time
                
                # 处理按键
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    print("\n👋 退出演示")
                    break
                elif key == ord('r'):
                    detector.reset_statistics()
                    print("\n🔄 统计信息已重置")
                elif key == ord('s'):
                    stats = detector.get_violation_statistics()
                    print(f"\n📊 当前统计: 骑行者{stats['total_riders']}, 违规{stats['violation_count']}, 违规率{stats['violation_rate']:.1f}%")
                
                # 控制播放速度
                time.sleep(1/30)  # 约30 FPS
                
            except Exception as e:
                print(f"❌ 处理帧 {frame_num} 时出错: {e}")
                continue
        
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        
        # 显示最终统计
        print("\n" + "=" * 50)
        print("📊 最终统计结果")
        print("=" * 50)
        final_stats = detector.get_violation_statistics()
        print(f"处理帧数: {frame_num}")
        print(f"总骑行者: {final_stats['total_riders']}")
        print(f"违规数量: {final_stats['violation_count']}")
        print(f"违规率: {final_stats['violation_rate']:.1f}%")
        print(f"合规率: {final_stats['compliance_rate']:.1f}%")
        
        print("\n🎉 修复版安全帽检测演示完成！")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖包")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
