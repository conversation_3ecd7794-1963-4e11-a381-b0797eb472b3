#!/usr/bin/env python3
"""
测试数据模型是否正常工作
"""

def test_models_import():
    """测试模型导入"""
    print("🔍 测试数据模型导入...")
    
    try:
        from core.data.models import Base, Detection, TrafficSession
        print("✅ 模型导入成功")
        
        # 测试模型属性
        print(f"  Detection表名: {Detection.__tablename__}")
        print(f"  TrafficSession表名: {TrafficSession.__tablename__}")
        
        # 检查Detection模型的字段
        detection_columns = [column.name for column in Detection.__table__.columns]
        print(f"  Detection字段: {detection_columns}")
        
        # 确认extra_data字段存在
        if 'extra_data' in detection_columns:
            print("  ✅ extra_data字段存在")
        else:
            print("  ❌ extra_data字段不存在")
        
        # 确认metadata字段不存在
        if 'metadata' not in detection_columns:
            print("  ✅ metadata字段已移除")
        else:
            print("  ❌ metadata字段仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False


def test_database_creation():
    """测试数据库创建"""
    print("\n🔍 测试数据库创建...")
    
    try:
        from sqlalchemy import create_engine
        from core.data.models import Base
        
        # 创建内存数据库进行测试
        engine = create_engine("sqlite:///:memory:", echo=False)
        
        # 创建所有表
        Base.metadata.create_all(engine)
        print("✅ 数据库表创建成功")
        
        # 检查表是否存在
        table_names = list(Base.metadata.tables.keys())
        print(f"  创建的表: {table_names}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False


def test_detection_model():
    """测试Detection模型"""
    print("\n🔍 测试Detection模型...")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from core.data.models import Base, Detection
        from datetime import datetime
        
        # 创建内存数据库
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 创建测试数据
        test_detection = Detection(
            session_id="test_session",
            frame_id=1,
            timestamp=datetime.utcnow(),
            bbox_x1=100.0,
            bbox_y1=100.0,
            bbox_x2=200.0,
            bbox_y2=200.0,
            confidence=0.95,
            class_id=1,
            class_name="car",
            track_id=1,
            extra_data={"test": "data"}
        )
        
        # 保存到数据库
        session.add(test_detection)
        session.commit()
        
        # 查询数据
        saved_detection = session.query(Detection).first()
        print(f"✅ Detection模型测试成功")
        print(f"  ID: {saved_detection.id}")
        print(f"  类别: {saved_detection.class_name}")
        print(f"  置信度: {saved_detection.confidence}")
        print(f"  扩展数据: {saved_detection.extra_data}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ Detection模型测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试数据模型...")
    
    # 测试模型导入
    import_ok = test_models_import()
    
    # 测试数据库创建
    db_ok = test_database_creation()
    
    # 测试Detection模型
    detection_ok = test_detection_model()
    
    print("\n📊 测试结果:")
    print(f"  模型导入: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"  数据库创建: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"  Detection模型: {'✅ 通过' if detection_ok else '❌ 失败'}")
    
    if import_ok and db_ok and detection_ok:
        print("\n🎉 所有测试通过！数据模型修复成功。")
    else:
        print("\n⚠️  存在问题，请检查上述错误信息。")
