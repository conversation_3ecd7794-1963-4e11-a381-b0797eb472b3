import torch
import cv2
import numpy as np
import sys
sys.path.append("yolov5")
from yolov5.models.experimental import attempt_load
from yolov5.utils.general import non_max_suppression

# 🚀 Letterbox resize
def letterbox(im, new_shape=(640, 640), color=(114, 114, 114)):
    shape = im.shape[:2]
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]
    dw /= 2
    dh /= 2
    im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
    return im, r, (dw, dh)

# ✨ 坐标从缩放图映射回原图
def scale_coords_back(xyxy, gain, pad):
    x1, y1, x2, y2 = xyxy
    x1 = (x1 - pad[0]) / gain
    y1 = (y1 - pad[1]) / gain
    x2 = (x2 - pad[0]) / gain
    y2 = (y2 - pad[1]) / gain
    return map(int, (x1, y1, x2, y2))

# 📊 绘制密度曲线
def draw_density_plot(densities_v, densities_p, window_size=(400, 300), max_points=100):
    img = np.zeros((window_size[1], window_size[0], 3), dtype=np.uint8)
    img.fill(255)
    cv2.line(img, (50, window_size[1]-50), (window_size[0]-10, window_size[1]-50), (0, 0, 0), 1)
    cv2.line(img, (50, window_size[1]-50), (50, 10), (0, 0, 0), 1)
    cv2.putText(img, "Time", (window_size[0]//2, window_size[1]-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(img, "Density (count/m²)", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(img, "Vehicle", (window_size[0]-80, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
    cv2.putText(img, "Person", (window_size[0]-80, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

    max_density = max(max(densities_v + densities_p, default=0.001), 0.01)
    for i in range(1, min(len(densities_v), max_points)):
        x1 = 50 + int((i-1) * (window_size[0]-60) / max_points)
        x2 = 50 + int(i * (window_size[0]-60) / max_points)
        y1_v = window_size[1] - 50 - int((densities_v[i-1] / max_density) * (window_size[1]-60))
        y2_v = window_size[1] - 50 - int((densities_v[i] / max_density) * (window_size[1]-60))
        y1_p = window_size[1] - 50 - int((densities_p[i-1] / max_density) * (window_size[1]-60))
        y2_p = window_size[1] - 50 - int((densities_p[i] / max_density) * (window_size[1]-60))
        cv2.line(img, (x1, y1_v), (x2, y2_v), (0, 0, 255), 2)  # 车辆（红色）
        cv2.line(img, (x1, y1_p), (x2, y2_p), (255, 0, 0), 2)  # 行人（蓝色）
    return img

# 📦 设备设置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 🔍 加载模型
try:
    model = attempt_load("yolov5s.pt")
    model.to(device).eval()
    print("Model loaded successfully")
except Exception as e:
    print(f"Error loading model: {e}")
    sys.exit(1)

# 🎥 打开视频
cap = cv2.VideoCapture("action1.mp4")
if not cap.isOpened():
    print("Error: Could not open video file 'action1.mp4'")
    sys.exit(1)

fps = cap.get(cv2.CAP_PROP_FPS)
frame_count_total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
print(f"Video opened successfully. FPS: {fps}, Total Frames: {frame_count_total}, Resolution: {width}x{height}")

# 📊 初始化数据存储
vehicle_densities = []
person_densities = []
vehicle_counts = []
person_counts = []
vehicle_pixel_areas = []  # 存储车辆像素面积
max_points = 100
frame_count = 0
AREA_M2 = None  # 动态估算面积
VEHICLE_PHYSICAL_AREA = 8.64  # 普通车辆面积：4.8m × 1.8m = 8.64 m²
CALIBRATION_FRAMES = 10  # 使用前10帧估算面积

# 📊 创建 OpenCV 窗口（可调节大小）
cv2.namedWindow("YOLOv5 Detection", cv2.WINDOW_NORMAL)
cv2.namedWindow("Traffic Density Plot", cv2.WINDOW_NORMAL)
cv2.resizeWindow("YOLOv5 Detection", int(width * 0.5), int(height * 0.5))  # 初始缩小70%以适配屏幕
cv2.resizeWindow("Traffic Density Plot", 400, 300)

while cap.isOpened():
    try:
        ret, frame = cap.read()
        if not ret:
            print(f"End of video at frame {frame_count}")
            break

        print(f"Processing frame {frame_count}, size: {frame.shape}")
        original = frame.copy()
        img, gain, pad = letterbox(frame, new_shape=(640, 640))
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = torch.from_numpy(img).permute(2, 0, 1).float().unsqueeze(0) / 255.0
        img = img.to(device)

        with torch.no_grad():
            pred = model(img)[0]
            pred = non_max_suppression(pred, conf_thres=0.25, iou_thres=0.45)

        vehicle_count = 0
        person_count = 0
        frame_vehicle_pixel_areas = []
        for det in pred:
            if det is not None and len(det):
                for *xyxy, conf, cls in det:
                    x1, y1, x2, y2 = scale_coords_back(xyxy, gain, pad)
                    if model.names[int(cls)] in ["car", "truck", "bus"]:
                        vehicle_count += 1
                        # 计算车辆边界框像素面积
                        pixel_area = (x2 - x1) * (y2 - y1)
                        frame_vehicle_pixel_areas.append(pixel_area)
                    if model.names[int(cls)] == "person":
                        person_count += 1
                    label = f"{model.names[int(cls)]} {conf:.2f}"
                    cv2.rectangle(original, (x1, y1), (x2, y2), (0, 255, 0), 1)
                    cv2.putText(original, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX,
                                0.9, (0, 255, 0), 2)

        # 估算物理面积（前10帧）
        if frame_count < CALIBRATION_FRAMES and frame_vehicle_pixel_areas:
            vehicle_pixel_areas.extend(frame_vehicle_pixel_areas)
        if frame_count == CALIBRATION_FRAMES and vehicle_pixel_areas:
            avg_vehicle_pixel_area = np.mean(vehicle_pixel_areas)
            pixel_to_m2 = VEHICLE_PHYSICAL_AREA / avg_vehicle_pixel_area
            AREA_M2 = width * height * pixel_to_m2
            print(f"Estimated AREA_M2: {AREA_M2:.2f} m² (based on avg vehicle pixel area: {avg_vehicle_pixel_area:.2f})")

        # 使用默认面积直到估算完成
        area_m2 = AREA_M2 if AREA_M2 is not None else 1000
        vehicle_density = vehicle_count / area_m2
        person_density = person_count / area_m2
        vehicle_counts.append(vehicle_count)
        person_counts.append(person_count)
        vehicle_densities.append(vehicle_density)
        person_densities.append(person_density)
        frame_count += 1
        print(f"Frame {frame_count}: Vehicles={vehicle_count}, Persons={person_count}, "
              f"Vehicle Density={vehicle_density:.4f}/m², Person Density={person_density:.4f}/m²")

        if len(vehicle_densities) > max_points:
            vehicle_densities.pop(0)
            person_densities.pop(0)
            vehicle_counts.pop(0)
            person_counts.pop(0)

        # 绘制密度曲线
        plot_img = draw_density_plot(vehicle_densities, person_densities)
        density_text = f"Vehicles: {vehicle_count} ({vehicle_density:.4f}/m²)\nPersons: {person_count} ({person_density:.4f}/m²)"
        y_offset = 30
        for i, line in enumerate(density_text.split('\n')):
            cv2.putText(original, line, (10, y_offset + i * 30), cv2.FONT_HERSHEY_SIMPLEX,
                        0.7, (0, 255, 255), 2)

        cv2.imshow("YOLOv5 Detection", original)
        cv2.imshow("Traffic Density Plot", plot_img)

        if cv2.waitKey(33) & 0xFF == ord('q'):
            print("User requested exit")
            break

    except Exception as e:
        print(f"Error in frame processing at frame {frame_count}: {e}")
        break

cap.release()
cv2.destroyAllWindows()