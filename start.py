#!/usr/bin/env python3
"""
无人机交通警察系统快速启动脚本
Quick Start Script for Air Traffic Police System
"""
import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                  无人机交通警察系统                             ║
    ║              Air Traffic Police System                       ║
    ║                                                              ║
    ║  🚁 智能交通监控 | 🤖 AI驱动 | 🌐 实时分析                        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")

    # 包名映射：pip包名 -> 导入名
    package_mapping = {
        'torch': 'torch',
        'torchvision': 'torchvision',
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'fastapi': 'fastapi',
        'uvicorn': 'uvicorn',
        'sqlalchemy': 'sqlalchemy',
        'pydantic': 'pydantic'
    }

    missing_packages = []

    for pip_name, import_name in package_mapping.items():
        try:
            module = __import__(import_name)
            # 特殊检查opencv版本
            if import_name == 'cv2':
                version = getattr(module, '__version__', 'unknown')
                print(f"  ✅ {pip_name} (版本: {version})")
            else:
                print(f"  ✅ {pip_name}")
        except ImportError as e:
            print(f"  ❌ {pip_name} (缺失: {e})")
            missing_packages.append(pip_name)
        except Exception as e:
            print(f"  ⚠️  {pip_name} (导入异常: {e})")

    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖包: {', '.join(missing_packages)}")
        print("   请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依赖包检查通过")
    return True


def create_directories():
    """创建必要的目录"""
    print("\n📁 创建必要目录...")
    
    directories = [
        'logs',
        'data',
        'models', 
        'temp',
        'static',
        'uploads',
        'config'
    ]
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ 创建目录: {directory}")
        else:
            print(f"  ✅ 目录已存在: {directory}")


def create_default_config():
    """创建默认配置文件"""
    print("\n⚙️  创建默认配置...")
    
    config_file = Path("config/config.yaml")
    
    if not config_file.exists():
        default_config = """
# 无人机交通警察系统配置文件

# 系统配置
system:
  name: "Air Traffic Police System"
  version: "1.0.0"
  debug: true
  log_level: "INFO"

# 硬件配置
hardware:
  camera:
    type: "usb"  # usb, rtsp, drone
    device_id: 0
    resolution: [1920, 1080]
    fps: 30
  
  drone:
    enabled: false
    type: "dji"
    connection: "wifi"

# AI配置
ai:
  detection:
    model_path: "models/yolov8n.pt"
    confidence_threshold: 0.5
    iou_threshold: 0.4
    device: "auto"  # auto, cpu, cuda
    
  tracking:
    max_age: 30
    min_hits: 3
    iou_threshold: 0.3

# 业务配置
business:
  traffic_analysis:
    density_threshold: 0.3
    congestion_levels: [0.2, 0.4, 0.6, 0.8]
    
  accident_detection:
    enabled: true
    sensitivity: 0.7
    
  violation_detection:
    enabled: true
    speed_limit: 60  # km/h

# API配置
api:
  host: "0.0.0.0"
  port: 8000
  cors_origins: ["*"]
  enable_docs: true

# 数据库配置
database:
  url: "sqlite:///data/traffic_police.db"
  echo: false
  
# 缓存配置
cache:
  enabled: true
  type: "memory"  # memory, redis
  ttl: 3600

# LLM配置
llm:
  enabled: false
  provider: "openai"
  api_key: ""
"""
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(default_config.strip())
        
        print(f"  ✅ 创建配置文件: {config_file}")
    else:
        print(f"  ✅ 配置文件已存在: {config_file}")


def download_models():
    """下载模型文件"""
    print("\n🤖 检查AI模型...")
    
    model_file = Path("models/yolov8n.pt")
    
    if not model_file.exists():
        print("  📥 下载YOLO模型...")
        try:
            # 这里会自动下载模型
            import torch
            from ultralytics import YOLO
            
            model = YOLO('yolov8n.pt')
            print("  ✅ YOLO模型下载完成")
        except Exception as e:
            print(f"  ⚠️  模型下载失败: {e}")
            print("     系统将在运行时自动下载")
    else:
        print("  ✅ YOLO模型已存在")


def show_menu():
    """显示启动菜单"""
    print("\n🚀 启动选项:")
    print("  1. 📱 交互式演示 (推荐) - 命令行交互界面")
    print("  2. 🖥️  图形界面演示 - GUI可视化界面")
    print("  3. 🎬 视频检测演示 - 真实视频处理")
    print("  4. 📊 真实数据演示 - Qt6/Web + 真实检测")
    print("  5. 🛡️ 安全帽检测演示 (新功能) - 电动车安全帽检测")
    print("  6. 🌐 Web演示界面 - 浏览器访问")
    print("  7. 🚀 API服务模式 - RESTful API服务")
    print("  8. 🎭 简单演示模式 - 基础命令行演示")
    print("  9. 🔧 系统测试 - 完整功能测试")
    print("  10. 📦 安装依赖")
    print("  11. 🔍 检查系统")
    print("  0. 👋 退出")

    while True:
        try:
            choice = input("\n请选择启动模式 (0-11): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']:
                return choice
            else:
                print("❌ 无效选择，请输入0-11之间的数字")
        except KeyboardInterrupt:
            print("\n\n👋 再见!")
            sys.exit(0)


def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖包...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到requirements.txt文件")
        return False
    
    return True


def run_system(mode):
    """运行系统"""
    print(f"\n🚀 启动系统 - {mode}模式...")
    
    try:
        if mode == "api":
            cmd = [sys.executable, "main.py", "--mode", "api"]
        elif mode == "console":
            cmd = [sys.executable, "main.py", "--mode", "console"]
        elif mode == "demo":
            cmd = [sys.executable, "main.py", "--mode", "demo"]
        else:
            print("❌ 未知的启动模式")
            return
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  系统已停止")
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")


def check_system():
    """系统检查"""
    print("\n🔍 系统检查...")
    
    # 检查操作系统
    os_name = platform.system()
    print(f"  ✅ 操作系统: {os_name} {platform.release()}")
    
    # 检查Python版本
    check_python_version()
    
    # 检查依赖
    check_dependencies()
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"  ✅ GPU: {gpu_name} (数量: {gpu_count})")
        else:
            print("  ⚠️  GPU: 未检测到CUDA设备，将使用CPU")
    except ImportError:
        print("  ⚠️  GPU: 无法检测GPU状态")
    
    # 检查磁盘空间
    import shutil
    total, used, free = shutil.disk_usage(".")
    free_gb = free // (1024**3)
    print(f"  ✅ 磁盘空间: {free_gb}GB 可用")
    
    print("\n✅ 系统检查完成")


def main():
    """主函数"""
    print_banner()
    
    # 基础检查
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录和配置
    create_directories()
    create_default_config()
    
    # 显示菜单
    while True:
        choice = show_menu()

        if choice == '0':
            print("\n👋 再见!")
            break
        elif choice == '1':
            # 交互式演示
            if check_dependencies():
                print("🚀 启动交互式演示...")
                subprocess.run([sys.executable, "demo_interactive.py"])
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '2':
            # 图形界面演示
            if check_dependencies():
                print("🚀 启动图形界面演示...")

                # 优先尝试Qt6界面
                try:
                    import PyQt6
                    print("💎 使用Qt6现代化界面...")
                    subprocess.run([sys.executable, "demo_qt6.py"])
                except ImportError:
                    print("⚠️ Qt6未安装，尝试Tkinter界面...")
                    try:
                        subprocess.run([sys.executable, "demo_ui.py"])
                    except:
                        print("❌ GUI库未安装，切换到交互式演示...")
                        subprocess.run([sys.executable, "demo_interactive.py"])
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '3':
            # 视频检测演示
            if check_dependencies():
                print("🚀 启动视频检测演示...")
                print("💡 将处理action1.mp4视频文件并显示检测效果")

                # 检查视频文件
                video_path = "data/videos/action1.mp4"
                if not os.path.exists(video_path):
                    print(f"⚠️ 找不到视频文件: {video_path}")
                    print("💡 请确保action1.mp4文件在data/videos/目录中")
                else:
                    try:
                        import PyQt6
                        subprocess.run([sys.executable, "demo_video_detection.py"])
                    except ImportError:
                        print("❌ Qt6未安装，无法启动视频检测演示")
                        print("💡 安装命令: pip install PyQt6")
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '4':
            # 真实数据演示
            if check_dependencies():
                print("🚀 启动真实数据演示...")
                print("💡 这将使用action1.mp4视频和真实的AI检测")

                # 检查视频文件
                video_path = "data/videos/action1.mp4"
                if not os.path.exists(video_path):
                    print(f"⚠️ 找不到视频文件: {video_path}")
                    print("💡 请确保action1.mp4文件在data/videos/目录中")
                else:
                    print("请选择真实数据演示模式:")
                    print("  1. Qt6界面 + 真实数据")
                    print("  2. Web界面 + 真实数据")
                    print("  3. 数据处理测试")

                    sub_choice = input("请选择 (1-3): ").strip()

                    if sub_choice in ['1', '2', '3']:
                        subprocess.run([sys.executable, "demo_real_data.py"])
                    else:
                        print("❌ 无效选择")
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '5':
            # 安全帽检测演示
            if check_dependencies():
                print("🛡️ 启动安全帽检测演示...")
                print("💡 这将展示电动车骑行者未戴安全帽检测功能")

                # 检查视频文件
                video_path = "data/videos/action1.mp4"
                if not os.path.exists(video_path):
                    print(f"⚠️ 找不到视频文件: {video_path}")
                    print("💡 请确保action1.mp4文件在data/videos/目录中")
                else:
                    print("\n请选择演示版本:")
                    print("  1. Qt6专业界面 (需要PyQt6)")
                    print("  2. OpenCV可视化版 (推荐)")
                    print("  3. 控制台版 (最稳定)")

                    sub_choice = input("请选择 (1-3): ").strip()

                    if sub_choice == "1":
                        try:
                            import PyQt6
                            subprocess.run([sys.executable, "demo_helmet_detection.py"])
                        except ImportError:
                            print("❌ Qt6未安装，启动修复版...")
                            subprocess.run([sys.executable, "demo_helmet_fixed.py"])
                    elif sub_choice == "2":
                        subprocess.run([sys.executable, "demo_helmet_fixed.py"])
                    elif sub_choice == "3":
                        subprocess.run([sys.executable, "demo_helmet_console.py"])
                    else:
                        print("❌ 无效选择，启动修复版...")
                        subprocess.run([sys.executable, "demo_helmet_fixed.py"])
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '6':
            # Web演示界面
            if check_dependencies():
                print("🚀 启动Web演示界面...")
                print("💡 启动后请在浏览器中访问: http://localhost:8080")
                subprocess.run([sys.executable, "demo_web.py"])
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '7':
            # API服务模式
            if check_dependencies():
                download_models()
                print("💡 启动后请访问: http://localhost:8000/docs")
                run_system("api")
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '8':
            # 简单演示模式
            if check_dependencies():
                download_models()
                print("🚀 启动简单演示模式...")
                subprocess.run([sys.executable, "-W", "ignore", "main.py", "--mode", "demo"])
            else:
                print("\n❌ 请先安装依赖包")
        elif choice == '9':
            # 系统测试
            print("🔧 运行系统测试...")
            subprocess.run([sys.executable, "test_final.py"])
        elif choice == '10':
            # 安装依赖
            install_dependencies()
        elif choice == '11':
            # 检查系统
            check_system()


if __name__ == "__main__":
    main()
