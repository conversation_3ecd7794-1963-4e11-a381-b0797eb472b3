#!/usr/bin/env python3
"""
Ollama 功能测试脚本
测试Ollama服务连接和模型功能
"""

import sys
import time
import asyncio
import requests
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.llm_config import OLLAMA_CONFIG
from core.llm.ollama_client import OllamaClient, OllamaClientSync

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🤖 {title}")
    print("=" * 60)

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{test_name}: {status}")
    if details:
        print(f"   详情: {details}")

def test_ollama_service():
    """测试Ollama服务连接"""
    print_header("Ollama服务连接测试")
    
    try:
        base_url = OLLAMA_CONFIG.get("base_url", "http://localhost:11434")
        
        # 测试版本接口
        response = requests.get(f"{base_url}/api/version", timeout=5)
        
        if response.status_code == 200:
            version_info = response.json()
            print_result("服务连接", True, f"版本: {version_info.get('version', 'unknown')}")
            return True
        else:
            print_result("服务连接", False, f"状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print_result("服务连接", False, "无法连接到Ollama服务")
        print("💡 请确保Ollama服务正在运行: ollama serve")
        return False
    except Exception as e:
        print_result("服务连接", False, str(e))
        return False

def test_available_models():
    """测试可用模型"""
    print_header("可用模型测试")
    
    try:
        base_url = OLLAMA_CONFIG.get("base_url", "http://localhost:11434")
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            
            if models:
                print_result("模型列表获取", True, f"发现 {len(models)} 个模型")
                
                print("\n📦 已安装的模型:")
                for model in models:
                    name = model.get("name", "unknown")
                    size = model.get("size", 0)
                    size_mb = size / (1024 * 1024) if size > 0 else 0
                    modified = model.get("modified_at", "")
                    
                    print(f"  - {name} ({size_mb:.1f}MB) - {modified[:10]}")
                
                return models
            else:
                print_result("模型列表获取", False, "未发现已安装的模型")
                print("💡 请先下载模型: ollama pull qwen:7b-chat")
                return []
        else:
            print_result("模型列表获取", False, f"状态码: {response.status_code}")
            return []
            
    except Exception as e:
        print_result("模型列表获取", False, str(e))
        return []

def test_model_generation(model_name):
    """测试模型生成功能"""
    print_header(f"模型生成测试 - {model_name}")
    
    try:
        client = OllamaClientSync(OLLAMA_CONFIG)
        
        # 测试简单对话
        print("🔄 测试简单对话...")
        start_time = time.time()
        
        response = client.generate_response(
            model_name, 
            "你好，请简单介绍一下自己，不超过50字。"
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response and len(response.strip()) > 0:
            print_result("简单对话", True, f"响应时间: {response_time:.2f}秒")
            print(f"📝 模型响应: {response[:100]}...")
        else:
            print_result("简单对话", False, "模型无响应")
            return False
        
        # 测试交通报告生成
        print("\n🔄 测试交通报告生成...")
        start_time = time.time()
        
        traffic_prompt = """请根据以下交通检测数据生成一份简短的交通报告：

检测时间: 2024-01-15 14:30:00
检测位置: 市中心路口
检测数据:
- 车辆: 15辆
- 行人: 8人
- 自行车: 3辆

请分析交通状况并提供建议，不超过100字。"""

        response = client.generate_response(model_name, traffic_prompt)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response and len(response.strip()) > 0:
            print_result("交通报告生成", True, f"响应时间: {response_time:.2f}秒")
            print(f"📊 报告内容: {response[:150]}...")
        else:
            print_result("交通报告生成", False, "无法生成报告")
            return False
        
        return True
        
    except Exception as e:
        print_result("模型生成测试", False, str(e))
        return False

async def test_async_client():
    """测试异步客户端"""
    print_header("异步客户端测试")
    
    try:
        client = OllamaClient(OLLAMA_CONFIG)
        
        # 测试连接
        connected = await client.check_connection()
        print_result("异步连接", connected)
        
        if not connected:
            return False
        
        # 测试异步生成
        print("\n🔄 测试异步生成...")
        start_time = time.time()
        
        # 获取第一个可用模型进行测试
        available_models = await client.list_models()
        if available_models:
            test_model = available_models[0]["name"]
        else:
            test_model = "qwen3:latest"  # 使用默认模型

        response = await client.generate_response(
            test_model,
            "请用一句话描述人工智能的作用。"
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response and len(response.strip()) > 0:
            print_result("异步生成", True, f"响应时间: {response_time:.2f}秒")
            print(f"📝 响应内容: {response[:100]}...")
        else:
            print_result("异步生成", False, "无响应")
            return False
        
        return True
        
    except Exception as e:
        print_result("异步客户端测试", False, str(e))
        return False

def test_recommended_models():
    """测试推荐模型配置"""
    print_header("推荐模型配置测试")
    
    try:
        client = OllamaClientSync(OLLAMA_CONFIG)
        
        # 测试推荐模型获取
        recommended_models = OLLAMA_CONFIG.get("recommended", {})
        
        print("🎯 推荐模型配置:")
        for task_type, model_name in recommended_models.items():
            print(f"  - {task_type}: {model_name}")
        
        # 测试获取推荐模型
        general_model = client.get_recommended_model("general")
        chinese_model = client.get_recommended_model("chinese")
        coding_model = client.get_recommended_model("coding")
        
        print_result("推荐模型获取", True, 
                    f"通用: {general_model}, 中文: {chinese_model}, 编程: {coding_model}")
        
        return True
        
    except Exception as e:
        print_result("推荐模型配置测试", False, str(e))
        return False

def test_model_info():
    """测试模型信息获取"""
    print_header("模型信息测试")
    
    try:
        models = OLLAMA_CONFIG.get("models", {})
        
        print("📋 支持的模型列表:")
        for model_name, model_config in models.items():
            description = model_config.get("description", "无描述")
            context_length = model_config.get("context_length", "未知")
            
            print(f"  - {model_name}: {description}")
            print(f"    上下文长度: {context_length}")
        
        print_result("模型信息获取", True, f"支持 {len(models)} 个模型")
        return True
        
    except Exception as e:
        print_result("模型信息测试", False, str(e))
        return False

def show_installation_guide():
    """显示安装指南"""
    print_header("Ollama安装指南")
    
    print("""
🚀 Ollama快速安装:

1. 安装Ollama:
   curl -fsSL https://ollama.ai/install.sh | sh

2. 启动服务:
   ollama serve

3. 下载推荐模型:
   ollama pull qwen:7b-chat
   ollama pull llama2:7b-chat
   ollama pull phi:2.7b

4. 验证安装:
   ollama list
   ollama run qwen:7b-chat "你好"

5. 配置项目:
   编辑 .env 文件:
   LLM_PROVIDER=ollama
   OLLAMA_BASE_URL=http://localhost:11434
   OLLAMA_MODEL=qwen:7b-chat

📚 详细指南: 查看 OLLAMA_SETUP_GUIDE.md
""")

def main():
    """主函数"""
    print_header("Ollama功能测试")
    
    # 测试项目列表
    tests = [
        ("Ollama服务连接", test_ollama_service),
        ("可用模型检查", test_available_models),
        ("推荐模型配置", test_recommended_models),
        ("模型信息获取", test_model_info),
    ]
    
    results = {}
    
    # 运行基础测试
    for test_name, test_func in tests:
        try:
            if test_name == "可用模型检查":
                models = test_func()
                results[test_name] = len(models) > 0
                
                # 如果有可用模型，测试第一个模型
                if models:
                    first_model = models[0]["name"]
                    model_test_result = test_model_generation(first_model)
                    results["模型生成功能"] = model_test_result
            else:
                results[test_name] = test_func()
        except Exception as e:
            print_result(test_name, False, f"测试异常: {e}")
            results[test_name] = False
    
    # 运行异步测试
    try:
        async_result = asyncio.run(test_async_client())
        results["异步客户端"] = async_result
    except Exception as e:
        print_result("异步客户端", False, f"测试异常: {e}")
        results["异步客户端"] = False
    
    # 显示测试总结
    print_header("测试结果总结")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！Ollama配置正确，可以正常使用。")
        print("💡 现在可以在系统中使用Ollama作为LLM提供商。")
    elif passed > 0:
        print("\n⚠️ 部分测试通过，请检查失败的项目。")
    else:
        print("\n❌ 所有测试失败，请检查Ollama安装和配置。")
        show_installation_guide()

if __name__ == "__main__":
    main()
