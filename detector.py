"""
交通监控系统核心检测模块
封装YOLO模型和检测逻辑
"""
import sys
import torch
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass

# 添加yolov5路径
sys.path.append("yolov5")
from yolov5.models.experimental import attempt_load
from yolov5.utils.general import non_max_suppression

from config import Config
from traffic_utils import setup_device, preprocess_frame, scale_coords_back, get_bbox_center, get_bbox_area


@dataclass
class Detection:
    """检测结果数据类"""
    bbox: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    confidence: float
    class_id: int
    class_name: str
    center: Tuple[float, float]
    area: float


class TrafficDetector:
    """交通检测器类，封装YOLO模型和检测逻辑"""
    
    def __init__(self, config: Config):
        """
        初始化检测器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.device = setup_device(config.model.device)
        self.model = None
        self.model_names = []
        
        self._load_model()
    
    def _load_model(self):
        """加载YOLO模型"""
        try:
            print(f"Loading model from {self.config.model.model_path}")
            self.model = attempt_load(self.config.model.model_path)
            self.model.to(self.device).eval()
            self.model_names = self.model.names
            print(f"Model loaded successfully on {self.device}")
            print(f"Model classes: {self.model_names}")
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
    
    def detect(self, frame: np.ndarray) -> Tuple[List[Detection], List[Detection]]:
        """
        检测单帧图像中的车辆和行人
        
        Args:
            frame: 输入图像帧
        
        Returns:
            vehicles: 车辆检测结果列表
            persons: 行人检测结果列表
        """
        if self.model is None:
            raise RuntimeError("Model not loaded")
        
        # 预处理
        img_tensor, gain, pad = preprocess_frame(
            frame, 
            self.config.model.input_size, 
            self.device
        )
        
        # 推理
        with torch.no_grad():
            pred = self.model(img_tensor)[0]
            pred = non_max_suppression(
                pred, 
                conf_thres=self.config.model.conf_threshold,
                iou_thres=self.config.model.iou_threshold
            )
        
        # 解析检测结果
        vehicles = []
        persons = []
        
        for det in pred:
            if det is not None and len(det):
                for *xyxy, conf, cls in det:
                    # 坐标转换回原图
                    x1, y1, x2, y2 = scale_coords_back(xyxy, gain, pad)
                    
                    # 创建检测对象
                    class_id = int(cls)
                    class_name = self.model_names[class_id]
                    bbox = (x1, y1, x2, y2)
                    center = get_bbox_center(bbox)
                    area = get_bbox_area(bbox)
                    
                    detection = Detection(
                        bbox=bbox,
                        confidence=float(conf),
                        class_id=class_id,
                        class_name=class_name,
                        center=center,
                        area=area
                    )
                    
                    # 分类存储
                    if class_name in self.config.detection.vehicle_classes:
                        vehicles.append(detection)
                    elif class_name in self.config.detection.person_classes:
                        persons.append(detection)
        
        return vehicles, persons
    
    def batch_detect(self, frames: List[np.ndarray]) -> List[Tuple[List[Detection], List[Detection]]]:
        """
        批量检测多帧图像
        
        Args:
            frames: 输入图像帧列表
        
        Returns:
            检测结果列表，每个元素为(vehicles, persons)
        """
        results = []
        for frame in frames:
            vehicles, persons = self.detect(frame)
            results.append((vehicles, persons))
        return results
    
    def get_detection_summary(self, vehicles: List[Detection], persons: List[Detection]) -> Dict:
        """
        获取检测结果摘要
        
        Args:
            vehicles: 车辆检测结果
            persons: 行人检测结果
        
        Returns:
            检测摘要字典
        """
        vehicle_count = len(vehicles)
        person_count = len(persons)
        
        # 计算平均置信度
        avg_vehicle_conf = np.mean([v.confidence for v in vehicles]) if vehicles else 0.0
        avg_person_conf = np.mean([p.confidence for p in persons]) if persons else 0.0
        
        # 统计各类别数量
        vehicle_class_counts = {}
        for vehicle in vehicles:
            class_name = vehicle.class_name
            vehicle_class_counts[class_name] = vehicle_class_counts.get(class_name, 0) + 1
        
        return {
            'vehicle_count': vehicle_count,
            'person_count': person_count,
            'avg_vehicle_confidence': avg_vehicle_conf,
            'avg_person_confidence': avg_person_conf,
            'vehicle_class_counts': vehicle_class_counts,
            'total_detections': vehicle_count + person_count
        }
    
    def filter_by_confidence(self, detections: List[Detection], min_confidence: float) -> List[Detection]:
        """
        根据置信度过滤检测结果
        
        Args:
            detections: 检测结果列表
            min_confidence: 最小置信度阈值
        
        Returns:
            过滤后的检测结果
        """
        return [det for det in detections if det.confidence >= min_confidence]
    
    def filter_by_area(self, detections: List[Detection], min_area: float, max_area: float = float('inf')) -> List[Detection]:
        """
        根据面积过滤检测结果
        
        Args:
            detections: 检测结果列表
            min_area: 最小面积
            max_area: 最大面积
        
        Returns:
            过滤后的检测结果
        """
        return [det for det in detections if min_area <= det.area <= max_area]
    
    def get_model_info(self) -> Dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        return {
            'model_path': self.config.model.model_path,
            'device': str(self.device),
            'input_size': self.config.model.input_size,
            'conf_threshold': self.config.model.conf_threshold,
            'iou_threshold': self.config.model.iou_threshold,
            'class_names': self.model_names,
            'num_classes': len(self.model_names)
        }
