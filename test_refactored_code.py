"""
测试重构后的代码结构
不依赖完整的YOLO模型，仅测试代码结构
"""
import sys
import numpy as np
from typing import List

# 测试配置模块
def test_config():
    """测试配置模块"""
    print("=== 测试配置模块 ===")
    try:
        from config import Config, default_config
        
        # 测试默认配置
        config = default_config
        print(f"✓ 默认配置加载成功")
        print(f"  模型路径: {config.model.model_path}")
        print(f"  视频路径: {config.video.video_path}")
        print(f"  输入尺寸: {config.model.input_size}")
        print(f"  置信度阈值: {config.model.conf_threshold}")
        
        # 测试自定义配置
        custom_config = Config()
        custom_config.model.conf_threshold = 0.3
        custom_config.video.frame_skip = 2
        print(f"✓ 自定义配置创建成功")
        print(f"  修改后置信度阈值: {custom_config.model.conf_threshold}")
        print(f"  修改后跳帧数: {custom_config.video.frame_skip}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False


def test_utils():
    """测试工具函数模块"""
    print("\n=== 测试工具函数模块 ===")
    try:
        from traffic_utils import (
            compute_iou, compute_distance, get_bbox_center, 
            get_bbox_area, safe_divide, clamp,
            FrameBuffer, PerformanceMonitor
        )
        
        # 测试IoU计算
        box1 = (10, 10, 50, 50)
        box2 = (30, 30, 70, 70)
        iou = compute_iou(box1, box2)
        print(f"✓ IoU计算: {iou:.3f}")
        
        # 测试距离计算
        point1 = (0, 0)
        point2 = (3, 4)
        distance = compute_distance(point1, point2)
        print(f"✓ 距离计算: {distance:.3f}")
        
        # 测试边界框中心
        bbox = (10, 20, 50, 60)
        center = get_bbox_center(bbox)
        print(f"✓ 边界框中心: {center}")
        
        # 测试边界框面积
        area = get_bbox_area(bbox)
        print(f"✓ 边界框面积: {area}")
        
        # 测试安全除法
        result = safe_divide(10, 2)
        result_zero = safe_divide(10, 0, default=999)
        print(f"✓ 安全除法: {result}, {result_zero}")
        
        # 测试限制函数
        clamped = clamp(15, 0, 10)
        print(f"✓ 限制函数: {clamped}")
        
        # 测试帧缓冲区
        buffer = FrameBuffer(buffer_size=3)
        fake_frame = np.zeros((100, 100, 3), dtype=np.uint8)
        buffer.add_frame(fake_frame, 1)
        buffer.add_frame(fake_frame, 2)
        is_full = buffer.add_frame(fake_frame, 3)
        print(f"✓ 帧缓冲区: 大小={buffer.size()}, 已满={is_full}")
        
        # 测试性能监控
        monitor = PerformanceMonitor()
        monitor.record_frame(0.05, 100.0)
        monitor.record_frame(0.03, 105.0)
        stats = monitor.get_stats()
        print(f"✓ 性能监控: 平均FPS={stats.get('avg_fps', 0):.1f}")
        
        return True
    except Exception as e:
        print(f"✗ 工具函数模块测试失败: {e}")
        return False


def test_data_manager():
    """测试数据管理模块"""
    print("\n=== 测试数据管理模块 ===")
    try:
        from config import default_config
        from data_manager import DataManager
        from detector import Detection
        
        # 创建数据管理器
        data_manager = DataManager(default_config)
        print("✓ 数据管理器创建成功")
        
        # 创建模拟检测结果
        vehicles = [
            Detection(
                bbox=(100, 100, 200, 200),
                confidence=0.8,
                class_id=0,
                class_name="car",
                center=(150, 150),
                area=10000
            ),
            Detection(
                bbox=(300, 300, 400, 400),
                confidence=0.9,
                class_id=1,
                class_name="truck",
                center=(350, 350),
                area=10000
            )
        ]
        
        persons = [
            Detection(
                bbox=(50, 50, 80, 120),
                confidence=0.7,
                class_id=2,
                class_name="person",
                center=(65, 85),
                area=2100
            )
        ]
        
        # 添加帧数据
        for i in range(5):
            frame_data = data_manager.add_frame_data(i, vehicles, persons, 0.05)
            print(f"  帧 {i}: 车辆={frame_data.vehicle_count}, 行人={frame_data.person_count}, "
                  f"车辆密度={frame_data.vehicle_density:.4f}/m²")
        
        # 获取统计信息
        stats = data_manager.get_current_statistics()
        print(f"✓ 统计信息获取成功")
        print(f"  平均车辆数: {stats['avg_vehicle_count']:.2f}")
        print(f"  平均行人数: {stats['avg_person_count']:.2f}")
        print(f"  估算区域面积: {stats['estimated_area_m2']:.2f} m²")
        
        # 获取历史数据
        vehicle_densities, person_densities = data_manager.get_density_history()
        print(f"✓ 历史数据获取成功: {len(vehicle_densities)} 个数据点")
        
        return True
    except Exception as e:
        print(f"✗ 数据管理模块测试失败: {e}")
        return False


def test_visualization():
    """测试可视化模块"""
    print("\n=== 测试可视化模块 ===")
    try:
        from config import default_config
        from visualization import Visualizer
        import numpy as np
        
        # 创建可视化器
        visualizer = Visualizer(default_config)
        print("✓ 可视化器创建成功")
        
        # 创建模拟密度数据
        vehicle_densities = [0.1 * i + 0.05 * np.sin(i * 0.1) for i in range(20)]
        person_densities = [0.05 * i + 0.02 * np.cos(i * 0.15) for i in range(20)]
        
        # 绘制密度图
        plot_img = visualizer.draw_density_plot(vehicle_densities, person_densities)
        print(f"✓ 密度图绘制成功: 尺寸={plot_img.shape}")
        
        # 测试颜色配置
        print(f"✓ 颜色配置: 车辆={visualizer.colors['vehicle']}, 事故={visualizer.colors['accident']}")
        
        return True
    except Exception as e:
        print(f"✗ 可视化模块测试失败: {e}")
        return False


def test_accident_detector():
    """测试事故检测模块"""
    print("\n=== 测试事故检测模块 ===")
    try:
        from config import default_config
        from accident_detector import AccidentDetector
        from detector import Detection
        
        # 创建事故检测器
        accident_detector = AccidentDetector(default_config)
        print("✓ 事故检测器创建成功")
        
        # 创建重叠的车辆（模拟碰撞）
        vehicles = [
            Detection(
                bbox=(100, 100, 200, 200),
                confidence=0.9,
                class_id=0,
                class_name="car",
                center=(150, 150),
                area=10000
            ),
            Detection(
                bbox=(150, 150, 250, 250),  # 重叠的边界框
                confidence=0.8,
                class_id=0,
                class_name="car",
                center=(200, 200),
                area=10000
            )
        ]
        
        # 模拟多帧检测
        for frame_id in range(10):
            accident_info = accident_detector.update(vehicles, [], frame_id)
            
            if accident_info['detected']:
                print(f"✓ 事故检测成功: 帧 {frame_id}")
                print(f"  事故类型: {accident_info['type']}")
                print(f"  置信度: {accident_info['confidence']:.2f}")
                print(f"  描述: {accident_info['description']}")
                break
        else:
            print("✓ 事故检测器运行正常（未检测到事故）")
        
        # 获取跟踪信息
        tracking_info = accident_detector.get_tracking_info()
        print(f"✓ 跟踪信息: 跟踪对象数={tracking_info['num_tracked_objects']}")
        
        return True
    except Exception as e:
        print(f"✗ 事故检测模块测试失败: {e}")
        return False


def test_integration():
    """测试模块集成"""
    print("\n=== 测试模块集成 ===")
    try:
        from config import Config
        from data_manager import DataManager
        from visualization import Visualizer
        from accident_detector import AccidentDetector
        from detector import Detection
        
        # 创建配置
        config = Config()
        config.system.max_memory_frames = 10
        print("✓ 配置创建成功")
        
        # 创建各个模块
        data_manager = DataManager(config)
        visualizer = Visualizer(config)
        accident_detector = AccidentDetector(config)
        print("✓ 所有模块创建成功")
        
        # 模拟处理流程
        for frame_id in range(5):
            # 模拟检测结果
            vehicles = [
                Detection(
                    bbox=(100 + frame_id * 10, 100, 200 + frame_id * 10, 200),
                    confidence=0.8,
                    class_id=0,
                    class_name="car",
                    center=(150 + frame_id * 10, 150),
                    area=10000
                )
            ]
            persons = []
            
            # 事故检测
            accident_info = accident_detector.update(vehicles, persons, frame_id)
            
            # 数据管理
            frame_data = data_manager.add_frame_data(frame_id, vehicles, persons, 0.05)
            
            # 可视化数据准备
            vehicle_densities, person_densities = data_manager.get_density_history()
            
            print(f"  帧 {frame_id}: 处理完成")
        
        print("✓ 模块集成测试成功")
        return True
    except Exception as e:
        print(f"✗ 模块集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试重构后的交通监控系统")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置模块", test_config()))
    test_results.append(("工具函数模块", test_utils()))
    test_results.append(("数据管理模块", test_data_manager()))
    test_results.append(("可视化模块", test_visualization()))
    test_results.append(("事故检测模块", test_accident_detector()))
    test_results.append(("模块集成", test_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构后的代码结构正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
