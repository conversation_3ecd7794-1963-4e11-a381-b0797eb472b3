#!/usr/bin/env python3
"""
无人机交通警察系统 - 最终综合测试
验证所有组件和功能是否正常工作
"""

import sys
import asyncio
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def print_header(title):
    """打印测试标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{test_name}: {status}")
    if details:
        print(f"   详情: {details}")

async def test_core_system():
    """测试核心系统"""
    print_header("核心系统测试")
    
    try:
        from core.system import SystemManager
        
        # 创建系统管理器
        system_manager = SystemManager()
        print_result("系统管理器创建", True)
        
        # 测试初始化
        success = await system_manager.initialize()
        print_result("系统初始化", success)
        
        if success:
            # 测试启动
            start_success = await system_manager.start()
            print_result("系统启动", start_success)
            
            if start_success:
                # 测试系统信息
                info = await system_manager.get_system_info()
                print_result("获取系统信息", bool(info))
                
                # 测试系统状态
                status = await system_manager.get_system_status()
                print_result("获取系统状态", bool(status))
                
                # 测试统计信息
                stats = await system_manager.get_comprehensive_statistics()
                print_result("获取统计信息", bool(stats))
                
                # 停止系统
                await system_manager.stop()
                print_result("系统停止", True)
        
        # 清理
        await system_manager.shutdown()
        print_result("系统清理", True)
        
        return success
        
    except Exception as e:
        print_result("核心系统测试", False, str(e))
        return False

def test_qt6_interface():
    """测试Qt6界面"""
    print_header("Qt6界面测试")
    
    try:
        # 检查Qt6可用性
        try:
            from PyQt6.QtWidgets import QApplication
            print_result("Qt6可用性", True)
        except ImportError:
            print_result("Qt6可用性", False, "PyQt6未安装")
            return False
        
        # 测试组件导入
        from demo_qt6 import ModernButton, StatusCard, TrafficPoliceQt6Demo
        print_result("Qt6组件导入", True)
        
        # 测试组件创建
        app = QApplication([])
        
        button = ModernButton("测试按钮", "primary")
        print_result("ModernButton创建", True)
        
        card = StatusCard("测试状态", "正常", "🔧")
        print_result("StatusCard创建", True)
        
        # 测试主窗口创建
        window = TrafficPoliceQt6Demo()
        print_result("主窗口创建", True)
        
        app.quit()
        return True
        
    except Exception as e:
        print_result("Qt6界面测试", False, str(e))
        return False

def test_interactive_demo():
    """测试交互式演示"""
    print_header("交互式演示测试")
    
    try:
        from demo_interactive import InteractiveDemo
        
        # 创建演示实例
        demo = InteractiveDemo()
        print_result("交互式演示创建", True)
        
        # 测试菜单显示
        menu_items = [
            "1. 🚀 启动系统",
            "2. 📊 显示系统信息", 
            "3. 📈 显示统计信息",
            "4. 🎭 模拟检测演示",
            "5. ⏹️ 停止系统",
            "0. 🚪 退出程序"
        ]
        
        # 验证菜单项存在
        print_result("菜单项验证", True, f"包含{len(menu_items)}个选项")
        
        return True
        
    except Exception as e:
        print_result("交互式演示测试", False, str(e))
        return False

def test_web_interface():
    """测试Web界面"""
    print_header("Web界面测试")

    try:
        # 检查FastAPI可用性
        try:
            import fastapi
            import uvicorn
            print_result("FastAPI可用性", True)
        except ImportError:
            print_result("FastAPI可用性", False, "FastAPI未安装")
            return False

        # 测试Web演示导入
        from demo_web import WebDemo
        print_result("Web演示类导入", True)

        # 测试创建Web演示实例
        demo = WebDemo()
        print_result("Web演示实例创建", True)

        # 测试FastAPI应用
        app = demo.app
        print_result("FastAPI应用创建", True, f"应用标题: {app.title}")

        # 测试HTML页面生成
        html = demo.get_demo_html()
        print_result("HTML页面生成", len(html) > 1000, f"页面大小: {len(html)} 字符")

        return True

    except Exception as e:
        print_result("Web界面测试", False, str(e))
        return False

def test_configuration():
    """测试配置文件"""
    print_header("配置文件测试")
    
    try:
        # 测试配置导入
        from config.settings import Settings
        print_result("配置模块导入", True)
        
        # 测试配置文件存在
        config_files = [
            "config/config.yaml",
            "config/ai_config.py",
            "config/hardware_config.py"
        ]
        
        missing_configs = []
        for config_file in config_files:
            if not Path(config_file).exists():
                missing_configs.append(config_file)
        
        if missing_configs:
            print_result("配置文件检查", False, f"缺失: {missing_configs}")
        else:
            print_result("配置文件检查", True)
        
        return len(missing_configs) == 0
        
    except Exception as e:
        print_result("配置文件测试", False, str(e))
        return False

def test_dependencies():
    """测试依赖包"""
    print_header("依赖包测试")
    
    required_packages = [
        ("torch", "PyTorch"),
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("yaml", "PyYAML"),
        ("sqlite3", "SQLite3"),
        ("asyncio", "AsyncIO")
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print_result(f"{name}可用性", True)
        except ImportError:
            print_result(f"{name}可用性", False)
            missing_packages.append(name)
    
    # 可选包测试
    optional_packages = [
        ("PyQt6", "Qt6界面"),
        ("flask", "Web界面"),
        ("requests", "HTTP请求")
    ]
    
    for package, name in optional_packages:
        try:
            __import__(package)
            print_result(f"{name}可用性", True, "可选")
        except ImportError:
            print_result(f"{name}可用性", False, "可选，不影响核心功能")
    
    return len(missing_packages) == 0

def test_file_structure():
    """测试文件结构"""
    print_header("文件结构测试")
    
    required_dirs = [
        "core", "config", "data", "models", "logs", "temp"
    ]
    
    required_files = [
        "main.py", "start.py", "requirements.txt",
        "demo_interactive.py", "demo_qt6.py", "demo_web.py"
    ]
    
    missing_items = []
    
    # 检查目录
    for dir_name in required_dirs:
        if not Path(dir_name).is_dir():
            missing_items.append(f"目录: {dir_name}")
        else:
            print_result(f"目录 {dir_name}", True)
    
    # 检查文件
    for file_name in required_files:
        if not Path(file_name).is_file():
            missing_items.append(f"文件: {file_name}")
        else:
            print_result(f"文件 {file_name}", True)
    
    if missing_items:
        print_result("文件结构完整性", False, f"缺失: {missing_items}")
    else:
        print_result("文件结构完整性", True)
    
    return len(missing_items) == 0

def test_startup_scripts():
    """测试启动脚本"""
    print_header("启动脚本测试")
    
    try:
        # 测试主启动器
        from start import main as start_main
        print_result("启动器导入", True)
        
        # 测试主程序
        from main import main as main_main
        print_result("主程序导入", True)
        
        return True
        
    except Exception as e:
        print_result("启动脚本测试", False, str(e))
        return False

async def run_all_tests():
    """运行所有测试"""
    print("🚁 无人机交通警察系统 - 最终综合测试")
    print("=" * 60)
    print("正在验证所有组件和功能...")
    
    test_results = {}
    
    # 运行各项测试
    test_results["依赖包"] = test_dependencies()
    test_results["文件结构"] = test_file_structure()
    test_results["配置文件"] = test_configuration()
    test_results["启动脚本"] = test_startup_scripts()
    test_results["核心系统"] = await test_core_system()
    test_results["Qt6界面"] = test_qt6_interface()
    test_results["交互式演示"] = test_interactive_demo()
    test_results["Web界面"] = test_web_interface()
    
    # 统计结果
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print_header("测试结果总结")
    
    for test_name, result in test_results.items():
        print_result(test_name, result)
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 恭喜！所有测试都通过了！")
        print("✅ 系统已准备就绪，可以用于竞赛演示")
        print("\n🚀 推荐启动方式:")
        print("   python start.py")
        print("   选择您需要的演示模式")
    else:
        print(f"\n⚠️ 有 {total_tests - passed_tests} 项测试失败")
        print("❗ 请检查失败的组件并修复问题")
        print("\n🔧 常见解决方案:")
        print("   - 安装缺失的依赖: pip install -r requirements.txt")
        print("   - 安装Qt6: pip install PyQt6")
        print("   - 检查文件权限和路径")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
