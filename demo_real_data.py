#!/usr/bin/env python3
"""
真实数据演示脚本
集成真实的视频检测数据到Qt6和Web界面
"""

import sys
import cv2
import time
import asyncio
import threading
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QTimer, pyqtSignal, QObject
    QT6_AVAILABLE = True
except ImportError:
    QT6_AVAILABLE = False

class RealDataProcessor(QObject):
    """真实数据处理器"""
    
    # Qt信号
    detection_result = pyqtSignal(dict) if QT6_AVAILABLE else None
    
    def __init__(self):
        if QT6_AVAILABLE:
            super().__init__()
        
        self.video_path = "data/videos/action1.mp4"
        self.cap = None
        self.yolo_model = None
        self.running = False
        self.frame_count = 0
        self.total_detections = 0
        
        # 回调函数列表（用于Web界面）
        self.callbacks = []
        
    def add_callback(self, callback):
        """添加检测结果回调函数"""
        self.callbacks.append(callback)
    
    def initialize_yolo(self):
        """初始化YOLO模型"""
        try:
            # 尝试加载YOLO模型
            import torch
            
            # 检查可用的模型文件
            model_paths = [
                "yolov8s.pt",
                "yolov8n.pt",
                "models/yolov8s.pt",
                "models/yolov8n.pt"
            ]
            
            for model_path in model_paths:
                if Path(model_path).exists():
                    try:
                        from ultralytics import YOLO
                        self.yolo_model = YOLO(model_path)
                        print(f"✅ YOLO模型加载成功: {model_path}")
                        return True
                    except ImportError:
                        # 尝试使用torch hub
                        try:
                            self.yolo_model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                            print("✅ YOLOv5模型加载成功")
                            return True
                        except Exception as e:
                            print(f"⚠️ torch hub加载失败: {e}")
            
            print("⚠️ 未找到YOLO模型，将使用运动检测")
            return False
            
        except Exception as e:
            print(f"❌ YOLO初始化失败: {e}")
            return False
    
    def initialize_video(self):
        """初始化视频"""
        try:
            if not Path(self.video_path).exists():
                print(f"❌ 视频文件不存在: {self.video_path}")
                return False
            
            self.cap = cv2.VideoCapture(self.video_path)
            
            if not self.cap.isOpened():
                print(f"❌ 无法打开视频文件: {self.video_path}")
                return False
            
            # 获取视频信息
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            print(f"✅ 视频加载成功:")
            print(f"   分辨率: {width}x{height}")
            print(f"   总帧数: {total_frames}")
            print(f"   帧率: {fps:.2f} FPS")
            
            return True
            
        except Exception as e:
            print(f"❌ 视频初始化失败: {e}")
            return False
    
    def detect_objects(self, frame):
        """目标检测"""
        detections = []
        
        try:
            if self.yolo_model:
                # 使用YOLO检测
                results = self.yolo_model(frame)
                
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = box.conf[0].cpu().numpy()
                            class_id = int(box.cls[0].cpu().numpy())
                            
                            # COCO类别名称
                            class_names = ['person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck']
                            class_name = class_names[class_id] if class_id < len(class_names) else f'class_{class_id}'
                            
                            # 只保留交通相关的目标
                            if class_name in ['person', 'bicycle', 'car', 'motorcycle', 'bus', 'truck'] and confidence > 0.5:
                                detections.append({
                                    'class': class_name,
                                    'confidence': float(confidence),
                                    'bbox': [int(x1), int(y1), int(x2-x1), int(y2-y1)],
                                    'area': int((x2-x1) * (y2-y1)),
                                    'id': len(detections) + 1,
                                    'speed': 0  # 实际应用中可以通过跟踪计算
                                })
            
            else:
                # 使用运动检测作为备选
                if hasattr(self, 'prev_frame'):
                    diff = cv2.absdiff(frame, self.prev_frame)
                    gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
                    _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)
                    
                    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                    
                    for i, contour in enumerate(contours):
                        area = cv2.contourArea(contour)
                        if area > 500:
                            x, y, w, h = cv2.boundingRect(contour)
                            
                            if area > 2000:
                                obj_class = "car"
                                confidence = 0.75
                            else:
                                obj_class = "person"
                                confidence = 0.65
                            
                            detections.append({
                                'class': obj_class,
                                'confidence': confidence,
                                'bbox': [x, y, w, h],
                                'area': area,
                                'id': i + 1,
                                'speed': 0
                            })
                
                self.prev_frame = frame.copy()
            
        except Exception as e:
            print(f"检测错误: {e}")
        
        return detections
    
    def process_frame(self):
        """处理单帧"""
        if not self.cap or not self.running:
            return False
        
        ret, frame = self.cap.read()
        if not ret:
            # 视频结束，重新开始
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            ret, frame = self.cap.read()
            if not ret:
                return False
        
        self.frame_count += 1
        
        # 目标检测
        detections = self.detect_objects(frame)
        self.total_detections += len(detections)
        
        # 构建结果数据
        result = {
            'frame': self.frame_count,
            'timestamp': time.strftime("%H:%M:%S"),
            'detections': detections,
            'fps': 30,  # 模拟FPS
            'total_detections': self.total_detections,
            'frame_shape': frame.shape
        }
        
        # 发送Qt信号
        if QT6_AVAILABLE and self.detection_result:
            self.detection_result.emit(result)
        
        # 调用回调函数
        for callback in self.callbacks:
            try:
                callback(result)
            except Exception as e:
                print(f"回调函数错误: {e}")
        
        return True
    
    def start_processing(self):
        """开始处理"""
        if not self.initialize_video():
            return False
        
        self.initialize_yolo()
        self.running = True
        self.frame_count = 0
        self.total_detections = 0
        
        print("🎬 开始真实数据处理...")
        return True
    
    def stop_processing(self):
        """停止处理"""
        self.running = False
        if self.cap:
            self.cap.release()
            self.cap = None
        print("⏹️ 数据处理已停止")

class RealDataQt6Demo:
    """Qt6真实数据演示"""
    
    def __init__(self):
        if not QT6_AVAILABLE:
            print("❌ PyQt6不可用")
            return
        
        self.app = QApplication(sys.argv)
        self.processor = RealDataProcessor()
        self.timer = QTimer()
        
        # 连接信号
        self.processor.detection_result.connect(self.handle_detection_result)
        self.timer.timeout.connect(self.processor.process_frame)
        
        # 导入并创建Qt6界面
        from demo_qt6 import TrafficPoliceDemo
        self.demo = TrafficPoliceDemo()
        
        # 替换模拟检测为真实检测
        self.demo.simulate_detection = self.get_real_detection_data
        
    def get_real_detection_data(self):
        """获取真实检测数据（替换模拟数据）"""
        # 这个方法会被Qt6演示调用，但我们通过信号处理真实数据
        return []
    
    def handle_detection_result(self, result):
        """处理检测结果"""
        # 更新Qt6界面
        self.demo.update_detection_result(result)
    
    def start(self):
        """启动演示"""
        if not QT6_AVAILABLE:
            return
        
        print("🚀 启动Qt6真实数据演示...")
        
        # 启动数据处理
        if self.processor.start_processing():
            self.timer.start(33)  # 30 FPS
            
            # 显示界面
            self.demo.show()
            
            # 运行应用
            self.app.exec()
        
        # 清理
        self.processor.stop_processing()

def create_web_demo_with_real_data():
    """创建带真实数据的Web演示"""
    try:
        from demo_web import WebDemo
        
        # 创建Web演示实例
        web_demo = WebDemo()
        
        # 创建数据处理器
        processor = RealDataProcessor()
        
        # 重写模拟方法
        async def real_data_simulation():
            """真实数据模拟"""
            if not processor.start_processing():
                return
            
            while web_demo.demo_running:
                if processor.process_frame():
                    await asyncio.sleep(1/30)  # 30 FPS
                else:
                    break
            
            processor.stop_processing()
        
        # 替换原有的模拟方法
        web_demo.run_simulation = real_data_simulation
        
        # 添加回调处理
        def web_callback(result):
            """Web回调处理"""
            if web_demo.demo_running:
                # 转换数据格式
                detection_data = {
                    "type": "detection",
                    "frame": result['frame'],
                    "timestamp": result['timestamp'],
                    "detections": result['detections'],
                    "stats": {
                        "total_objects": len(result['detections']),
                        "total_detections": result['total_detections'],
                        "cars": len([d for d in result['detections'] if d['class'] == 'car']),
                        "persons": len([d for d in result['detections'] if d['class'] == 'person']),
                        "bicycles": len([d for d in result['detections'] if d['class'] == 'bicycle']),
                        "motorcycles": len([d for d in result['detections'] if d['class'] == 'motorcycle']),
                        "fps": result['fps'],
                        "avg_confidence": sum(d['confidence'] for d in result['detections']) / len(result['detections']) if result['detections'] else 0
                    }
                }
                
                # 广播数据
                asyncio.create_task(web_demo.broadcast_message(detection_data))
        
        processor.add_callback(web_callback)
        
        return web_demo
        
    except ImportError as e:
        print(f"❌ Web演示依赖不可用: {e}")
        return None

def main():
    """主函数"""
    print("🎬 真实数据演示启动器")
    print("=" * 50)
    
    print("请选择演示模式:")
    print("1. Qt6界面 + 真实数据")
    print("2. Web界面 + 真实数据")
    print("3. 数据处理测试")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            # Qt6真实数据演示
            demo = RealDataQt6Demo()
            demo.start()
            
        elif choice == "2":
            # Web真实数据演示
            web_demo = create_web_demo_with_real_data()
            if web_demo:
                print("🌐 启动Web真实数据演示...")
                web_demo.run()
            
        elif choice == "3":
            # 数据处理测试
            processor = RealDataProcessor()
            if processor.start_processing():
                print("🧪 运行数据处理测试...")
                for i in range(100):  # 处理100帧
                    if not processor.process_frame():
                        break
                    if i % 10 == 0:
                        print(f"已处理 {i+1} 帧")
                    time.sleep(0.1)
                processor.stop_processing()
                print("✅ 测试完成")
            
        elif choice == "0":
            print("👋 再见!")
            
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"❌ 演示错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
