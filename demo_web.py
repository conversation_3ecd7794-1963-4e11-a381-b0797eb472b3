#!/usr/bin/env python3
"""
无人机交通警察系统 - Web演示界面
提供基于浏览器的可视化演示
"""

import sys
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn

from core.system import SystemManager

class WebDemo:
    """Web演示类"""
    
    def __init__(self):
        self.app = FastAPI(title="无人机交通警察系统演示")
        self.system_manager = None
        self.connected_clients = set()
        self.demo_running = False
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def get_demo_page():
            return self.get_demo_html()
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            self.connected_clients.add(websocket)
            
            try:
                while True:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    await self.handle_websocket_message(websocket, message)
                    
            except WebSocketDisconnect:
                self.connected_clients.remove(websocket)
        
        @self.app.get("/api/status")
        async def get_status():
            return {
                "system_running": self.system_manager is not None,
                "demo_running": self.demo_running,
                "connected_clients": len(self.connected_clients)
            }
    
    async def handle_websocket_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """处理WebSocket消息"""
        action = message.get("action")
        
        if action == "start_system":
            await self.start_system(websocket)
        elif action == "stop_system":
            await self.stop_system(websocket)
        elif action == "start_demo":
            await self.start_demo(websocket)
        elif action == "stop_demo":
            await self.stop_demo(websocket)
    
    async def start_system(self, websocket: WebSocket):
        """启动系统"""
        try:
            await self.send_message(websocket, {
                "type": "log",
                "message": "🚀 正在启动系统..."
            })
            
            self.system_manager = SystemManager()
            success = await self.system_manager.initialize()
            
            if success:
                await self.send_message(websocket, {
                    "type": "log",
                    "message": "✅ 系统启动成功"
                })
                await self.send_message(websocket, {
                    "type": "status",
                    "system_running": True
                })
            else:
                await self.send_message(websocket, {
                    "type": "log",
                    "message": "❌ 系统启动失败"
                })
                
        except Exception as e:
            await self.send_message(websocket, {
                "type": "log",
                "message": f"❌ 启动错误: {e}"
            })
    
    async def stop_system(self, websocket: WebSocket):
        """停止系统"""
        try:
            await self.send_message(websocket, {
                "type": "log",
                "message": "⏹️ 正在停止系统..."
            })
            
            if self.system_manager:
                await self.system_manager.shutdown()
                self.system_manager = None
            
            self.demo_running = False
            
            await self.send_message(websocket, {
                "type": "log",
                "message": "✅ 系统已停止"
            })
            await self.send_message(websocket, {
                "type": "status",
                "system_running": False,
                "demo_running": False
            })
            
        except Exception as e:
            await self.send_message(websocket, {
                "type": "log",
                "message": f"❌ 停止错误: {e}"
            })
    
    async def start_demo(self, websocket: WebSocket):
        """开始演示"""
        if not self.system_manager:
            await self.send_message(websocket, {
                "type": "log",
                "message": "❌ 请先启动系统"
            })
            return
        
        self.demo_running = True
        await self.send_message(websocket, {
            "type": "log",
            "message": "🎭 开始模拟演示"
        })
        
        # 启动模拟演示
        asyncio.create_task(self.run_simulation())
    
    async def stop_demo(self, websocket: WebSocket):
        """停止演示"""
        self.demo_running = False
        await self.send_message(websocket, {
            "type": "log",
            "message": "⏹️ 演示已停止"
        })
    
    async def run_simulation(self):
        """运行模拟演示"""
        frame_count = 0
        total_detections = 0

        # 统计数据
        detection_stats = {'car': 0, 'person': 0, 'bicycle': 0, 'motorcycle': 0}

        while self.demo_running:
            frame_count += 1

            # 生成更真实的检测数据
            detections = self.generate_realistic_detections(frame_count)
            total_detections += len(detections)

            # 更新统计
            for det in detections:
                obj_type = det['class']
                if obj_type in detection_stats:
                    detection_stats[obj_type] += 1

            # 构建检测数据
            detection_data = {
                "type": "detection",
                "frame": frame_count,
                "timestamp": time.strftime("%H:%M:%S"),
                "detections": detections,
                "stats": {
                    "total_objects": len(detections),
                    "total_detections": total_detections,
                    "cars": detection_stats['car'],
                    "persons": detection_stats['person'],
                    "bicycles": detection_stats['bicycle'],
                    "motorcycles": detection_stats['motorcycle'],
                    "fps": 30,
                    "avg_confidence": sum(d['confidence'] for d in detections) / len(detections) if detections else 0
                }
            }

            # 发送给所有连接的客户端
            await self.broadcast_message(detection_data)

            # 每10帧发送一次详细日志
            if frame_count % 10 == 0:
                if detections:
                    log_msg = f"🎯 帧 {frame_count}: 检测到 {len(detections)} 个目标 - "
                    type_counts = {}
                    for det in detections:
                        obj_type = det['class']
                        type_counts[obj_type] = type_counts.get(obj_type, 0) + 1

                    type_strs = []
                    for obj_type, count in type_counts.items():
                        emoji = {'car': '🚗', 'person': '🚶', 'bicycle': '🚲', 'motorcycle': '🏍️'}.get(obj_type, '📦')
                        type_strs.append(f"{emoji}{count}")

                    log_msg += " ".join(type_strs)
                else:
                    log_msg = f"⭕ 帧 {frame_count}: 无目标检测"

                await self.broadcast_message({
                    "type": "log",
                    "message": log_msg
                })

            # 每30帧发送统计报告
            if frame_count % 30 == 0:
                await self.broadcast_message({
                    "type": "log",
                    "message": f"📊 统计报告: 总计{total_detections}个检测，平均每帧{total_detections/frame_count:.1f}个"
                })

            await asyncio.sleep(1/30)  # 30 FPS

    def generate_realistic_detections(self, frame_count):
        """生成更真实的检测数据"""
        import random

        detections = []

        # 根据帧数模拟不同的交通场景
        time_factor = (frame_count % 300) / 300  # 10秒周期

        if time_factor < 0.2:  # 低峰期
            detection_prob = 0.3
            max_objects = 2
        elif time_factor < 0.6:  # 正常期
            detection_prob = 0.7
            max_objects = 4
        else:  # 高峰期
            detection_prob = 0.9
            max_objects = 6

        if random.random() < detection_prob:
            num_objects = random.randint(1, max_objects)

            for i in range(num_objects):
                # 真实的目标类型分布
                obj_types = ['car'] * 6 + ['person'] * 3 + ['bicycle'] * 1 + ['motorcycle'] * 1
                obj_type = random.choice(obj_types)

                # 根据目标类型调整置信度
                if obj_type == 'car':
                    confidence = random.uniform(0.75, 0.95)
                    bbox = [random.randint(50, 400), random.randint(50, 250),
                           random.randint(80, 150), random.randint(60, 100)]
                elif obj_type == 'person':
                    confidence = random.uniform(0.65, 0.90)
                    bbox = [random.randint(100, 500), random.randint(100, 300),
                           random.randint(30, 60), random.randint(80, 120)]
                elif obj_type == 'bicycle':
                    confidence = random.uniform(0.60, 0.85)
                    bbox = [random.randint(80, 450), random.randint(80, 280),
                           random.randint(40, 80), random.randint(50, 90)]
                else:  # motorcycle
                    confidence = random.uniform(0.70, 0.88)
                    bbox = [random.randint(70, 420), random.randint(70, 270),
                           random.randint(60, 100), random.randint(80, 120)]

                detections.append({
                    'class': obj_type,
                    'confidence': confidence,
                    'bbox': bbox,
                    'id': i + 1,
                    'speed': random.uniform(0, 60) if obj_type in ['car', 'motorcycle'] else random.uniform(0, 15),
                    'track_id': random.randint(1000, 9999)
                })

        return detections
    
    async def send_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """发送消息给单个客户端"""
        try:
            await websocket.send_text(json.dumps(message))
        except:
            pass
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """广播消息给所有客户端"""
        if not self.connected_clients:
            return
        
        message_text = json.dumps(message)
        disconnected = set()
        
        for client in self.connected_clients:
            try:
                await client.send_text(message_text)
            except:
                disconnected.add(client)
        
        # 移除断开连接的客户端
        self.connected_clients -= disconnected
    
    def get_demo_html(self) -> str:
        """获取演示页面HTML"""
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚁 无人机交通警察系统演示</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; min-height: 100vh;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .main-content { display: grid; grid-template-columns: 300px 1fr 350px; gap: 20px; }
        .panel { background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; backdrop-filter: blur(10px); }
        .control-panel h3, .info-panel h3 { margin-bottom: 15px; color: #fff; }
        .btn { 
            background: linear-gradient(45deg, #4CAF50, #45a049); 
            color: white; border: none; padding: 12px 24px; 
            border-radius: 8px; cursor: pointer; font-size: 14px; 
            margin: 5px 0; width: 100%; transition: all 0.3s;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn:disabled { background: #666; cursor: not-allowed; transform: none; }
        .btn.stop { background: linear-gradient(45deg, #f44336, #d32f2f); }
        .btn.demo { background: linear-gradient(45deg, #2196F3, #1976D2); }
        .video-area { 
            background: #000; border-radius: 10px; 
            display: flex; align-items: center; justify-content: center;
            min-height: 400px; position: relative; overflow: hidden;
        }
        .detection-overlay { 
            position: absolute; top: 0; left: 0; 
            width: 100%; height: 100%; pointer-events: none;
        }
        .detection-box { 
            position: absolute; border: 2px solid #00ff00; 
            background: rgba(0,255,0,0.1);
        }
        .detection-label { 
            background: #00ff00; color: #000; 
            padding: 2px 6px; font-size: 12px; font-weight: bold;
        }
        .log-area, .results-area { 
            background: rgba(0,0,0,0.3); border-radius: 8px; 
            padding: 15px; height: 200px; overflow-y: auto; 
            font-family: 'Courier New', monospace; font-size: 12px;
        }
        .stats { 
            background: rgba(0,0,0,0.2); padding: 10px; 
            border-radius: 8px; margin-top: 10px;
        }
        .status-indicator { 
            display: inline-block; width: 12px; height: 12px; 
            border-radius: 50%; margin-right: 8px;
        }
        .status-running { background: #4CAF50; }
        .status-stopped { background: #f44336; }
        .demo-placeholder {
            font-size: 18px; color: #ccc; text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚁 无人机交通警察系统</h1>
            <p>AI驱动的智能交通监控演示平台</p>
        </div>
        
        <div class="main-content">
            <!-- 控制面板 -->
            <div class="panel control-panel">
                <h3>🎮 系统控制</h3>
                <button id="startSystem" class="btn">🚀 启动系统</button>
                <button id="stopSystem" class="btn stop" disabled>⏹️ 停止系统</button>
                
                <h3 style="margin-top: 30px;">🎭 演示控制</h3>
                <button id="startDemo" class="btn demo" disabled>▶️ 开始演示</button>
                <button id="stopDemo" class="btn stop" disabled>⏸️ 停止演示</button>
                
                <h3 style="margin-top: 30px;">📊 系统状态</h3>
                <div class="stats">
                    <div><span id="systemStatus" class="status-indicator status-stopped"></span>系统状态: <span id="systemStatusText">已停止</span></div>
                    <div style="margin-top: 5px;"><span id="demoStatus" class="status-indicator status-stopped"></span>演示状态: <span id="demoStatusText">未运行</span></div>
                </div>
            </div>
            
            <!-- 视频显示区域 -->
            <div class="panel">
                <h3>📹 实时检测画面</h3>
                <div class="video-area" id="videoArea">
                    <div class="demo-placeholder">等待演示开始...</div>
                    <div class="detection-overlay" id="detectionOverlay"></div>
                </div>
                <div class="stats">
                    <div id="detectionStats">检测统计: 等待数据...</div>
                </div>
            </div>
            
            <!-- 信息面板 -->
            <div class="panel info-panel">
                <h3>📋 检测结果</h3>
                <div class="results-area" id="resultsArea">等待检测结果...</div>
                
                <h3 style="margin-top: 20px;">📝 系统日志</h3>
                <div class="log-area" id="logArea">系统就绪，等待启动...</div>
            </div>
        </div>
    </div>

    <script>
        class DemoApp {
            constructor() {
                this.ws = null;
                this.connectWebSocket();
                this.setupEventListeners();
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                this.ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
                
                this.ws.onopen = () => {
                    this.log('🔗 连接已建立');
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };
                
                this.ws.onclose = () => {
                    this.log('❌ 连接已断开');
                    setTimeout(() => this.connectWebSocket(), 3000);
                };
            }
            
            setupEventListeners() {
                document.getElementById('startSystem').onclick = () => {
                    this.sendMessage({action: 'start_system'});
                };
                
                document.getElementById('stopSystem').onclick = () => {
                    this.sendMessage({action: 'stop_system'});
                };
                
                document.getElementById('startDemo').onclick = () => {
                    this.sendMessage({action: 'start_demo'});
                };
                
                document.getElementById('stopDemo').onclick = () => {
                    this.sendMessage({action: 'stop_demo'});
                };
            }
            
            sendMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(message));
                }
            }
            
            handleMessage(data) {
                switch(data.type) {
                    case 'log':
                        this.log(data.message);
                        break;
                    case 'status':
                        this.updateStatus(data);
                        break;
                    case 'detection':
                        this.updateDetection(data);
                        break;
                }
            }
            
            log(message) {
                const logArea = document.getElementById('logArea');
                const timestamp = new Date().toLocaleTimeString();
                logArea.innerHTML += `[${timestamp}] ${message}\\n`;
                logArea.scrollTop = logArea.scrollHeight;
            }
            
            updateStatus(status) {
                const systemStatus = document.getElementById('systemStatus');
                const systemStatusText = document.getElementById('systemStatusText');
                const demoStatus = document.getElementById('demoStatus');
                const demoStatusText = document.getElementById('demoStatusText');
                
                if (status.system_running !== undefined) {
                    systemStatus.className = `status-indicator ${status.system_running ? 'status-running' : 'status-stopped'}`;
                    systemStatusText.textContent = status.system_running ? '运行中' : '已停止';
                    
                    document.getElementById('startSystem').disabled = status.system_running;
                    document.getElementById('stopSystem').disabled = !status.system_running;
                    document.getElementById('startDemo').disabled = !status.system_running;
                }
                
                if (status.demo_running !== undefined) {
                    demoStatus.className = `status-indicator ${status.demo_running ? 'status-running' : 'status-stopped'}`;
                    demoStatusText.textContent = status.demo_running ? '运行中' : '未运行';
                    
                    document.getElementById('stopDemo').disabled = !status.demo_running;
                }
            }
            
            updateDetection(data) {
                // 更新检测统计
                const stats = data.stats;
                const statsText = `帧数: ${data.frame} | 总检测: ${stats.total_detections} | 当前: ${stats.total_objects}个 | 🚗${stats.cars} 🚶${stats.persons} 🚲${stats.bicycles} 🏍️${stats.motorcycles} | FPS: ${stats.fps}`;
                document.getElementById('detectionStats').innerHTML = statsText;

                // 更新检测结果
                const resultsArea = document.getElementById('resultsArea');
                if (data.detections.length > 0) {
                    let resultText = `🎯 [${data.timestamp}] 帧 ${data.frame}: ${data.detections.length}个目标\\n`;

                    // 按类型分组显示
                    const byType = {};
                    data.detections.forEach(det => {
                        if (!byType[det.class]) byType[det.class] = [];
                        byType[det.class].push(det);
                    });

                    for (const [type, objects] of Object.entries(byType)) {
                        const emoji = {'car': '🚗', 'person': '🚶', 'bicycle': '🚲', 'motorcycle': '🏍️'}[type] || '📦';
                        resultText += `  ${emoji} ${type}: ${objects.length}个\\n`;

                        objects.slice(0, 2).forEach((det, i) => {
                            resultText += `    #${det.id}: ${det.confidence.toFixed(2)}`;
                            if (det.speed) resultText += ` (${det.speed.toFixed(1)}km/h)`;
                            resultText += `\\n`;
                        });

                        if (objects.length > 2) {
                            resultText += `    ... 还有${objects.length - 2}个\\n`;
                        }
                    }

                    // 添加分析
                    const totalVehicles = (stats.cars || 0) + (stats.motorcycles || 0);
                    if (totalVehicles > 5) resultText += `  ⚠️ 车流密度较高\\n`;
                    if (stats.persons > 3) resultText += `  👥 行人较多\\n`;

                    resultText += `─────────────────\\n`;
                    resultsArea.innerHTML += resultText;
                } else {
                    resultsArea.innerHTML += `⭕ [${data.timestamp}] 帧 ${data.frame}: 无目标检测\\n`;
                }

                resultsArea.scrollTop = resultsArea.scrollHeight;

                // 更新视觉效果
                this.updateVideoDisplay(data);
            }
            
            updateVideoDisplay(data) {
                const videoArea = document.getElementById('videoArea');

                if (data.detections.length > 0) {
                    // 创建模拟的检测画面
                    let displayHTML = `
                        <div style="position: relative; width: 100%; height: 100%; background: linear-gradient(45deg, #1a1a2e, #16213e); display: flex; align-items: center; justify-content: center;">
                            <div style="color: #00ff00; font-size: 18px; text-align: center;">
                                🎥 AI检测进行中<br>
                                <span style="font-size: 14px; color: #ccc;">帧 ${data.frame} | ${data.detections.length}个目标</span>
                            </div>
                    `;

                    // 添加检测框模拟
                    data.detections.forEach((det, index) => {
                        const colors = {'car': '#00ff00', 'person': '#ff6b6b', 'bicycle': '#4ecdc4', 'motorcycle': '#ffe66d'};
                        const color = colors[det.class] || '#ffffff';
                        const left = 20 + (index % 3) * 30;
                        const top = 20 + Math.floor(index / 3) * 25;

                        displayHTML += `
                            <div style="position: absolute; left: ${left}%; top: ${top}%;
                                       border: 2px solid ${color}; background: ${color}20;
                                       padding: 4px 8px; border-radius: 4px; font-size: 12px; color: ${color};">
                                ${det.class} ${det.confidence.toFixed(2)}
                            </div>
                        `;
                    });

                    displayHTML += '</div>';
                    videoArea.innerHTML = displayHTML;
                } else {
                    videoArea.innerHTML = `
                        <div style="color: #666; font-size: 16px; text-align: center;">
                            📹 监控画面<br>
                            <span style="font-size: 14px;">帧 ${data.frame} | 无目标检测</span>
                        </div>
                    `;
                }

                // 添加闪烁效果表示活动
                videoArea.style.border = '2px solid #00ff00';
                setTimeout(() => {
                    videoArea.style.border = '2px solid transparent';
                }, 200);
            }
        }
        
        // 启动应用
        window.onload = () => {
            new DemoApp();
        };
    </script>
</body>
</html>
        '''
    
    def run(self, host="0.0.0.0", port=8080):
        """运行Web演示"""
        print(f"🌐 启动Web演示界面: http://{host}:{port}")
        print("🎯 在浏览器中打开上述地址查看演示")
        uvicorn.run(self.app, host=host, port=port)

def main():
    """主函数"""
    print("🚁 无人机交通警察系统 - Web演示界面")
    print("=" * 50)
    
    try:
        demo = WebDemo()
        demo.run()
    except KeyboardInterrupt:
        print("\n👋 Web演示已停止")
    except Exception as e:
        print(f"❌ Web演示运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
