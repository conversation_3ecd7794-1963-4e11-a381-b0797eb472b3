version: '3.8'

services:
  # 主应用服务 - 生产环境配置
  traffic-police:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: traffic-police-app-prod
    restart: always
    ports:
      - "8000:8000"
      - "8080:8080"
    volumes:
      - /opt/traffic-police/data:/app/data
      - /opt/traffic-police/logs:/app/logs
      - /opt/traffic-police/models:/app/models
      - /opt/traffic-police/config:/app/config
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://traffic_admin:${DB_PASSWORD}@postgres:5432/traffic_police
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - A<PERSON>URE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - DJI_APP_KEY=${DJI_APP_KEY}
      - DJI_APP_SECRET=${DJI_APP_SECRET}
      - CUDA_VISIBLE_DEVICES=0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - traffic-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL 数据库 - 生产配置
  postgres:
    image: postgres:14-alpine
    container_name: traffic-police-db-prod
    restart: always
    environment:
      POSTGRES_DB: traffic_police
      POSTGRES_USER: traffic_admin
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    volumes:
      - /opt/traffic-police/postgres:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "127.0.0.1:5432:5432"  # 只绑定本地
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U traffic_admin -d traffic_police"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存 - 生产配置
  redis:
    image: redis:7-alpine
    container_name: traffic-police-redis-prod
    restart: always
    command: redis-server /etc/redis/redis.conf
    volumes:
      - /opt/traffic-police/redis:/data
      - ./redis/redis.conf:/etc/redis/redis.conf
    ports:
      - "127.0.0.1:6379:6379"  # 只绑定本地
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO 对象存储 - 生产配置
  minio:
    image: minio/minio:latest
    container_name: traffic-police-minio-prod
    restart: always
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    volumes:
      - /opt/traffic-police/minio:/data
    ports:
      - "127.0.0.1:9000:9000"
      - "127.0.0.1:9001:9001"
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 负载均衡器
  nginx:
    image: nginx:alpine
    container_name: traffic-police-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - traffic-police
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: traffic-police-prometheus-prod
    restart: always
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml
      - /opt/traffic-police/prometheus:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: traffic-police-grafana-prod
    restart: always
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_ADMIN_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SECURITY_ALLOW_EMBEDDING: true
      GF_AUTH_ANONYMOUS_ENABLED: false
    volumes:
      - /opt/traffic-police/grafana:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "127.0.0.1:3000:3000"
    depends_on:
      - prometheus
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # 日志收集
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: traffic-police-fluentd-prod
    restart: always
    volumes:
      - ./logging/fluentd.conf:/fluentd/etc/fluent.conf
      - /opt/traffic-police/logs:/app/logs:ro
    ports:
      - "127.0.0.1:24224:24224"
    networks:
      - traffic-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # 备份服务
  backup:
    image: postgres:14-alpine
    container_name: traffic-police-backup-prod
    restart: "no"
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - /opt/traffic-police/backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"
    depends_on:
      - postgres
    networks:
      - traffic-network
    profiles:
      - backup

# 网络配置
networks:
  traffic-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 生产环境不使用命名卷，直接挂载主机目录
# 确保 /opt/traffic-police 目录存在且权限正确
