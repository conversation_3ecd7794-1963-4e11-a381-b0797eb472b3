#!/usr/bin/env python3
"""
Qt6安装脚本
自动安装PyQt6和相关依赖
"""

import subprocess
import sys
import os

def print_status(message, status="INFO"):
    """打印状态信息"""
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "END": "\033[0m"
    }
    print(f"{colors.get(status, '')}{message}{colors['END']}")

def check_qt6():
    """检查Qt6是否已安装"""
    try:
        import PyQt6
        from PyQt6.QtWidgets import QApplication
        print_status("✅ PyQt6已安装", "SUCCESS")
        return True
    except ImportError:
        print_status("❌ PyQt6未安装", "WARNING")
        return False

def install_qt6():
    """安装PyQt6"""
    print_status("🚀 开始安装PyQt6...", "INFO")
    
    packages = [
        "PyQt6>=6.5.0",
        "PyQt6-tools>=6.5.0"
    ]
    
    for package in packages:
        try:
            print_status(f"正在安装 {package}...", "INFO")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ])
            print_status(f"✅ {package} 安装成功", "SUCCESS")
        except subprocess.CalledProcessError as e:
            print_status(f"❌ {package} 安装失败: {e}", "ERROR")
            return False
    
    return True

def test_qt6():
    """测试Qt6安装"""
    print_status("🧪 测试Qt6安装...", "INFO")
    
    try:
        from PyQt6.QtWidgets import QApplication, QLabel
        from PyQt6.QtCore import Qt
        
        # 创建测试应用
        app = QApplication([])
        
        # 创建测试窗口
        label = QLabel("Qt6安装测试成功！")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                border-radius: 10px;
            }
        """)
        label.resize(300, 100)
        label.show()
        
        print_status("✅ Qt6测试成功！测试窗口已显示", "SUCCESS")
        print_status("💡 请关闭测试窗口继续...", "INFO")
        
        # 运行测试应用
        app.exec()
        
        return True
        
    except Exception as e:
        print_status(f"❌ Qt6测试失败: {e}", "ERROR")
        return False

def create_qt6_demo_shortcut():
    """创建Qt6演示快捷方式"""
    print_status("🔗 创建Qt6演示快捷方式...", "INFO")
    
    # Linux/macOS快捷方式
    if os.name != 'nt':
        shortcut_content = '''#!/bin/bash
# Qt6演示启动脚本

echo "🚁 启动Qt6现代化演示界面..."
echo "请确保已激活conda环境: conda activate yolov5"
echo ""

# 检查环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未找到，请检查环境"
    exit 1
fi

# 检查Qt6
python -c "import PyQt6" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ PyQt6未安装，请运行: python install_qt6.py"
    exit 1
fi

# 启动演示
python demo_qt6.py
'''
        
        with open("start_qt6_demo.sh", "w") as f:
            f.write(shortcut_content)
        
        os.chmod("start_qt6_demo.sh", 0o755)
        print_status("✅ 创建了 start_qt6_demo.sh", "SUCCESS")
    
    # Windows快捷方式
    else:
        shortcut_content = '''@echo off
REM Qt6演示启动脚本

echo 🚁 启动Qt6现代化演示界面...
echo 请确保已激活conda环境: conda activate yolov5
echo.

REM 检查Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Python未找到，请检查环境
    pause
    exit /b 1
)

REM 检查Qt6
python -c "import PyQt6" >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ PyQt6未安装，请运行: python install_qt6.py
    pause
    exit /b 1
)

REM 启动演示
python demo_qt6.py
pause
'''
        
        with open("start_qt6_demo.bat", "w") as f:
            f.write(shortcut_content)
        
        print_status("✅ 创建了 start_qt6_demo.bat", "SUCCESS")

def show_usage_guide():
    """显示使用指南"""
    print_status("\n🎯 Qt6演示界面使用指南", "INFO")
    print("""
📱 启动方式:
  1. python demo_qt6.py
  2. python start.py (选择图形界面演示)
  3. ./start_qt6_demo.sh (Linux/macOS)
  4. start_qt6_demo.bat (Windows)

🎨 界面特色:
  ✨ 现代化渐变背景
  🎯 实时状态卡片
  📊 多标签页信息面板
  🎮 直观的控制按钮
  📈 实时统计图表

🎭 演示流程:
  1. 点击"🚀 初始化系统"
  2. 等待系统加载完成
  3. 点击"🎭 开始演示"
  4. 观察实时检测效果
  5. 查看各个信息标签页

💡 竞赛建议:
  - 现代化界面给评委留下深刻印象
  - 实时状态展示系统专业性
  - 多维度信息展示系统完整性
    """)

def main():
    """主函数"""
    print("🎨 Qt6现代化演示界面安装器")
    print("=" * 50)
    
    # 检查当前安装状态
    if check_qt6():
        print_status("Qt6已安装，可以直接使用演示界面", "SUCCESS")
        
        choice = input("\n是否要测试Qt6安装? (y/n): ").lower().strip()
        if choice == 'y':
            test_qt6()
        
        choice = input("\n是否要创建快捷启动方式? (y/n): ").lower().strip()
        if choice == 'y':
            create_qt6_demo_shortcut()
        
        show_usage_guide()
        return
    
    # 安装Qt6
    print_status("需要安装PyQt6以使用现代化演示界面", "INFO")
    choice = input("\n是否现在安装? (y/n): ").lower().strip()
    
    if choice != 'y':
        print_status("安装已取消", "WARNING")
        return
    
    # 执行安装
    success = install_qt6()
    
    if success:
        print_status("\n🎉 PyQt6安装成功！", "SUCCESS")
        
        # 测试安装
        choice = input("\n是否要测试安装? (y/n): ").lower().strip()
        if choice == 'y':
            test_success = test_qt6()
            
            if test_success:
                print_status("✅ Qt6安装和测试完成", "SUCCESS")
            else:
                print_status("⚠️ Qt6安装成功但测试失败", "WARNING")
        
        # 创建快捷方式
        create_qt6_demo_shortcut()
        
        # 显示使用指南
        show_usage_guide()
        
        print_status("\n🚀 现在可以启动Qt6演示界面了:", "SUCCESS")
        print_status("python demo_qt6.py", "INFO")
        
    else:
        print_status("\n❌ PyQt6安装失败", "ERROR")
        print_status("请检查网络连接和Python环境", "WARNING")
        print_status("或手动安装: pip install PyQt6", "INFO")

if __name__ == "__main__":
    main()
