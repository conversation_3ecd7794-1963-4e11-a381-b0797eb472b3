"""
优化版交通监控系统
包含批处理、内存优化、帧跳过等性能优化功能
"""
import cv2
import time
import sys
import gc
from typing import Optional, List
import logging

from config import Config, default_config
from detector import TrafficDetector
from data_manager import DataManager
from visualization import Visualizer
from accident_detector import AccidentDetector
from traffic_utils import setup_logging, FrameBuffer, PerformanceMonitor, get_memory_usage, optimize_frame_for_processing


class OptimizedTrafficMonitor:
    """优化版交通监控类，包含性能优化功能"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化优化版交通监控系统
        
        Args:
            config: 配置对象
        """
        self.config = config or default_config
        self.logger = setup_logging(self.config.system.log_level)
        
        # 性能优化相关
        self.frame_buffer = FrameBuffer(buffer_size=4)
        self.performance_monitor = PerformanceMonitor()
        self.enable_batch_processing = True
        self.enable_frame_optimization = True
        self.memory_cleanup_interval = 100  # 每100帧清理一次内存
        
        # 初始化模块
        self.detector = None
        self.data_manager = None
        self.visualizer = None
        self.accident_detector = None
        self.video_capture = None
        
        # 运行状态
        self.is_running = False
        self.frame_count = 0
        self.start_time = None
        self.last_memory_cleanup = 0
        
        self._initialize_modules()
    
    def _initialize_modules(self):
        """初始化所有模块"""
        try:
            self.logger.info("Initializing optimized traffic monitoring system...")
            
            # 初始化检测器
            self.detector = TrafficDetector(self.config)
            
            # 初始化数据管理器
            self.data_manager = DataManager(self.config)
            
            # 初始化可视化器
            self.visualizer = Visualizer(self.config)
            
            # 初始化事故检测器
            self.accident_detector = AccidentDetector(self.config)
            
            self.logger.info("All modules initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize modules: {e}")
            raise
    
    def _setup_video_capture(self) -> bool:
        """设置视频捕获"""
        try:
            self.video_capture = cv2.VideoCapture(self.config.video.video_path)
            
            if not self.video_capture.isOpened():
                self.logger.error(f"Failed to open video: {self.config.video.video_path}")
                return False
            
            # 获取视频信息
            fps = self.video_capture.get(cv2.CAP_PROP_FPS)
            frame_count = int(self.video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(self.video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            self.logger.info(f"Video info - FPS: {fps}, Frames: {frame_count}, "
                           f"Resolution: {width}x{height}")
            
            # 设置显示窗口
            self.visualizer.setup_windows(width, height)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup video capture: {e}")
            return False
    
    def run(self) -> bool:
        """运行优化版交通监控系统"""
        if not self._setup_video_capture():
            return False
        
        self.is_running = True
        self.start_time = time.time()
        self.frame_count = 0
        
        self.logger.info("Starting optimized traffic monitoring...")
        
        try:
            while self.is_running:
                success = self._process_frame_optimized()
                if not success:
                    break
                
                # 内存清理
                if self.frame_count - self.last_memory_cleanup >= self.memory_cleanup_interval:
                    self._cleanup_memory()
                    self.last_memory_cleanup = self.frame_count
                
                # 检查退出条件
                if self._should_exit():
                    break
            
            # 处理剩余的缓冲帧
            if not self.frame_buffer.is_empty():
                self._process_buffered_frames()
            
            self.logger.info("Optimized traffic monitoring stopped")
            self._print_performance_summary()
            return True
            
        except KeyboardInterrupt:
            self.logger.info("Interrupted by user")
            return True
        except Exception as e:
            self.logger.error(f"Error during processing: {e}")
            return False
        finally:
            self._cleanup()
    
    def _process_frame_optimized(self) -> bool:
        """优化的帧处理"""
        frame_start_time = time.time()
        
        # 读取帧
        ret, frame = self.video_capture.read()
        if not ret:
            self.logger.info("End of video or failed to read frame")
            return False
        
        # 跳帧处理
        if self.frame_count % self.config.video.frame_skip != 0:
            self.frame_count += 1
            return True
        
        # 帧优化
        if self.enable_frame_optimization:
            frame = optimize_frame_for_processing(frame, quality=0.9)
        
        # 批处理模式
        if self.enable_batch_processing:
            buffer_full = self.frame_buffer.add_frame(frame, self.frame_count)
            if buffer_full:
                self._process_buffered_frames()
        else:
            # 单帧处理
            self._process_single_frame(frame, self.frame_count, frame_start_time)
        
        self.frame_count += 1
        return True
    
    def _process_buffered_frames(self):
        """处理缓冲的帧（批处理）"""
        if self.frame_buffer.is_empty():
            return
        
        batch_start_time = time.time()
        frames, frame_ids = self.frame_buffer.get_batch()
        
        try:
            # 批量检测
            batch_results = self.detector.batch_detect(frames)
            
            # 处理每个帧的结果
            for i, (vehicles, persons) in enumerate(batch_results):
                frame_id = frame_ids[i]
                frame = frames[i]
                
                # 事故检测
                accident_info = self.accident_detector.update(vehicles, persons, frame_id)
                
                # 数据管理
                processing_time = (time.time() - batch_start_time) / len(frames)
                frame_data = self.data_manager.add_frame_data(
                    frame_id, vehicles, persons, processing_time
                )
                
                # 只可视化最后一帧（减少显示开销）
                if i == len(batch_results) - 1:
                    statistics = self.data_manager.get_current_statistics()
                    self._visualize_results(frame, frame_data, vehicles, persons, 
                                          accident_info, statistics)
                
                # 性能监控
                memory_usage = get_memory_usage()
                self.performance_monitor.record_frame(processing_time, memory_usage)
                
                # 日志输出
                if frame_id % 60 == 0:  # 每60帧输出一次
                    self._log_progress(frame_data, statistics, accident_info)
        
        except Exception as e:
            self.logger.error(f"Error in batch processing: {e}")
    
    def _process_single_frame(self, frame, frame_id, frame_start_time):
        """处理单帧"""
        try:
            # 目标检测
            vehicles, persons = self.detector.detect(frame)
            
            # 事故检测
            accident_info = self.accident_detector.update(vehicles, persons, frame_id)
            
            # 数据管理
            processing_time = time.time() - frame_start_time
            frame_data = self.data_manager.add_frame_data(
                frame_id, vehicles, persons, processing_time
            )
            
            # 获取统计信息
            statistics = self.data_manager.get_current_statistics()
            
            # 可视化
            self._visualize_results(frame, frame_data, vehicles, persons, 
                                  accident_info, statistics)
            
            # 性能监控
            memory_usage = get_memory_usage()
            self.performance_monitor.record_frame(processing_time, memory_usage)
            
            # 日志输出
            if frame_id % 30 == 0:
                self._log_progress(frame_data, statistics, accident_info)
        
        except Exception as e:
            self.logger.error(f"Error processing frame {frame_id}: {e}")
    
    def _visualize_results(self, frame, frame_data, vehicles, persons, accident_info, statistics):
        """可视化结果"""
        # 绘制检测结果
        detection_frame = self.visualizer.draw_detections(
            frame, vehicles, persons, 
            accident_info.get('accident_bboxes', [])
        )
        
        # 绘制统计信息
        detection_frame = self.visualizer.draw_statistics(
            detection_frame, frame_data, statistics, accident_info
        )
        
        # 绘制密度图
        vehicle_densities, person_densities = self.data_manager.get_density_history()
        plot_frame = self.visualizer.draw_density_plot(vehicle_densities, person_densities)
        
        # 显示
        self.visualizer.show_frames(detection_frame, plot_frame)
    
    def _cleanup_memory(self):
        """清理内存"""
        gc.collect()  # 强制垃圾回收
        memory_usage = get_memory_usage()
        self.logger.debug(f"Memory cleanup at frame {self.frame_count}, "
                         f"current usage: {memory_usage:.1f} MB")
    
    def _log_progress(self, frame_data, statistics, accident_info):
        """记录进度日志"""
        runtime = time.time() - self.start_time
        fps = statistics.get('processing_fps', 0)
        memory_usage = get_memory_usage()
        
        log_msg = (f"Frame {frame_data.frame_id}: "
                  f"V={frame_data.vehicle_count}, P={frame_data.person_count}, "
                  f"FPS={fps:.1f}, Memory={memory_usage:.1f}MB, Runtime={runtime:.1f}s")
        
        if accident_info.get('detected', False):
            log_msg += f", ACCIDENT: {accident_info.get('type', 'Unknown')}"
        
        self.logger.info(log_msg)
    
    def _should_exit(self) -> bool:
        """检查是否应该退出"""
        key = self.visualizer.wait_key(1)
        if key == ord('q'):
            self.logger.info("Exit requested by user")
            return True
        
        if (self.config.video.max_frames > 0 and 
            self.frame_count >= self.config.video.max_frames):
            self.logger.info("Reached maximum frame limit")
            return True
        
        return False
    
    def _print_performance_summary(self):
        """打印性能摘要"""
        stats = self.performance_monitor.get_stats()
        if stats:
            self.logger.info("=== Performance Summary ===")
            self.logger.info(f"Average FPS: {stats.get('avg_fps', 0):.2f}")
            self.logger.info(f"Average processing time: {stats.get('avg_processing_time_ms', 0):.2f} ms")
            self.logger.info(f"Max processing time: {stats.get('max_processing_time_ms', 0):.2f} ms")
            self.logger.info(f"Frames processed: {stats.get('frames_processed', 0)}")
            if 'avg_memory_mb' in stats:
                self.logger.info(f"Average memory usage: {stats.get('avg_memory_mb', 0):.1f} MB")
                self.logger.info(f"Max memory usage: {stats.get('max_memory_mb', 0):.1f} MB")
    
    def _cleanup(self):
        """清理资源"""
        self.is_running = False
        
        if self.video_capture:
            self.video_capture.release()
        
        if self.visualizer:
            self.visualizer.cleanup()
        
        self.logger.info("Resources cleaned up")


def main():
    """主函数"""
    # 创建优化配置
    config = default_config
    config.video.frame_skip = 2  # 跳帧处理，提高性能
    config.system.max_memory_frames = 50  # 减少内存使用
    
    # 创建并运行优化版交通监控系统
    monitor = OptimizedTrafficMonitor(config)
    success = monitor.run()
    
    if success:
        print("Optimized traffic monitoring completed successfully")
    else:
        print("Optimized traffic monitoring failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
