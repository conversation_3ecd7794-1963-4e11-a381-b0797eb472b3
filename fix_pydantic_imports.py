#!/usr/bin/env python3
"""
批量修复Pydantic导入问题
"""
import os
import re
from pathlib import Path


def fix_pydantic_imports(file_path):
    """修复单个文件的Pydantic导入"""
    print(f"修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修复
    if 'from pydantic import BaseSettings' not in content:
        print(f"  跳过 {file_path} (无需修复)")
        return
    
    # 替换导入语句
    old_import = 'from pydantic import BaseSettings, Field'
    new_import = '''try:
    # Pydantic v2
    from pydantic import BaseModel, Field
    from pydantic_settings import BaseSettings
except ImportError:
    # Pydantic v1
    from pydantic import BaseSettings, Field'''
    
    if old_import in content:
        content = content.replace(old_import, new_import)
        print(f"  ✅ 替换了导入语句")
    
    # 替换Config类
    config_pattern = r'class Config:\s*\n\s*env_file = "\.env"\s*\n\s*env_file_encoding = "utf-8"\s*\n\s*case_sensitive = True'
    new_config = '''# Pydantic v2 配置
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"
    }'''
    
    if re.search(config_pattern, content):
        content = re.sub(config_pattern, new_config, content)
        print(f"  ✅ 替换了Config类")
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"  ✅ 修复完成: {file_path}")


def main():
    """主函数"""
    print("🔧 开始批量修复Pydantic导入问题...")
    
    # 需要修复的配置文件
    config_files = [
        'config/ai_config.py',
        'config/api_config.py', 
        'config/database_config.py'
    ]
    
    for file_path in config_files:
        if os.path.exists(file_path):
            try:
                fix_pydantic_imports(file_path)
            except Exception as e:
                print(f"  ❌ 修复失败 {file_path}: {e}")
        else:
            print(f"  ⚠️  文件不存在: {file_path}")
    
    print("\n🎉 批量修复完成!")


if __name__ == "__main__":
    main()
