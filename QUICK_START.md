# 🚀 快速开始指南

## 📦 一键部署

### Linux/macOS 用户

```bash
# 1. 下载项目
git clone <your-repository-url>
cd air_traffic_police

# 2. 运行自动部署脚本
chmod +x deploy.sh
./deploy.sh

# 3. 启动系统
./start_system.sh
```

### Windows 用户

```cmd
REM 1. 下载项目
git clone <your-repository-url>
cd air_traffic_police

REM 2. 运行自动部署脚本
deploy.bat

REM 3. 启动系统
start_system.bat
```

## ⚡ 手动快速部署

### 步骤1: 环境准备

```bash
# 创建conda环境
conda create -n yolov5 python=3.9 -y
conda activate yolov5

# 安装PyTorch (GPU版本)
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 或安装CPU版本
# conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

### 步骤2: 安装依赖

```bash
# 安装核心依赖
pip install ultralytics>=8.0.0 opencv-python>=4.8.0 fastapi>=0.104.0
pip install uvicorn>=0.24.0 websockets>=12.0 pydantic>=2.5.0
pip install pydantic-settings>=2.1.0 sqlalchemy>=2.0.0 aiosqlite>=0.19.0
```

### 步骤3: 下载模型

```bash
# 创建模型目录并下载
mkdir -p models && cd models
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt
cd ..
```

### 步骤4: 测试系统

```bash
# 运行系统测试
python test_final.py
```

### 步骤5: 启动系统

```bash
# 演示模式
python main.py --mode demo

# API服务模式
python main.py --mode api

# 完整模式
python main.py --mode full
```

## 🎯 验证安装

### 检查GPU支持

```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA devices: {torch.cuda.device_count()}")
```

### 检查模型加载

```python
from ultralytics import YOLO
model = YOLO('models/yolov8s.pt')
print("✅ YOLO模型加载成功")
```

### 测试API接口

```bash
# 启动API服务
python main.py --mode api &

# 测试健康检查
curl http://localhost:8000/health

# 查看API文档
# 浏览器访问: http://localhost:8000/docs
```

## 🔧 常见问题

### Q: CUDA out of memory
**A**: 降低批处理大小或使用更小的模型
```python
# config/ai_config.py
BATCH_SIZE = 1
MODEL_TYPE = ModelType.YOLOV8S
```

### Q: 模块导入错误
**A**: 检查虚拟环境是否正确激活
```bash
conda activate yolov5
which python  # 应显示conda环境路径
```

### Q: 端口被占用
**A**: 更改API端口
```python
# config/api_config.py
PORT = 8001  # 或其他可用端口
```

## 📊 性能配置

### 高性能配置 (GPU)
```python
# config/ai_config.py
MODEL_TYPE = ModelType.YOLOV8M
DEVICE = DeviceType.CUDA
HALF_PRECISION = True
BATCH_SIZE = 4
INPUT_SIZE = (640, 640)
```

### 低资源配置 (CPU)
```python
# config/ai_config.py
MODEL_TYPE = ModelType.YOLOV8S
DEVICE = DeviceType.CPU
BATCH_SIZE = 1
INPUT_SIZE = (416, 416)
CONFIDENCE_THRESHOLD = 0.5
```

## 🌐 Web界面访问

启动API服务后，可通过以下地址访问：

- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **系统状态**: http://localhost:8000/api/v1/system/status
- **实时监控**: http://localhost:8000/monitor (如果启用)

## 📱 移动端支持

系统API支持移动端访问，可以开发配套的移动应用：

```javascript
// 示例: 获取系统状态
fetch('http://your-server:8000/api/v1/system/status')
  .then(response => response.json())
  .then(data => console.log(data));
```

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t air-traffic-police .

# 运行容器 (GPU)
docker run -d --gpus all -p 8000:8000 air-traffic-police

# 运行容器 (CPU)
docker run -d -p 8000:8000 air-traffic-police
```

## 📈 监控和日志

### 查看系统日志
```bash
# 实时日志
tail -f logs/system.log

# 错误日志
grep ERROR logs/system.log

# 性能日志
grep "Processing time" logs/system.log
```

### 系统监控
```bash
# 资源使用情况
htop

# GPU使用情况 (如果有GPU)
nvidia-smi

# 磁盘使用情况
df -h
```

## 🎓 竞赛展示建议

### 演示流程
1. **系统介绍**: 展示系统架构和核心功能
2. **实时检测**: 运行演示模式，展示目标检测效果
3. **数据分析**: 展示交通流分析和统计报告
4. **API接口**: 演示RESTful API和实时数据推送
5. **性能指标**: 展示检测精度、处理速度等关键指标

### 准备材料
- 测试视频文件 (交通场景)
- 系统架构图
- 性能测试报告
- API文档
- 用户手册

### 技术亮点
- **AI算法**: YOLOv8 + 多目标跟踪
- **实时性**: 毫秒级检测响应
- **准确性**: 高精度目标识别
- **扩展性**: 模块化架构设计
- **易用性**: 一键部署和启动

## 📞 获取帮助

- **详细文档**: 查看 `DEPLOYMENT_GUIDE.md`
- **系统测试**: 运行 `python test_final.py`
- **问题排查**: 查看日志文件 `logs/system.log`
- **技术支持**: 提交GitHub Issues

---

🎉 **恭喜！您已成功快速部署无人机交通警察系统！**

现在可以开始您的AI交通监控之旅了！
