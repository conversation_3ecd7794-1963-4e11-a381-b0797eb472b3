"""
摄像头接口
提供统一的摄像头数据获取接口
"""
import asyncio
import cv2
import logging
import numpy as np
from typing import Optional, Dict, Any, Callable
from datetime import datetime
from enum import Enum
import threading
import queue

from config.hardware_config import CameraType


class CameraStatus(str, Enum):
    """摄像头状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    STREAMING = "streaming"
    ERROR = "error"


class CameraInterface:
    """摄像头接口类"""
    
    def __init__(self, camera_id: int, config: Dict):
        self.camera_id = camera_id
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.camera_{camera_id}")
        
        # 状态管理
        self.status = CameraStatus.DISCONNECTED
        self.last_frame: Optional[np.ndarray] = None
        self.last_update = datetime.now()
        
        # 连接管理
        self._capture: Optional[cv2.VideoCapture] = None
        self._connected = False
        self._streaming = False
        
        # 帧缓冲
        self._frame_queue = queue.Queue(maxsize=10)
        self._capture_thread: Optional[threading.Thread] = None
        
        # 事件回调
        self.on_status_changed: Optional[Callable] = None
        self.on_frame_received: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 统计信息
        self.frame_count = 0
        self.fps_counter = 0
        self.last_fps_time = datetime.now()
        self.current_fps = 0.0
    
    async def connect(self) -> bool:
        """连接摄像头"""
        try:
            self.logger.info(f"Connecting to camera {self.camera_id}...")
            await self._set_status(CameraStatus.CONNECTING)
            
            camera_type = self.config.get("type", CameraType.FILE_SOURCE)
            
            if camera_type == CameraType.FILE_SOURCE:
                success = await self._connect_file_source()
            elif camera_type == CameraType.USB_CAMERA:
                success = await self._connect_usb_camera()
            elif camera_type == CameraType.IP_CAMERA:
                success = await self._connect_ip_camera()
            elif camera_type == CameraType.DRONE_CAMERA:
                success = await self._connect_drone_camera()
            else:
                raise ValueError(f"Unsupported camera type: {camera_type}")
            
            if success:
                self._connected = True
                await self._set_status(CameraStatus.CONNECTED)
                self.logger.info(f"Camera {self.camera_id} connected successfully")
                
                # 启动视频流
                await self.start_streaming()
                return True
            else:
                await self._set_status(CameraStatus.ERROR)
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect camera {self.camera_id}: {e}")
            await self._set_status(CameraStatus.ERROR)
            return False
    
    async def disconnect(self):
        """断开摄像头连接"""
        try:
            self.logger.info(f"Disconnecting camera {self.camera_id}...")
            
            # 停止视频流
            await self.stop_streaming()
            
            # 释放资源
            if self._capture:
                self._capture.release()
                self._capture = None
            
            self._connected = False
            await self._set_status(CameraStatus.DISCONNECTED)
            
            self.logger.info(f"Camera {self.camera_id} disconnected")
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect camera {self.camera_id}: {e}")
    
    async def _connect_file_source(self) -> bool:
        """连接文件视频源"""
        file_path = self.config.get("file_path", "action1.mp4")
        
        try:
            self._capture = cv2.VideoCapture(file_path)
            
            if not self._capture.isOpened():
                raise ValueError(f"Cannot open video file: {file_path}")
            
            # 获取视频信息
            fps = self._capture.get(cv2.CAP_PROP_FPS)
            frame_count = int(self._capture.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(self._capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self._capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            self.logger.info(f"Video file loaded: {file_path}")
            self.logger.info(f"Video info: {width}x{height}, {fps} FPS, {frame_count} frames")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to file source: {e}")
            return False
    
    async def _connect_usb_camera(self) -> bool:
        """连接USB摄像头"""
        device_id = self.config.get("device", 0)
        
        try:
            self._capture = cv2.VideoCapture(device_id)
            
            if not self._capture.isOpened():
                raise ValueError(f"Cannot open USB camera: {device_id}")
            
            # 设置摄像头参数
            width = self.config.get("resolution", {}).get("width", 1920)
            height = self.config.get("resolution", {}).get("height", 1080)
            fps = self.config.get("fps", 30)
            
            self._capture.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self._capture.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            self._capture.set(cv2.CAP_PROP_FPS, fps)
            
            # 设置缓冲区大小
            buffer_size = self.config.get("buffer_size", 1)
            self._capture.set(cv2.CAP_PROP_BUFFERSIZE, buffer_size)
            
            self.logger.info(f"USB camera connected: device {device_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to USB camera: {e}")
            return False
    
    async def _connect_ip_camera(self) -> bool:
        """连接IP摄像头"""
        url = self.config.get("url", "")
        timeout = self.config.get("timeout", 10)
        
        try:
            self._capture = cv2.VideoCapture(url)
            
            if not self._capture.isOpened():
                raise ValueError(f"Cannot open IP camera: {url}")
            
            # 设置超时
            self._capture.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)
            self._capture.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, timeout * 1000)
            
            self.logger.info(f"IP camera connected: {url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to IP camera: {e}")
            return False
    
    async def _connect_drone_camera(self) -> bool:
        """连接无人机摄像头"""
        # TODO: 实现无人机摄像头连接
        # 这通常需要通过无人机的SDK获取视频流
        self.logger.info("Drone camera connected")
        return True
    
    async def start_streaming(self) -> bool:
        """开始视频流"""
        if not self._connected or self._streaming:
            return False
        
        try:
            self._streaming = True
            await self._set_status(CameraStatus.STREAMING)
            
            # 启动帧捕获线程
            self._capture_thread = threading.Thread(target=self._capture_frames, daemon=True)
            self._capture_thread.start()
            
            # 启动帧处理任务
            asyncio.create_task(self._frame_processing_loop())
            
            self.logger.info(f"Camera {self.camera_id} streaming started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start streaming: {e}")
            return False
    
    async def stop_streaming(self):
        """停止视频流"""
        if not self._streaming:
            return
        
        try:
            self._streaming = False
            
            # 等待捕获线程结束
            if self._capture_thread and self._capture_thread.is_alive():
                self._capture_thread.join(timeout=5)
            
            # 清空帧队列
            while not self._frame_queue.empty():
                try:
                    self._frame_queue.get_nowait()
                except queue.Empty:
                    break
            
            await self._set_status(CameraStatus.CONNECTED)
            self.logger.info(f"Camera {self.camera_id} streaming stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop streaming: {e}")
    
    def _capture_frames(self):
        """帧捕获线程"""
        while self._streaming and self._capture:
            try:
                ret, frame = self._capture.read()
                
                if not ret:
                    # 如果是文件源且启用循环，重新开始
                    if (self.config.get("type") == CameraType.FILE_SOURCE and 
                        self.config.get("loop", True)):
                        self._capture.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        continue
                    else:
                        self.logger.warning("Failed to read frame")
                        break
                
                # 添加到帧队列
                if not self._frame_queue.full():
                    self._frame_queue.put((frame, datetime.now()))
                else:
                    # 队列满时，丢弃最旧的帧
                    try:
                        self._frame_queue.get_nowait()
                        self._frame_queue.put((frame, datetime.now()))
                    except queue.Empty:
                        pass
                
                self.frame_count += 1
                
                # 控制帧率
                if self.config.get("type") == CameraType.FILE_SOURCE:
                    fps = self._capture.get(cv2.CAP_PROP_FPS) or 30
                    import time
                    time.sleep(1.0 / fps)
                
            except Exception as e:
                self.logger.error(f"Error in frame capture: {e}")
                break
    
    async def _frame_processing_loop(self):
        """帧处理循环"""
        while self._streaming:
            try:
                # 从队列获取帧
                if not self._frame_queue.empty():
                    frame, timestamp = self._frame_queue.get()
                    
                    self.last_frame = frame
                    self.last_update = timestamp
                    
                    # 更新FPS统计
                    self._update_fps_stats()
                    
                    # 触发帧接收回调
                    if self.on_frame_received:
                        await self._call_callback(self.on_frame_received, self.camera_id, frame)
                
                await asyncio.sleep(0.01)  # 10ms间隔
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in frame processing: {e}")
                await asyncio.sleep(1)
    
    def _update_fps_stats(self):
        """更新FPS统计"""
        self.fps_counter += 1
        current_time = datetime.now()
        
        time_diff = (current_time - self.last_fps_time).total_seconds()
        if time_diff >= 1.0:  # 每秒更新一次FPS
            self.current_fps = self.fps_counter / time_diff
            self.fps_counter = 0
            self.last_fps_time = current_time
    
    async def get_frame(self) -> Optional[np.ndarray]:
        """获取最新帧"""
        if not self._streaming:
            return None
        
        return self.last_frame.copy() if self.last_frame is not None else None
    
    async def _set_status(self, new_status: CameraStatus):
        """设置摄像头状态"""
        if self.status != new_status:
            old_status = self.status
            self.status = new_status
            
            self.logger.info(f"Camera {self.camera_id} status changed: {old_status} -> {new_status}")
            
            # 触发状态变化回调
            if self.on_status_changed:
                await self._call_callback(self.on_status_changed, self.camera_id, new_status.value)
    
    async def _call_callback(self, callback: Callable, *args):
        """安全调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            self.logger.error(f"Error in callback: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected
    
    def is_streaming(self) -> bool:
        """检查是否正在流式传输"""
        return self._streaming
    
    def get_stats(self) -> Dict[str, Any]:
        """获取摄像头统计信息"""
        return {
            "camera_id": self.camera_id,
            "status": self.status.value,
            "connected": self._connected,
            "streaming": self._streaming,
            "frame_count": self.frame_count,
            "current_fps": self.current_fps,
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "queue_size": self._frame_queue.qsize(),
            "config": self.config
        }
