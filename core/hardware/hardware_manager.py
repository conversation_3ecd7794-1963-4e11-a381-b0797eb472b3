"""
硬件管理器
统一管理所有硬件设备的连接、状态和控制
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum

from config import hardware_config
from .drone_interface import DroneInterface
from .camera_interface import Camera<PERSON><PERSON>face
from .communication import CommunicationManager


class HardwareStatus(str, Enum):
    """硬件状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class HardwareManager:
    """硬件管理器类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = hardware_config
        
        # 硬件设备实例
        self.drones: Dict[int, DroneInterface] = {}
        self.cameras: Dict[int, CameraInterface] = {}
        self.communication: Optional[CommunicationManager] = None
        
        # 状态管理
        self.hardware_status: Dict[str, HardwareStatus] = {}
        self.last_heartbeat: Dict[str, datetime] = {}
        
        # 事件回调
        self.event_callbacks: Dict[str, List[callable]] = {
            "device_connected": [],
            "device_disconnected": [],
            "device_error": [],
            "data_received": []
        }
        
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> bool:
        """初始化硬件管理器"""
        try:
            self.logger.info("Initializing hardware manager...")
            
            # 初始化通信管理器
            if self.config.COMMUNICATION_TYPE:
                self.communication = CommunicationManager(self.config.get_communication_config())
                await self.communication.initialize()
            
            # 初始化无人机
            if self.config.DRONE_ENABLED:
                await self._initialize_drones()
            
            # 初始化摄像头
            if self.config.CAMERA_ENABLED:
                await self._initialize_cameras()
            
            # 启动监控任务
            self._running = True
            self._monitor_task = asyncio.create_task(self._monitor_hardware())
            
            self.logger.info("Hardware manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize hardware manager: {e}")
            return False
    
    async def _initialize_drones(self):
        """初始化无人机设备"""
        for drone_id in range(self.config.DRONE_COUNT):
            try:
                drone_config = self.config.get_drone_config(drone_id)
                drone = DroneInterface(drone_id, drone_config)
                
                # 注册事件回调
                drone.on_status_changed = self._on_drone_status_changed
                drone.on_data_received = self._on_drone_data_received
                
                self.drones[drone_id] = drone
                self.hardware_status[f"drone_{drone_id}"] = HardwareStatus.DISCONNECTED
                
                self.logger.info(f"Drone {drone_id} initialized")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize drone {drone_id}: {e}")
    
    async def _initialize_cameras(self):
        """初始化摄像头设备"""
        for camera_id in range(self.config.CAMERA_COUNT):
            try:
                camera_config = self.config.get_camera_config(camera_id)
                camera = CameraInterface(camera_id, camera_config)
                
                # 注册事件回调
                camera.on_status_changed = self._on_camera_status_changed
                camera.on_frame_received = self._on_camera_frame_received
                
                self.cameras[camera_id] = camera
                self.hardware_status[f"camera_{camera_id}"] = HardwareStatus.DISCONNECTED
                
                self.logger.info(f"Camera {camera_id} initialized")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize camera {camera_id}: {e}")
    
    async def connect_all(self) -> bool:
        """连接所有硬件设备"""
        try:
            self.logger.info("Connecting to all hardware devices...")
            
            # 连接无人机
            for drone_id, drone in self.drones.items():
                try:
                    await drone.connect()
                    self.logger.info(f"Drone {drone_id} connected")
                except Exception as e:
                    self.logger.error(f"Failed to connect drone {drone_id}: {e}")
            
            # 连接摄像头
            for camera_id, camera in self.cameras.items():
                try:
                    await camera.connect()
                    self.logger.info(f"Camera {camera_id} connected")
                except Exception as e:
                    self.logger.error(f"Failed to connect camera {camera_id}: {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect hardware devices: {e}")
            return False
    
    async def disconnect_all(self):
        """断开所有硬件设备连接"""
        try:
            self.logger.info("Disconnecting all hardware devices...")
            
            # 停止监控任务
            self._running = False
            if self._monitor_task:
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 断开无人机连接
            for drone_id, drone in self.drones.items():
                try:
                    await drone.disconnect()
                    self.logger.info(f"Drone {drone_id} disconnected")
                except Exception as e:
                    self.logger.error(f"Failed to disconnect drone {drone_id}: {e}")
            
            # 断开摄像头连接
            for camera_id, camera in self.cameras.items():
                try:
                    await camera.disconnect()
                    self.logger.info(f"Camera {camera_id} disconnected")
                except Exception as e:
                    self.logger.error(f"Failed to disconnect camera {camera_id}: {e}")
            
            # 断开通信连接
            if self.communication:
                await self.communication.disconnect()
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect hardware devices: {e}")
    
    async def _monitor_hardware(self):
        """监控硬件设备状态"""
        while self._running:
            try:
                # 检查无人机状态
                for drone_id, drone in self.drones.items():
                    if drone.is_connected():
                        self.last_heartbeat[f"drone_{drone_id}"] = datetime.now()
                        
                        # 获取无人机状态
                        status = await drone.get_status()
                        if status:
                            await self._emit_event("data_received", {
                                "device_type": "drone",
                                "device_id": drone_id,
                                "data": status
                            })
                
                # 检查摄像头状态
                for camera_id, camera in self.cameras.items():
                    if camera.is_connected():
                        self.last_heartbeat[f"camera_{camera_id}"] = datetime.now()
                
                # 检查设备超时
                await self._check_device_timeouts()
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in hardware monitoring: {e}")
                await asyncio.sleep(1)
    
    async def _check_device_timeouts(self):
        """检查设备超时"""
        current_time = datetime.now()
        timeout_threshold = 30  # 30秒超时
        
        for device_key, last_time in self.last_heartbeat.items():
            if (current_time - last_time).total_seconds() > timeout_threshold:
                if self.hardware_status.get(device_key) == HardwareStatus.CONNECTED:
                    self.hardware_status[device_key] = HardwareStatus.ERROR
                    await self._emit_event("device_error", {
                        "device": device_key,
                        "error": "Device timeout"
                    })
    
    # 事件处理方法
    async def _on_drone_status_changed(self, drone_id: int, status: str):
        """无人机状态变化回调"""
        device_key = f"drone_{drone_id}"
        old_status = self.hardware_status.get(device_key)
        new_status = HardwareStatus(status)
        
        if old_status != new_status:
            self.hardware_status[device_key] = new_status
            
            if new_status == HardwareStatus.CONNECTED:
                await self._emit_event("device_connected", {
                    "device_type": "drone",
                    "device_id": drone_id
                })
            elif new_status == HardwareStatus.DISCONNECTED:
                await self._emit_event("device_disconnected", {
                    "device_type": "drone", 
                    "device_id": drone_id
                })
    
    async def _on_drone_data_received(self, drone_id: int, data: Dict):
        """无人机数据接收回调"""
        await self._emit_event("data_received", {
            "device_type": "drone",
            "device_id": drone_id,
            "data": data
        })
    
    async def _on_camera_status_changed(self, camera_id: int, status: str):
        """摄像头状态变化回调"""
        device_key = f"camera_{camera_id}"
        old_status = self.hardware_status.get(device_key)
        new_status = HardwareStatus(status)
        
        if old_status != new_status:
            self.hardware_status[device_key] = new_status
            
            if new_status == HardwareStatus.CONNECTED:
                await self._emit_event("device_connected", {
                    "device_type": "camera",
                    "device_id": camera_id
                })
            elif new_status == HardwareStatus.DISCONNECTED:
                await self._emit_event("device_disconnected", {
                    "device_type": "camera",
                    "device_id": camera_id
                })
    
    async def _on_camera_frame_received(self, camera_id: int, frame: Any):
        """摄像头帧数据接收回调"""
        await self._emit_event("data_received", {
            "device_type": "camera",
            "device_id": camera_id,
            "data": {"frame": frame, "timestamp": datetime.now()}
        })
    
    async def _emit_event(self, event_type: str, data: Dict):
        """触发事件"""
        callbacks = self.event_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                self.logger.error(f"Error in event callback: {e}")
    
    # 公共接口方法
    def register_event_callback(self, event_type: str, callback: callable):
        """注册事件回调"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
    
    def get_hardware_status(self) -> Dict[str, Any]:
        """获取硬件状态"""
        return {
            "drones": {
                drone_id: {
                    "status": self.hardware_status.get(f"drone_{drone_id}", "unknown"),
                    "last_heartbeat": self.last_heartbeat.get(f"drone_{drone_id}")
                }
                for drone_id in self.drones.keys()
            },
            "cameras": {
                camera_id: {
                    "status": self.hardware_status.get(f"camera_{camera_id}", "unknown"),
                    "last_heartbeat": self.last_heartbeat.get(f"camera_{camera_id}")
                }
                for camera_id in self.cameras.keys()
            },
            "communication": {
                "status": "connected" if self.communication and self.communication.is_connected() else "disconnected"
            }
        }
    
    async def control_drone(self, drone_id: int, command: str, params: Dict = None) -> bool:
        """控制无人机"""
        if drone_id not in self.drones:
            return False
        
        try:
            drone = self.drones[drone_id]
            return await drone.execute_command(command, params or {})
        except Exception as e:
            self.logger.error(f"Failed to control drone {drone_id}: {e}")
            return False
    
    async def get_camera_frame(self, camera_id: int) -> Optional[Any]:
        """获取摄像头帧"""
        if camera_id not in self.cameras:
            return None

        try:
            camera = self.cameras[camera_id]
            return await camera.get_frame()
        except Exception as e:
            self.logger.error(f"Failed to get frame from camera {camera_id}: {e}")
            return None

    def is_ready(self) -> bool:
        """检查硬件是否就绪"""
        # 检查至少有一个摄像头连接
        camera_ready = any(
            self.hardware_status.get(f"camera_{cid}") == HardwareStatus.CONNECTED
            for cid in self.cameras.keys()
        )

        # 如果启用了无人机，检查无人机状态
        if self.config.DRONE_ENABLED:
            drone_ready = any(
                self.hardware_status.get(f"drone_{did}") == HardwareStatus.CONNECTED
                for did in self.drones.keys()
            )
            return camera_ready and drone_ready

        return camera_ready
