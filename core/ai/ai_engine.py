"""
AI引擎
整合检测、跟踪、行为分析等AI功能
"""
import logging
import time
import asyncio
from typing import List, Dict, Optional, Any, Callable
import numpy as np

from config import ai_config
from .detection import YOLODetector, Detection
from .detection.helmet_detector import HelmetDetector, HelmetDetection
from .tracking import ObjectTracker, Track
from .behavior_analyzer import BehaviorA<PERSON>yzer, BehaviorEvent


class AIEngine:
    """AI引擎类"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # AI组件
        self.detector: Optional[YOLODetector] = None
        self.helmet_detector: Optional[HelmetDetector] = None
        self.tracker: Optional[ObjectTracker] = None
        self.behavior_analyzer: Optional[BehaviorAnalyzer] = None
        
        # 状态管理
        self.initialized = False
        self.processing = False
        
        # 事件回调
        self.on_detections: Optional[Callable] = None
        self.on_helmet_violations: Optional[Callable] = None
        self.on_tracks: Optional[Callable] = None
        self.on_behaviors: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 性能统计
        self.frame_count = 0
        self.total_processing_time = 0.0
        self.last_fps_update = time.time()
        self.current_fps = 0.0
        
        # 结果缓存
        self.last_detections: List[Detection] = []
        self.last_helmet_violations: List[HelmetDetection] = []
        self.last_tracks: List[Track] = []
        self.last_behaviors: List[BehaviorEvent] = []
    
    async def initialize(self) -> bool:
        """初始化AI引擎"""
        try:
            self.logger.info("Initializing AI engine...")
            
            # 初始化检测器
            if ai_config.MODEL_TYPE:
                self.detector = YOLODetector(ai_config.get_model_config())
                if not self.detector.load_model():
                    raise RuntimeError("Failed to load detection model")
                self.logger.info("Detection model loaded")

            # 初始化安全帽检测器
            helmet_config = self.config.get("helmet_detection", {})
            if helmet_config.get("enabled", True):  # 默认启用安全帽检测
                self.helmet_detector = HelmetDetector(helmet_config)
                self.logger.info("Helmet detector initialized")
            
            # 初始化跟踪器
            if ai_config.TRACKING_ENABLED:
                self.tracker = ObjectTracker(ai_config.get_tracking_config())
                self.logger.info("Object tracker initialized")
            
            # 初始化行为分析器
            if ai_config.BEHAVIOR_ANALYSIS_ENABLED:
                self.behavior_analyzer = BehaviorAnalyzer()
                self.logger.info("Behavior analyzer initialized")
            
            self.initialized = True
            self.logger.info("AI engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize AI engine: {e}")
            return False

    async def start(self) -> bool:
        """启动AI引擎"""
        try:
            if not self.initialized:
                self.logger.error("AI engine not initialized")
                return False

            self.logger.info("Starting AI engine...")
            # 这里可以添加启动逻辑

            self.logger.info("AI engine started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start AI engine: {e}")
            return False

    async def stop(self) -> bool:
        """停止AI引擎"""
        try:
            self.logger.info("Stopping AI engine...")

            # 停止处理
            self.processing = False

            # 这里可以添加停止逻辑

            self.logger.info("AI engine stopped successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to stop AI engine: {e}")
            return False

    async def get_health_status(self) -> str:
        """获取健康状态"""
        if not self.initialized:
            return "not_initialized"
        elif self.processing:
            return "processing"
        else:
            return "healthy"

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.get_overall_statistics()
    
    async def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """处理单帧图像"""
        if not self.initialized:
            await self.initialize()
        
        if self.processing:
            self.logger.warning("Previous frame still processing, skipping")
            return self._get_last_results()
        
        self.processing = True
        start_time = time.time()
        
        try:
            results = {}
            
            # 目标检测
            detections = []
            if self.detector:
                detections = self.detector.detect(frame)
                self.last_detections = detections
                results["detections"] = [det.to_dict() for det in detections]

                # 触发检测回调
                if self.on_detections:
                    await self._safe_callback(self.on_detections, detections)

            # 安全帽检测
            helmet_violations = []
            if self.helmet_detector:
                helmet_violations = self.helmet_detector.detect_helmet_violations(frame)
                self.last_helmet_violations = helmet_violations
                results["helmet_violations"] = [self._helmet_detection_to_dict(hv) for hv in helmet_violations]

                # 触发安全帽违规回调
                if self.on_helmet_violations:
                    await self._safe_callback(self.on_helmet_violations, helmet_violations)
            
            # 目标跟踪
            tracks = []
            if self.tracker and detections:
                tracked_detections = self.tracker.update(detections)
                tracks = self.tracker.get_active_tracks()
                self.last_tracks = tracks
                results["tracks"] = [self._track_to_dict(track) for track in tracks]
                results["tracked_detections"] = [det.to_dict() for det in tracked_detections]
                
                # 触发跟踪回调
                if self.on_tracks:
                    await self._safe_callback(self.on_tracks, tracks)
            
            # 行为分析
            behaviors = []
            if self.behavior_analyzer and tracks:
                behaviors = self.behavior_analyzer.analyze(tracks)
                self.last_behaviors = behaviors
                results["behaviors"] = [behavior.to_dict() for behavior in behaviors]
                
                # 触发行为回调
                if self.on_behaviors:
                    await self._safe_callback(self.on_behaviors, behaviors)
            
            # 更新性能统计
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time
            self.frame_count += 1
            self._update_fps()
            
            # 添加性能信息
            results["performance"] = {
                "processing_time": processing_time,
                "fps": self.current_fps,
                "frame_count": self.frame_count
            }
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            if self.on_error:
                await self._safe_callback(self.on_error, e)
            return self._get_last_results()
        
        finally:
            self.processing = False
    
    def _helmet_detection_to_dict(self, helmet_detection: HelmetDetection) -> Dict[str, Any]:
        """将安全帽检测结果转换为字典"""
        return {
            "person": helmet_detection.person_detection.to_dict(),
            "vehicle": helmet_detection.vehicle_detection.to_dict() if helmet_detection.vehicle_detection else None,
            "helmet_detected": helmet_detection.helmet_detected,
            "helmet_confidence": helmet_detection.helmet_confidence,
            "violation_type": helmet_detection.violation_type,
            "risk_level": helmet_detection.risk_level,
            "metadata": helmet_detection.metadata
        }

    def _track_to_dict(self, track: Track) -> Dict[str, Any]:
        """将轨迹转换为字典"""
        return {
            "track_id": track.track_id,
            "class_name": track.class_name,
            "last_detection": track.last_detection.to_dict() if track.last_detection else None,
            "velocity": track.velocity,
            "speed": track.get_speed(),
            "direction": track.get_direction(),
            "age": track.age,
            "hits": track.hits,
            "avg_confidence": track.avg_confidence,
            "is_stationary": track.is_stationary(),
            "trajectory": track.get_trajectory()[-10:]  # 最近10个点
        }
    
    def _get_last_results(self) -> Dict[str, Any]:
        """获取最后的处理结果"""
        return {
            "detections": [det.to_dict() for det in self.last_detections],
            "helmet_violations": [self._helmet_detection_to_dict(hv) for hv in self.last_helmet_violations],
            "tracks": [self._track_to_dict(track) for track in self.last_tracks],
            "behaviors": [behavior.to_dict() for behavior in self.last_behaviors],
            "performance": {
                "processing_time": 0.0,
                "fps": self.current_fps,
                "frame_count": self.frame_count
            }
        }
    
    def _update_fps(self):
        """更新FPS统计"""
        current_time = time.time()
        if current_time - self.last_fps_update >= 1.0:  # 每秒更新一次
            time_diff = current_time - self.last_fps_update
            self.current_fps = 1.0 / (self.total_processing_time / self.frame_count) if self.frame_count > 0 else 0
            self.last_fps_update = current_time
    
    async def _safe_callback(self, callback: Callable, *args):
        """安全调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            self.logger.error(f"Error in callback: {e}")
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        if self.detector:
            return self.detector.get_statistics()
        return {}
    
    def get_tracking_statistics(self) -> Dict[str, Any]:
        """获取跟踪统计信息"""
        if self.tracker:
            return self.tracker.get_statistics()
        return {}
    
    def get_behavior_statistics(self) -> Dict[str, Any]:
        """获取行为分析统计信息"""
        if self.behavior_analyzer:
            return self.behavior_analyzer.get_statistics()
        return {}

    def get_helmet_statistics(self) -> Dict[str, Any]:
        """获取安全帽检测统计信息"""
        if self.helmet_detector:
            return self.helmet_detector.get_violation_statistics()
        return {}
    
    def get_overall_statistics(self) -> Dict[str, Any]:
        """获取整体统计信息"""
        return {
            "ai_engine": {
                "initialized": self.initialized,
                "frame_count": self.frame_count,
                "total_processing_time": self.total_processing_time,
                "avg_processing_time": self.total_processing_time / self.frame_count if self.frame_count > 0 else 0,
                "current_fps": self.current_fps
            },
            "detection": self.get_detection_statistics(),
            "helmet_detection": self.get_helmet_statistics(),
            "tracking": self.get_tracking_statistics(),
            "behavior": self.get_behavior_statistics()
        }
    
    def get_recent_behaviors(self, time_window: float = 60.0) -> List[Dict[str, Any]]:
        """获取最近的行为事件"""
        if self.behavior_analyzer:
            events = self.behavior_analyzer.get_recent_events(time_window)
            return [event.to_dict() for event in events]
        return []
    
    def get_track_by_id(self, track_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取轨迹信息"""
        if self.tracker:
            track = self.tracker.get_track_by_id(track_id)
            if track:
                return self._track_to_dict(track)
        return None
    
    def get_track_behavior_summary(self, track_id: int) -> Dict[str, Any]:
        """获取轨迹行为摘要"""
        if self.behavior_analyzer:
            return self.behavior_analyzer.get_track_behavior_summary(track_id)
        return {}
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        
        # 更新各组件配置
        if self.detector and "detection" in new_config:
            self.detector.update_config(new_config["detection"])
        
        if self.tracker and "tracking" in new_config:
            # 跟踪器配置更新需要重新初始化
            pass
        
        if self.behavior_analyzer and "behavior" in new_config:
            # 行为分析器配置更新
            pass
        
        self.logger.info("AI engine configuration updated")
    
    def reset(self):
        """重置AI引擎"""
        if self.detector:
            self.detector.reset_statistics()

        if self.helmet_detector:
            self.helmet_detector.reset_statistics()

        if self.tracker:
            self.tracker.reset()

        if self.behavior_analyzer:
            self.behavior_analyzer.reset()

        # 重置统计信息
        self.frame_count = 0
        self.total_processing_time = 0.0
        self.current_fps = 0.0
        self.last_fps_update = time.time()

        # 清空结果缓存
        self.last_detections.clear()
        self.last_helmet_violations.clear()
        self.last_tracks.clear()
        self.last_behaviors.clear()

        self.logger.info("AI engine reset")
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.processing = False

            # 清理各组件
            if self.detector:
                del self.detector
                self.detector = None

            if self.helmet_detector:
                self.helmet_detector = None

            if self.tracker:
                self.tracker = None

            if self.behavior_analyzer:
                self.behavior_analyzer = None

            self.initialized = False
            self.logger.info("AI engine cleaned up")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'initialized') and self.initialized:
            # 注意：在析构函数中不能使用async
            pass
