"""
行为分析器
分析目标的行为模式，检测异常行为
"""
import logging
import time
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from enum import Enum
from collections import defaultdict, deque

from config import ai_config
from .detection import Detection
from .tracking import Track, ObjectTracker


class BehaviorType(str, Enum):
    """行为类型枚举"""
    NORMAL = "normal"
    STATIONARY = "stationary"
    FAST_MOVING = "fast_moving"
    ERRATIC = "erratic"
    COLLISION = "collision"
    WRONG_DIRECTION = "wrong_direction"
    LANE_VIOLATION = "lane_violation"
    SPEED_VIOLATION = "speed_violation"


class BehaviorEvent:
    """行为事件类"""
    
    def __init__(self, 
                 behavior_type: BehaviorType,
                 track_id: int,
                 confidence: float,
                 timestamp: float,
                 location: Tuple[float, float],
                 description: str = "",
                 metadata: Optional[Dict] = None):
        self.behavior_type = behavior_type
        self.track_id = track_id
        self.confidence = confidence
        self.timestamp = timestamp
        self.location = location
        self.description = description
        self.metadata = metadata or {}
        self.event_id = f"{behavior_type}_{track_id}_{int(timestamp)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "event_id": self.event_id,
            "behavior_type": self.behavior_type,
            "track_id": self.track_id,
            "confidence": self.confidence,
            "timestamp": self.timestamp,
            "location": self.location,
            "description": self.description,
            "metadata": self.metadata
        }


class BehaviorAnalyzer:
    """行为分析器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 分析参数
        self.stationary_threshold = self.config.get("stationary_threshold", 5.0)  # 像素/秒
        self.fast_speed_threshold = self.config.get("fast_speed_threshold", 100.0)  # 像素/秒
        self.erratic_threshold = self.config.get("erratic_threshold", 50.0)  # 方向变化阈值
        self.collision_distance_threshold = self.config.get("collision_distance", 50.0)  # 像素
        
        # 时间窗口
        self.analysis_window = self.config.get("analysis_window", 30)  # 帧数
        self.stationary_time_threshold = self.config.get("stationary_time", 60)  # 秒
        
        # 历史数据
        self.track_histories: Dict[int, deque] = defaultdict(lambda: deque(maxlen=self.analysis_window))
        self.behavior_events: List[BehaviorEvent] = []
        self.recent_events: deque = deque(maxlen=100)
        
        # 统计信息
        self.total_events = 0
        self.event_counts = defaultdict(int)
    
    def analyze(self, tracks: List[Track]) -> List[BehaviorEvent]:
        """分析轨迹行为"""
        current_events = []
        current_time = time.time()
        
        for track in tracks:
            if not track.is_active or track.hits < 3:
                continue
            
            # 更新轨迹历史
            self._update_track_history(track)
            
            # 分析各种行为
            events = []
            events.extend(self._analyze_stationary_behavior(track, current_time))
            events.extend(self._analyze_speed_behavior(track, current_time))
            events.extend(self._analyze_erratic_behavior(track, current_time))
            
            current_events.extend(events)
        
        # 分析轨迹间的交互行为
        interaction_events = self._analyze_interactions(tracks, current_time)
        current_events.extend(interaction_events)
        
        # 过滤重复事件
        current_events = self._filter_duplicate_events(current_events)
        
        # 更新事件历史
        for event in current_events:
            self.behavior_events.append(event)
            self.recent_events.append(event)
            self.total_events += 1
            self.event_counts[event.behavior_type] += 1
        
        return current_events
    
    def _update_track_history(self, track: Track):
        """更新轨迹历史"""
        if track.last_detection:
            history_entry = {
                "timestamp": track.last_update,
                "position": track.last_detection.center,
                "velocity": track.velocity,
                "speed": track.get_speed(),
                "direction": track.get_direction(),
                "confidence": track.last_detection.confidence
            }
            self.track_histories[track.track_id].append(history_entry)
    
    def _analyze_stationary_behavior(self, track: Track, current_time: float) -> List[BehaviorEvent]:
        """分析静止行为"""
        events = []
        
        if track.is_stationary(self.stationary_threshold):
            # 检查静止时间
            history = self.track_histories[track.track_id]
            if len(history) >= 2:
                stationary_duration = current_time - history[0]["timestamp"]
                
                if stationary_duration > self.stationary_time_threshold:
                    event = BehaviorEvent(
                        behavior_type=BehaviorType.STATIONARY,
                        track_id=track.track_id,
                        confidence=0.9,
                        timestamp=current_time,
                        location=track.last_detection.center,
                        description=f"目标静止超过{stationary_duration:.1f}秒",
                        metadata={
                            "duration": stationary_duration,
                            "class_name": track.class_name
                        }
                    )
                    events.append(event)
        
        return events
    
    def _analyze_speed_behavior(self, track: Track, current_time: float) -> List[BehaviorEvent]:
        """分析速度行为"""
        events = []
        current_speed = track.get_speed()
        
        # 检测高速行为
        if current_speed > self.fast_speed_threshold:
            event = BehaviorEvent(
                behavior_type=BehaviorType.FAST_MOVING,
                track_id=track.track_id,
                confidence=min(0.9, current_speed / self.fast_speed_threshold * 0.5),
                timestamp=current_time,
                location=track.last_detection.center,
                description=f"目标高速移动，速度: {current_speed:.1f} 像素/秒",
                metadata={
                    "speed": current_speed,
                    "class_name": track.class_name
                }
            )
            events.append(event)
        
        # 检测速度异常变化
        history = self.track_histories[track.track_id]
        if len(history) >= 5:
            recent_speeds = [entry["speed"] for entry in list(history)[-5:]]
            speed_variance = np.var(recent_speeds)
            
            if speed_variance > 1000:  # 速度变化过大
                event = BehaviorEvent(
                    behavior_type=BehaviorType.SPEED_VIOLATION,
                    track_id=track.track_id,
                    confidence=0.7,
                    timestamp=current_time,
                    location=track.last_detection.center,
                    description=f"目标速度变化异常，方差: {speed_variance:.1f}",
                    metadata={
                        "speed_variance": speed_variance,
                        "recent_speeds": recent_speeds,
                        "class_name": track.class_name
                    }
                )
                events.append(event)
        
        return events
    
    def _analyze_erratic_behavior(self, track: Track, current_time: float) -> List[BehaviorEvent]:
        """分析不规律行为"""
        events = []
        history = self.track_histories[track.track_id]
        
        if len(history) >= 5:
            # 分析方向变化
            recent_directions = [entry["direction"] for entry in list(history)[-5:]]
            direction_changes = []
            
            for i in range(1, len(recent_directions)):
                change = abs(recent_directions[i] - recent_directions[i-1])
                # 处理角度跨越180度的情况
                if change > 180:
                    change = 360 - change
                direction_changes.append(change)
            
            avg_direction_change = np.mean(direction_changes)
            
            if avg_direction_change > self.erratic_threshold:
                event = BehaviorEvent(
                    behavior_type=BehaviorType.ERRATIC,
                    track_id=track.track_id,
                    confidence=min(0.9, avg_direction_change / 90.0),
                    timestamp=current_time,
                    location=track.last_detection.center,
                    description=f"目标运动不规律，平均方向变化: {avg_direction_change:.1f}度",
                    metadata={
                        "avg_direction_change": avg_direction_change,
                        "direction_changes": direction_changes,
                        "class_name": track.class_name
                    }
                )
                events.append(event)
        
        return events
    
    def _analyze_interactions(self, tracks: List[Track], current_time: float) -> List[BehaviorEvent]:
        """分析轨迹间的交互行为"""
        events = []
        
        # 检测碰撞
        for i, track1 in enumerate(tracks):
            for j, track2 in enumerate(tracks[i+1:], i+1):
                if not (track1.is_active and track2.is_active):
                    continue
                
                if not (track1.last_detection and track2.last_detection):
                    continue
                
                # 计算距离
                distance = track1.last_detection.distance_to(track2.last_detection)
                
                if distance < self.collision_distance_threshold:
                    # 检查是否是真正的碰撞（速度方向相向）
                    v1 = np.array(track1.velocity)
                    v2 = np.array(track2.velocity)
                    
                    # 计算相对速度
                    relative_velocity = np.linalg.norm(v1 - v2)
                    
                    if relative_velocity > 20:  # 相对速度阈值
                        event = BehaviorEvent(
                            behavior_type=BehaviorType.COLLISION,
                            track_id=track1.track_id,
                            confidence=0.8,
                            timestamp=current_time,
                            location=track1.last_detection.center,
                            description=f"检测到潜在碰撞: 轨迹{track1.track_id}和{track2.track_id}",
                            metadata={
                                "other_track_id": track2.track_id,
                                "distance": distance,
                                "relative_velocity": relative_velocity,
                                "track1_class": track1.class_name,
                                "track2_class": track2.class_name
                            }
                        )
                        events.append(event)
        
        return events
    
    def _filter_duplicate_events(self, events: List[BehaviorEvent]) -> List[BehaviorEvent]:
        """过滤重复事件"""
        filtered_events = []
        
        for event in events:
            # 检查最近是否有相同类型的事件
            is_duplicate = False
            for recent_event in list(self.recent_events)[-10:]:  # 检查最近10个事件
                if (recent_event.behavior_type == event.behavior_type and
                    recent_event.track_id == event.track_id and
                    abs(recent_event.timestamp - event.timestamp) < 5.0):  # 5秒内
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered_events.append(event)
        
        return filtered_events
    
    def get_recent_events(self, time_window: float = 60.0) -> List[BehaviorEvent]:
        """获取最近的行为事件"""
        current_time = time.time()
        recent_events = []
        
        for event in reversed(self.behavior_events):
            if current_time - event.timestamp <= time_window:
                recent_events.append(event)
            else:
                break
        
        return list(reversed(recent_events))
    
    def get_track_behavior_summary(self, track_id: int) -> Dict[str, Any]:
        """获取特定轨迹的行为摘要"""
        track_events = [event for event in self.behavior_events 
                       if event.track_id == track_id]
        
        if not track_events:
            return {"track_id": track_id, "event_count": 0, "behaviors": []}
        
        behavior_counts = defaultdict(int)
        for event in track_events:
            behavior_counts[event.behavior_type] += 1
        
        return {
            "track_id": track_id,
            "event_count": len(track_events),
            "behaviors": dict(behavior_counts),
            "latest_event": track_events[-1].to_dict() if track_events else None,
            "first_event_time": track_events[0].timestamp if track_events else None,
            "last_event_time": track_events[-1].timestamp if track_events else None
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取行为分析统计信息"""
        return {
            "total_events": self.total_events,
            "event_counts": dict(self.event_counts),
            "active_tracks": len(self.track_histories),
            "recent_events_count": len(self.recent_events),
            "config": self.config
        }
    
    def reset(self):
        """重置分析器"""
        self.track_histories.clear()
        self.behavior_events.clear()
        self.recent_events.clear()
        self.total_events = 0
        self.event_counts.clear()
        self.logger.info("Behavior analyzer reset")
