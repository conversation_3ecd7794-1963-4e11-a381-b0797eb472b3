"""
目标跟踪模块
实现多目标跟踪算法
"""
import logging
import time
from typing import List, Dict, Optional, Tuple, Any
import numpy as np
from collections import defaultdict, deque
from dataclasses import dataclass, field

from config import ai_config
from .detection import Detection


@dataclass
class Track:
    """跟踪轨迹类"""
    track_id: int
    class_name: str
    detections: deque = field(default_factory=lambda: deque(maxlen=30))
    last_detection: Optional[Detection] = None
    last_update: float = field(default_factory=time.time)
    
    # 状态信息
    is_active: bool = True
    age: int = 0  # 轨迹存在的帧数
    hits: int = 0  # 成功匹配的次数
    time_since_update: int = 0  # 自上次更新以来的帧数
    
    # 运动信息
    velocity: Tuple[float, float] = (0.0, 0.0)
    predicted_position: Optional[Tuple[float, float]] = None
    
    # 统计信息
    avg_confidence: float = 0.0
    max_confidence: float = 0.0
    
    def update(self, detection: Detection):
        """更新轨迹"""
        # 计算速度
        if self.last_detection:
            dt = detection.timestamp - self.last_detection.timestamp if detection.timestamp else 1.0
            if dt > 0:
                dx = detection.center[0] - self.last_detection.center[0]
                dy = detection.center[1] - self.last_detection.center[1]
                self.velocity = (dx / dt, dy / dt)
        
        # 更新检测历史
        self.detections.append(detection)
        self.last_detection = detection
        self.last_update = time.time()
        
        # 更新状态
        self.hits += 1
        self.time_since_update = 0
        self.age += 1
        
        # 更新统计信息
        confidences = [d.confidence for d in self.detections]
        self.avg_confidence = sum(confidences) / len(confidences)
        self.max_confidence = max(confidences)
        
        # 预测下一帧位置
        self.predicted_position = (
            detection.center[0] + self.velocity[0],
            detection.center[1] + self.velocity[1]
        )
    
    def predict(self) -> Optional[Detection]:
        """预测当前帧的检测结果"""
        if not self.last_detection or not self.predicted_position:
            return None
        
        # 基于速度预测新位置
        predicted_center = self.predicted_position
        
        # 保持边界框大小
        last_bbox = self.last_detection.bbox
        width = last_bbox[2] - last_bbox[0]
        height = last_bbox[3] - last_bbox[1]
        
        predicted_bbox = (
            predicted_center[0] - width / 2,
            predicted_center[1] - height / 2,
            predicted_center[0] + width / 2,
            predicted_center[1] + height / 2
        )
        
        return Detection(
            bbox=predicted_bbox,
            confidence=self.avg_confidence * 0.8,  # 降低预测的置信度
            class_id=self.last_detection.class_id,
            class_name=self.class_name,
            track_id=self.track_id,
            timestamp=time.time()
        )
    
    def get_trajectory(self) -> List[Tuple[float, float]]:
        """获取轨迹路径"""
        return [det.center for det in self.detections]
    
    def get_speed(self) -> float:
        """获取速度大小"""
        return np.sqrt(self.velocity[0] ** 2 + self.velocity[1] ** 2)
    
    def is_stationary(self, threshold: float = 5.0) -> bool:
        """判断是否静止"""
        return self.get_speed() < threshold
    
    def get_direction(self) -> float:
        """获取运动方向（角度）"""
        if self.velocity[0] == 0 and self.velocity[1] == 0:
            return 0.0
        return np.arctan2(self.velocity[1], self.velocity[0]) * 180 / np.pi


class ObjectTracker:
    """目标跟踪器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or ai_config.get_tracking_config()
        self.logger = logging.getLogger(__name__)
        
        # 跟踪参数
        self.max_age = self.config.get("max_age", 30)
        self.min_hits = self.config.get("min_hits", 3)
        self.iou_threshold = self.config.get("iou_threshold", 0.3)
        self.distance_threshold = self.config.get("distance_threshold", 100.0)
        
        # 跟踪状态
        self.tracks: Dict[int, Track] = {}
        self.next_track_id = 1
        self.frame_count = 0
        
        # 统计信息
        self.total_tracks_created = 0
        self.total_tracks_deleted = 0
        self.active_tracks_count = 0
    
    def update(self, detections: List[Detection]) -> List[Detection]:
        """更新跟踪器"""
        self.frame_count += 1
        
        # 预测现有轨迹的位置
        predicted_tracks = self._predict_tracks()
        
        # 数据关联
        matched_tracks, unmatched_detections, unmatched_tracks = self._associate(
            detections, predicted_tracks
        )
        
        # 更新匹配的轨迹
        for track_id, detection in matched_tracks:
            self.tracks[track_id].update(detection)
            detection.track_id = track_id
        
        # 创建新轨迹
        for detection in unmatched_detections:
            self._create_track(detection)
        
        # 更新未匹配轨迹的状态
        for track_id in unmatched_tracks:
            track = self.tracks[track_id]
            track.time_since_update += 1
            track.age += 1
        
        # 删除过期轨迹
        self._delete_old_tracks()
        
        # 返回带有轨迹ID的检测结果
        tracked_detections = []
        for detection in detections:
            if detection.track_id is not None:
                tracked_detections.append(detection)
        
        # 更新统计信息
        self.active_tracks_count = len([t for t in self.tracks.values() if t.is_active])
        
        return tracked_detections
    
    def _predict_tracks(self) -> Dict[int, Detection]:
        """预测轨迹位置"""
        predicted = {}
        for track_id, track in self.tracks.items():
            if track.is_active:
                prediction = track.predict()
                if prediction:
                    predicted[track_id] = prediction
        return predicted
    
    def _associate(self, detections: List[Detection], 
                  predicted_tracks: Dict[int, Detection]) -> Tuple[List[Tuple[int, Detection]], 
                                                                  List[Detection], 
                                                                  List[int]]:
        """数据关联"""
        if not detections or not predicted_tracks:
            return [], detections, list(predicted_tracks.keys())
        
        # 计算相似度矩阵
        similarity_matrix = self._compute_similarity_matrix(detections, predicted_tracks)
        
        # 匈牙利算法进行最优匹配
        matched_pairs = self._hungarian_matching(similarity_matrix)
        
        # 分离匹配和未匹配的结果
        matched_tracks = []
        unmatched_detections = list(detections)
        unmatched_tracks = list(predicted_tracks.keys())
        
        for det_idx, track_idx in matched_pairs:
            if det_idx < len(detections) and track_idx < len(predicted_tracks):
                detection = detections[det_idx]
                track_id = list(predicted_tracks.keys())[track_idx]
                
                # 检查匹配质量
                predicted_det = predicted_tracks[track_id]
                if (detection.class_name == predicted_det.class_name and
                    detection.iou(predicted_det) > self.iou_threshold):
                    
                    matched_tracks.append((track_id, detection))
                    if detection in unmatched_detections:
                        unmatched_detections.remove(detection)
                    if track_id in unmatched_tracks:
                        unmatched_tracks.remove(track_id)
        
        return matched_tracks, unmatched_detections, unmatched_tracks
    
    def _compute_similarity_matrix(self, detections: List[Detection], 
                                 predicted_tracks: Dict[int, Detection]) -> np.ndarray:
        """计算相似度矩阵"""
        num_detections = len(detections)
        num_tracks = len(predicted_tracks)
        
        if num_detections == 0 or num_tracks == 0:
            return np.array([])
        
        similarity_matrix = np.zeros((num_detections, num_tracks))
        track_list = list(predicted_tracks.values())
        
        for i, detection in enumerate(detections):
            for j, predicted_det in enumerate(track_list):
                # 计算IoU相似度
                iou = detection.iou(predicted_det)
                
                # 计算距离相似度
                distance = detection.distance_to(predicted_det)
                distance_similarity = max(0, 1 - distance / self.distance_threshold)
                
                # 类别匹配
                class_match = 1.0 if detection.class_name == predicted_det.class_name else 0.0
                
                # 综合相似度
                similarity = (iou * 0.5 + distance_similarity * 0.3 + class_match * 0.2)
                similarity_matrix[i, j] = similarity
        
        return similarity_matrix
    
    def _hungarian_matching(self, similarity_matrix: np.ndarray) -> List[Tuple[int, int]]:
        """简化的匈牙利算法匹配"""
        if similarity_matrix.size == 0:
            return []
        
        # 简单的贪心匹配算法（可以替换为真正的匈牙利算法）
        matches = []
        used_detections = set()
        used_tracks = set()
        
        # 按相似度排序
        indices = np.unravel_index(np.argsort(-similarity_matrix.ravel()), similarity_matrix.shape)
        
        for det_idx, track_idx in zip(indices[0], indices[1]):
            if (det_idx not in used_detections and 
                track_idx not in used_tracks and 
                similarity_matrix[det_idx, track_idx] > 0.1):  # 最小相似度阈值
                
                matches.append((det_idx, track_idx))
                used_detections.add(det_idx)
                used_tracks.add(track_idx)
        
        return matches
    
    def _create_track(self, detection: Detection):
        """创建新轨迹"""
        track_id = self.next_track_id
        self.next_track_id += 1
        
        track = Track(
            track_id=track_id,
            class_name=detection.class_name
        )
        track.update(detection)
        
        self.tracks[track_id] = track
        detection.track_id = track_id
        
        self.total_tracks_created += 1
        self.logger.debug(f"Created new track {track_id} for {detection.class_name}")
    
    def _delete_old_tracks(self):
        """删除过期轨迹"""
        tracks_to_delete = []
        
        for track_id, track in self.tracks.items():
            # 删除条件：超过最大年龄且命中次数不足
            if (track.time_since_update > self.max_age or 
                (track.age > self.max_age and track.hits < self.min_hits)):
                tracks_to_delete.append(track_id)
        
        for track_id in tracks_to_delete:
            del self.tracks[track_id]
            self.total_tracks_deleted += 1
            self.logger.debug(f"Deleted track {track_id}")
    
    def get_active_tracks(self) -> List[Track]:
        """获取活跃轨迹"""
        return [track for track in self.tracks.values() 
                if track.is_active and track.hits >= self.min_hits]
    
    def get_track_by_id(self, track_id: int) -> Optional[Track]:
        """根据ID获取轨迹"""
        return self.tracks.get(track_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取跟踪统计信息"""
        active_tracks = self.get_active_tracks()
        
        return {
            "frame_count": self.frame_count,
            "total_tracks_created": self.total_tracks_created,
            "total_tracks_deleted": self.total_tracks_deleted,
            "active_tracks_count": len(active_tracks),
            "total_tracks_count": len(self.tracks),
            "avg_track_age": np.mean([t.age for t in active_tracks]) if active_tracks else 0,
            "avg_track_confidence": np.mean([t.avg_confidence for t in active_tracks]) if active_tracks else 0,
            "config": self.config
        }
    
    def reset(self):
        """重置跟踪器"""
        self.tracks.clear()
        self.next_track_id = 1
        self.frame_count = 0
        self.total_tracks_created = 0
        self.total_tracks_deleted = 0
        self.active_tracks_count = 0
        self.logger.info("Tracker reset")
