"""
YOLO检测器
基于YOLOv8的目标检测实现
"""
import logging
import time
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import cv2

from config import ai_config
from .detection import Detection, DetectionFilter, DetectionUtils

# 尝试导入YOLOv8
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    logging.warning("YOLOv8 not available, using mock detector")


class YOLODetector:
    """YOLO检测器类"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or ai_config.get_model_config()
        self.logger = logging.getLogger(__name__)
        
        # 模型相关
        self.model = None
        self.model_loaded = False
        self.device = self._get_device(self.config.get("device", "cpu"))
        
        # 检测参数
        self.confidence_threshold = self.config.get("confidence_threshold", 0.25)
        self.iou_threshold = self.config.get("iou_threshold", 0.45)
        self.max_detections = self.config.get("max_detections", 1000)
        
        # 类别映射
        self.class_names = self._get_class_names()
        self.target_classes = self._get_target_classes()
        
        # 过滤器
        self.filter = DetectionFilter(
            min_confidence=self.confidence_threshold,
            min_area=ai_config.MIN_OBJECT_SIZE ** 2,
            max_area=ai_config.MAX_OBJECT_SIZE ** 2,
            min_aspect_ratio=ai_config.MIN_ASPECT_RATIO,
            max_aspect_ratio=ai_config.MAX_ASPECT_RATIO,
            allowed_classes=list(self.target_classes.keys())
        )
        
        # 统计信息
        self.total_detections = 0
        self.total_inference_time = 0.0
        self.frame_count = 0

    def _get_device(self, device_config: str) -> str:
        """自动检测或验证设备"""
        if device_config == "auto":
            # 自动检测最佳设备
            try:
                import torch
                if torch.cuda.is_available():
                    return "cuda"
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    return "mps"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        else:
            # 验证指定设备是否可用
            if device_config == "cuda":
                try:
                    import torch
                    if torch.cuda.is_available():
                        return "cuda"
                    else:
                        self.logger.warning("CUDA not available, falling back to CPU")
                        return "cpu"
                except ImportError:
                    self.logger.warning("PyTorch not available, using CPU")
                    return "cpu"
            elif device_config == "mps":
                try:
                    import torch
                    if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                        return "mps"
                    else:
                        self.logger.warning("MPS not available, falling back to CPU")
                        return "cpu"
                except ImportError:
                    self.logger.warning("PyTorch not available, using CPU")
                    return "cpu"
            else:
                return "cpu"
    
    def load_model(self) -> bool:
        """加载YOLO模型"""
        try:
            if not YOLO_AVAILABLE:
                self.logger.warning("YOLO not available, using mock detector")
                self.model_loaded = True
                return True
            
            model_path = self.config.get("path", "yolov8s.pt")
            self.logger.info(f"Loading YOLO model: {model_path}")
            
            # 加载模型
            self.model = YOLO(model_path)
            
            # 设置设备
            if self.device != "cpu":
                self.model.to(self.device)
            
            # 预热模型
            dummy_input = np.zeros((640, 640, 3), dtype=np.uint8)
            self.model(dummy_input, verbose=False)
            
            self.model_loaded = True
            self.logger.info(f"YOLO model loaded successfully on {self.device}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load YOLO model: {e}")
            return False
    
    def detect(self, image: np.ndarray) -> List[Detection]:
        """检测图像中的目标"""
        if not self.model_loaded:
            if not self.load_model():
                return []
        
        try:
            start_time = time.time()
            
            if YOLO_AVAILABLE and self.model:
                detections = self._detect_with_yolo(image)
            else:
                detections = self._mock_detect(image)
            
            # 后处理
            detections = self._post_process(detections)
            
            # 更新统计信息
            inference_time = time.time() - start_time
            self.total_inference_time += inference_time
            self.frame_count += 1
            self.total_detections += len(detections)
            
            return detections
            
        except Exception as e:
            self.logger.error(f"Detection failed: {e}")
            return []
    
    def _detect_with_yolo(self, image: np.ndarray) -> List[Detection]:
        """使用YOLO模型进行检测"""
        # 运行推理
        results = self.model(
            image,
            conf=self.confidence_threshold,
            iou=self.iou_threshold,
            max_det=self.max_detections,
            verbose=False
        )
        
        detections = []
        
        # 解析结果
        for result in results:
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
                confidences = result.boxes.conf.cpu().numpy()
                class_ids = result.boxes.cls.cpu().numpy().astype(int)
                
                for i in range(len(boxes)):
                    bbox = tuple(boxes[i])
                    confidence = float(confidences[i])
                    class_id = int(class_ids[i])
                    class_name = self.class_names.get(class_id, f"class_{class_id}")
                    
                    # 只保留目标类别
                    if class_name in self.target_classes:
                        detection = Detection(
                            bbox=bbox,
                            confidence=confidence,
                            class_id=class_id,
                            class_name=class_name,
                            timestamp=time.time()
                        )
                        detections.append(detection)
        
        return detections
    
    def _mock_detect(self, image: np.ndarray) -> List[Detection]:
        """模拟检测（用于测试）"""
        height, width = image.shape[:2]
        detections = []
        
        # 模拟一些检测结果
        mock_detections = [
            {
                "bbox": (width * 0.1, height * 0.1, width * 0.3, height * 0.4),
                "confidence": 0.85,
                "class_name": "car"
            },
            {
                "bbox": (width * 0.5, height * 0.2, width * 0.7, height * 0.5),
                "confidence": 0.92,
                "class_name": "truck"
            },
            {
                "bbox": (width * 0.2, height * 0.6, width * 0.25, height * 0.8),
                "confidence": 0.78,
                "class_name": "person"
            }
        ]
        
        for i, mock_det in enumerate(mock_detections):
            if mock_det["class_name"] in self.target_classes:
                detection = Detection(
                    bbox=mock_det["bbox"],
                    confidence=mock_det["confidence"],
                    class_id=self.target_classes[mock_det["class_name"]],
                    class_name=mock_det["class_name"],
                    timestamp=time.time()
                )
                detections.append(detection)
        
        return detections
    
    def _post_process(self, detections: List[Detection]) -> List[Detection]:
        """后处理检测结果"""
        # 过滤检测结果
        detections = self.filter.filter(detections)
        
        # 非极大值抑制
        detections = DetectionUtils.non_max_suppression(
            detections, 
            iou_threshold=self.iou_threshold
        )
        
        # 按置信度排序
        detections.sort(key=lambda x: x.confidence, reverse=True)
        
        # 限制最大检测数量
        if len(detections) > self.max_detections:
            detections = detections[:self.max_detections]
        
        return detections
    
    def _get_class_names(self) -> Dict[int, str]:
        """获取类别名称映射"""
        # COCO数据集的类别名称
        coco_classes = {
            0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane',
            5: 'bus', 6: 'train', 7: 'truck', 8: 'boat', 9: 'traffic light',
            10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench',
            14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow',
            20: 'elephant', 21: 'bear', 22: 'zebra', 23: 'giraffe', 24: 'backpack',
            25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase', 29: 'frisbee',
            30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite',
            34: 'baseball bat', 35: 'baseball glove', 36: 'skateboard',
            37: 'surfboard', 38: 'tennis racket', 39: 'bottle', 40: 'wine glass',
            41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl',
            46: 'banana', 47: 'apple', 48: 'sandwich', 49: 'orange',
            50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza',
            54: 'donut', 55: 'cake', 56: 'chair', 57: 'couch',
            58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet',
            62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard',
            67: 'cell phone', 68: 'microwave', 69: 'oven', 70: 'toaster',
            71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock',
            75: 'vase', 76: 'scissors', 77: 'teddy bear', 78: 'hair drier',
            79: 'toothbrush'
        }
        
        return coco_classes
    
    def _get_target_classes(self) -> Dict[str, int]:
        """获取目标检测类别"""
        # 交通相关的目标类别
        target_classes = {}
        
        # 车辆类别
        vehicle_classes = ai_config.VEHICLE_CLASSES
        for class_name in vehicle_classes:
            for class_id, coco_name in self.class_names.items():
                if coco_name == class_name:
                    target_classes[class_name] = class_id
                    break
        
        # 行人类别
        person_classes = ai_config.PERSON_CLASSES
        for class_name in person_classes:
            for class_id, coco_name in self.class_names.items():
                if coco_name == class_name:
                    target_classes[class_name] = class_id
                    break
        
        # 交通标志类别
        traffic_classes = ai_config.TRAFFIC_SIGN_CLASSES
        for class_name in traffic_classes:
            for class_id, coco_name in self.class_names.items():
                if coco_name == class_name:
                    target_classes[class_name] = class_id
                    break
        
        return target_classes
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        avg_inference_time = (self.total_inference_time / self.frame_count 
                            if self.frame_count > 0 else 0)
        avg_detections = (self.total_detections / self.frame_count 
                        if self.frame_count > 0 else 0)
        
        return {
            "model_loaded": self.model_loaded,
            "device": self.device,
            "frame_count": self.frame_count,
            "total_detections": self.total_detections,
            "avg_detections_per_frame": avg_detections,
            "total_inference_time": self.total_inference_time,
            "avg_inference_time": avg_inference_time,
            "fps": 1.0 / avg_inference_time if avg_inference_time > 0 else 0,
            "target_classes": list(self.target_classes.keys()),
            "confidence_threshold": self.confidence_threshold,
            "iou_threshold": self.iou_threshold
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_detections = 0
        self.total_inference_time = 0.0
        self.frame_count = 0
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        
        # 更新检测参数
        self.confidence_threshold = self.config.get("confidence_threshold", 0.25)
        self.iou_threshold = self.config.get("iou_threshold", 0.45)
        self.max_detections = self.config.get("max_detections", 1000)
        
        # 更新过滤器
        self.filter.min_confidence = self.confidence_threshold
        
        self.logger.info("Detector configuration updated")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'model') and self.model:
            del self.model
