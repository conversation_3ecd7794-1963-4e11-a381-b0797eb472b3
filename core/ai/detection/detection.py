"""
检测结果数据类
定义检测结果的数据结构
"""
from dataclasses import dataclass
from typing import Tuple, Optional, Dict, Any
import numpy as np


@dataclass
class Detection:
    """检测结果类"""
    
    # 基础信息
    bbox: Tuple[float, float, float, float]  # (x1, y1, x2, y2)
    confidence: float
    class_id: int
    class_name: str
    
    # 计算属性
    center: Optional[Tuple[float, float]] = None
    area: Optional[float] = None
    
    # 扩展信息
    track_id: Optional[int] = None
    features: Optional[np.ndarray] = None
    timestamp: Optional[float] = None
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.center is None:
            self.center = self.get_center()
        
        if self.area is None:
            self.area = self.get_area()
        
        if self.metadata is None:
            self.metadata = {}
    
    def get_center(self) -> Tuple[float, float]:
        """获取边界框中心点"""
        x1, y1, x2, y2 = self.bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        return (center_x, center_y)
    
    def get_area(self) -> float:
        """获取边界框面积"""
        x1, y1, x2, y2 = self.bbox
        width = x2 - x1
        height = y2 - y1
        return width * height
    
    def get_width(self) -> float:
        """获取边界框宽度"""
        x1, y1, x2, y2 = self.bbox
        return x2 - x1
    
    def get_height(self) -> float:
        """获取边界框高度"""
        x1, y1, x2, y2 = self.bbox
        return y2 - y1
    
    def get_aspect_ratio(self) -> float:
        """获取宽高比"""
        width = self.get_width()
        height = self.get_height()
        return width / height if height > 0 else 0
    
    def iou(self, other: 'Detection') -> float:
        """计算与另一个检测结果的IoU"""
        x1_1, y1_1, x2_1, y2_1 = self.bbox
        x1_2, y1_2, x2_2, y2_2 = other.bbox
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = self.get_area()
        area2 = other.get_area()
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def distance_to(self, other: 'Detection') -> float:
        """计算与另一个检测结果中心点的距离"""
        x1, y1 = self.center
        x2, y2 = other.center
        return np.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2)
    
    def is_inside(self, other: 'Detection') -> bool:
        """检查是否在另一个检测结果内部"""
        x1_1, y1_1, x2_1, y2_1 = self.bbox
        x1_2, y1_2, x2_2, y2_2 = other.bbox
        
        return (x1_1 >= x1_2 and y1_1 >= y1_2 and 
                x2_1 <= x2_2 and y2_1 <= y2_2)
    
    def overlaps_with(self, other: 'Detection', threshold: float = 0.1) -> bool:
        """检查是否与另一个检测结果重叠"""
        return self.iou(other) > threshold
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "bbox": self.bbox,
            "confidence": self.confidence,
            "class_id": self.class_id,
            "class_name": self.class_name,
            "center": self.center,
            "area": self.area,
            "track_id": self.track_id,
            "timestamp": self.timestamp,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Detection':
        """从字典创建检测结果"""
        return cls(
            bbox=tuple(data["bbox"]),
            confidence=data["confidence"],
            class_id=data["class_id"],
            class_name=data["class_name"],
            center=tuple(data["center"]) if data.get("center") else None,
            area=data.get("area"),
            track_id=data.get("track_id"),
            timestamp=data.get("timestamp"),
            metadata=data.get("metadata", {})
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"Detection(class={self.class_name}, "
                f"conf={self.confidence:.3f}, "
                f"bbox={self.bbox}, "
                f"track_id={self.track_id})")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()


class DetectionFilter:
    """检测结果过滤器"""
    
    def __init__(self, 
                 min_confidence: float = 0.5,
                 min_area: float = 100,
                 max_area: float = 100000,
                 min_aspect_ratio: float = 0.1,
                 max_aspect_ratio: float = 10.0,
                 allowed_classes: Optional[list] = None):
        self.min_confidence = min_confidence
        self.min_area = min_area
        self.max_area = max_area
        self.min_aspect_ratio = min_aspect_ratio
        self.max_aspect_ratio = max_aspect_ratio
        self.allowed_classes = allowed_classes or []
    
    def filter(self, detections: list[Detection]) -> list[Detection]:
        """过滤检测结果"""
        filtered = []
        
        for detection in detections:
            # 置信度过滤
            if detection.confidence < self.min_confidence:
                continue
            
            # 面积过滤
            if detection.area < self.min_area or detection.area > self.max_area:
                continue
            
            # 宽高比过滤
            aspect_ratio = detection.get_aspect_ratio()
            if aspect_ratio < self.min_aspect_ratio or aspect_ratio > self.max_aspect_ratio:
                continue
            
            # 类别过滤
            if self.allowed_classes and detection.class_name not in self.allowed_classes:
                continue
            
            filtered.append(detection)
        
        return filtered


class DetectionUtils:
    """检测结果工具类"""
    
    @staticmethod
    def non_max_suppression(detections: list[Detection], 
                          iou_threshold: float = 0.5) -> list[Detection]:
        """非极大值抑制"""
        if not detections:
            return []
        
        # 按置信度排序
        detections = sorted(detections, key=lambda x: x.confidence, reverse=True)
        
        keep = []
        while detections:
            # 保留置信度最高的检测结果
            current = detections.pop(0)
            keep.append(current)
            
            # 移除与当前检测结果重叠度过高的其他检测结果
            detections = [det for det in detections 
                         if current.iou(det) <= iou_threshold]
        
        return keep
    
    @staticmethod
    def group_by_class(detections: list[Detection]) -> Dict[str, list[Detection]]:
        """按类别分组检测结果"""
        groups = {}
        for detection in detections:
            class_name = detection.class_name
            if class_name not in groups:
                groups[class_name] = []
            groups[class_name].append(detection)
        return groups
    
    @staticmethod
    def calculate_statistics(detections: list[Detection]) -> Dict[str, Any]:
        """计算检测结果统计信息"""
        if not detections:
            return {
                "total_count": 0,
                "class_counts": {},
                "avg_confidence": 0.0,
                "avg_area": 0.0
            }
        
        class_counts = {}
        total_confidence = 0.0
        total_area = 0.0
        
        for detection in detections:
            # 统计类别数量
            class_name = detection.class_name
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            # 累计置信度和面积
            total_confidence += detection.confidence
            total_area += detection.area
        
        return {
            "total_count": len(detections),
            "class_counts": class_counts,
            "avg_confidence": total_confidence / len(detections),
            "avg_area": total_area / len(detections)
        }
