"""
安全帽检测器
专门用于检测电动车骑行者是否佩戴安全帽
"""
import logging
import time
import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

from .detection import Detection
from .yolo_detector import YOLODetector

@dataclass
class HelmetDetection:
    """安全帽检测结果"""
    person_detection: Detection  # 人员检测结果
    vehicle_detection: Optional[Detection]  # 车辆检测结果（如果有）
    helmet_detected: bool  # 是否检测到安全帽
    helmet_confidence: float  # 安全帽检测置信度
    violation_type: str  # 违规类型
    risk_level: str  # 风险等级
    metadata: Dict[str, Any]  # 额外信息

class HelmetDetector:
    """安全帽检测器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 基础检测器
        self.base_detector = YOLODetector(config)
        
        # 安全帽检测参数
        self.helmet_confidence_threshold = self.config.get("helmet_confidence_threshold", 0.3)
        self.person_vehicle_distance_threshold = self.config.get("person_vehicle_distance_threshold", 100)
        self.motorcycle_classes = ["motorcycle", "bicycle"]  # 电动车相关类别
        
        # 头部区域检测参数
        self.head_region_ratio = self.config.get("head_region_ratio", 0.25)  # 头部占人体的比例
        
        # 违规统计
        self.violation_count = 0
        self.total_riders = 0
        
        # 加载安全帽检测模型（如果有专门的模型）
        self.helmet_model = None
        self._load_helmet_model()
    
    def _load_helmet_model(self):
        """加载专门的安全帽检测模型"""
        try:
            # 尝试加载专门的安全帽检测模型
            helmet_model_path = self.config.get("helmet_model_path")
            if helmet_model_path:
                from ultralytics import YOLO
                self.helmet_model = YOLO(helmet_model_path)
                self.logger.info(f"Loaded helmet detection model: {helmet_model_path}")
            else:
                self.logger.info("Using base YOLO model for helmet detection")
        except Exception as e:
            self.logger.warning(f"Failed to load helmet model: {e}")
            self.helmet_model = None
    
    def detect_helmet_violations(self, image: np.ndarray) -> List[HelmetDetection]:
        """检测安全帽违规"""
        # 1. 基础目标检测
        base_detections = self.base_detector.detect(image)
        
        # 2. 分离人员和车辆检测
        persons = [d for d in base_detections if d.class_name == "person"]
        vehicles = [d for d in base_detections if d.class_name in self.motorcycle_classes]
        
        # 3. 匹配骑行者
        riders = self._match_riders_with_vehicles(persons, vehicles)
        
        # 4. 检测安全帽
        helmet_detections = []
        for person, vehicle in riders:
            helmet_result = self._detect_helmet_on_person(image, person, vehicle)
            helmet_detections.append(helmet_result)
            
            # 更新统计
            self.total_riders += 1
            if not helmet_result.helmet_detected:
                self.violation_count += 1
        
        return helmet_detections
    
    def _match_riders_with_vehicles(self, persons: List[Detection], 
                                   vehicles: List[Detection]) -> List[Tuple[Detection, Optional[Detection]]]:
        """匹配骑行者与车辆"""
        riders = []
        used_vehicles = set()
        
        for person in persons:
            best_vehicle = None
            min_distance = float('inf')
            
            # 寻找最近的车辆
            for i, vehicle in enumerate(vehicles):
                if i in used_vehicles:
                    continue
                
                distance = person.distance_to(vehicle)
                
                # 检查是否在合理距离内
                if distance < self.person_vehicle_distance_threshold and distance < min_distance:
                    # 检查人员是否在车辆上方或附近
                    if self._is_person_on_vehicle(person, vehicle):
                        best_vehicle = vehicle
                        min_distance = distance
            
            if best_vehicle:
                used_vehicles.add(vehicles.index(best_vehicle))
                riders.append((person, best_vehicle))
            else:
                # 即使没有匹配到车辆，也检查是否是潜在的骑行者
                if self._is_potential_rider(person):
                    riders.append((person, None))
        
        return riders
    
    def _is_person_on_vehicle(self, person: Detection, vehicle: Detection) -> bool:
        """判断人员是否在车辆上"""
        person_center_x, person_center_y = person.center
        vehicle_x1, vehicle_y1, vehicle_x2, vehicle_y2 = vehicle.bbox
        
        # 检查人员中心是否在车辆的水平范围内
        if vehicle_x1 <= person_center_x <= vehicle_x2:
            # 检查人员是否在车辆上方
            if person_center_y <= vehicle_y2:
                return True
        
        return False
    
    def _is_potential_rider(self, person: Detection) -> bool:
        """判断是否是潜在的骑行者（基于姿态等特征）"""
        # 简单的启发式规则
        aspect_ratio = person.get_aspect_ratio()
        
        # 骑行者通常宽高比较小（更高）
        if 0.3 <= aspect_ratio <= 0.8:
            return True
        
        return False
    
    def _detect_helmet_on_person(self, image: np.ndarray, person: Detection, 
                                vehicle: Optional[Detection]) -> HelmetDetection:
        """检测人员是否佩戴安全帽"""
        # 提取头部区域
        head_region = self._extract_head_region(image, person)
        
        # 检测安全帽
        helmet_detected, helmet_confidence = self._detect_helmet_in_region(head_region)
        
        # 确定违规类型和风险等级
        violation_type, risk_level = self._assess_violation(
            helmet_detected, helmet_confidence, person, vehicle
        )
        
        # 构建检测结果
        return HelmetDetection(
            person_detection=person,
            vehicle_detection=vehicle,
            helmet_detected=helmet_detected,
            helmet_confidence=helmet_confidence,
            violation_type=violation_type,
            risk_level=risk_level,
            metadata={
                "head_region_extracted": head_region is not None,
                "vehicle_type": vehicle.class_name if vehicle else "unknown",
                "detection_method": "helmet_model" if self.helmet_model else "color_analysis",
                "timestamp": time.time()
            }
        )
    
    def _extract_head_region(self, image: np.ndarray, person: Detection) -> Optional[np.ndarray]:
        """提取人员头部区域"""
        try:
            x1, y1, x2, y2 = person.bbox
            person_height = y2 - y1
            
            # 计算头部区域（人体上部的25%）
            head_height = person_height * self.head_region_ratio
            head_y1 = max(0, int(y1))
            head_y2 = min(image.shape[0], int(y1 + head_height))
            head_x1 = max(0, int(x1))
            head_x2 = min(image.shape[1], int(x2))
            
            # 提取头部区域
            head_region = image[head_y1:head_y2, head_x1:head_x2]
            
            return head_region if head_region.size > 0 else None
            
        except Exception as e:
            self.logger.error(f"Failed to extract head region: {e}")
            return None
    
    def _detect_helmet_in_region(self, head_region: Optional[np.ndarray]) -> Tuple[bool, float]:
        """在头部区域检测安全帽"""
        if head_region is None:
            return False, 0.0
        
        try:
            if self.helmet_model:
                # 使用专门的安全帽检测模型
                return self._detect_helmet_with_model(head_region)
            else:
                # 使用颜色和形状分析
                return self._detect_helmet_with_analysis(head_region)
                
        except Exception as e:
            self.logger.error(f"Helmet detection failed: {e}")
            return False, 0.0
    
    def _detect_helmet_with_model(self, head_region: np.ndarray) -> Tuple[bool, float]:
        """使用专门模型检测安全帽"""
        try:
            results = self.helmet_model(head_region, verbose=False)
            
            max_confidence = 0.0
            helmet_detected = False
            
            for result in results:
                if result.boxes is not None:
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)
                    
                    for conf, class_id in zip(confidences, class_ids):
                        # 假设class_id=0是安全帽，class_id=1是无安全帽
                        if class_id == 0 and conf > max_confidence:
                            max_confidence = float(conf)
                            helmet_detected = True
            
            return helmet_detected and max_confidence > self.helmet_confidence_threshold, max_confidence
            
        except Exception as e:
            self.logger.error(f"Model-based helmet detection failed: {e}")
            return False, 0.0
    
    def _detect_helmet_with_analysis(self, head_region: np.ndarray) -> Tuple[bool, float]:
        """使用颜色和形状分析检测安全帽"""
        try:
            # 转换到HSV颜色空间
            hsv = cv2.cvtColor(head_region, cv2.COLOR_BGR2HSV)
            
            # 定义安全帽常见颜色范围（白色、黄色、红色、蓝色）
            helmet_color_ranges = [
                # 白色
                (np.array([0, 0, 200]), np.array([180, 30, 255])),
                # 黄色
                (np.array([20, 100, 100]), np.array([30, 255, 255])),
                # 红色
                (np.array([0, 100, 100]), np.array([10, 255, 255])),
                (np.array([170, 100, 100]), np.array([180, 255, 255])),
                # 蓝色
                (np.array([100, 100, 100]), np.array([130, 255, 255]))
            ]
            
            total_pixels = head_region.shape[0] * head_region.shape[1]
            max_helmet_ratio = 0.0
            
            for lower, upper in helmet_color_ranges:
                mask = cv2.inRange(hsv, lower, upper)
                helmet_pixels = cv2.countNonZero(mask)
                helmet_ratio = helmet_pixels / total_pixels
                max_helmet_ratio = max(max_helmet_ratio, helmet_ratio)
            
            # 形状分析：检测圆形或椭圆形区域
            gray = cv2.cvtColor(head_region, cv2.COLOR_BGR2GRAY)
            circles = cv2.HoughCircles(
                gray, cv2.HOUGH_GRADIENT, 1, 20,
                param1=50, param2=30, minRadius=5, maxRadius=50
            )
            
            shape_confidence = 0.0
            if circles is not None:
                shape_confidence = min(len(circles[0]) * 0.3, 1.0)
            
            # 综合评分
            confidence = (max_helmet_ratio * 0.7 + shape_confidence * 0.3)
            helmet_detected = confidence > 0.3  # 降低阈值，因为这是启发式方法
            
            return helmet_detected, confidence
            
        except Exception as e:
            self.logger.error(f"Analysis-based helmet detection failed: {e}")
            return False, 0.0
    
    def _assess_violation(self, helmet_detected: bool, helmet_confidence: float,
                         person: Detection, vehicle: Optional[Detection]) -> Tuple[str, str]:
        """评估违规类型和风险等级"""
        if helmet_detected:
            return "compliant", "low"
        
        # 确定违规类型
        if vehicle:
            if vehicle.class_name == "motorcycle":
                violation_type = "motorcycle_no_helmet"
            elif vehicle.class_name == "bicycle":
                violation_type = "bicycle_no_helmet"
            else:
                violation_type = "vehicle_rider_no_helmet"
        else:
            violation_type = "potential_rider_no_helmet"
        
        # 确定风险等级
        if vehicle and vehicle.class_name == "motorcycle":
            risk_level = "high"  # 摩托车不戴头盔风险最高
        elif vehicle and vehicle.class_name == "bicycle":
            risk_level = "medium"  # 自行车不戴头盔中等风险
        else:
            risk_level = "medium"  # 其他情况中等风险
        
        return violation_type, risk_level
    
    def get_violation_statistics(self) -> Dict[str, Any]:
        """获取违规统计信息"""
        violation_rate = (self.violation_count / self.total_riders * 100 
                         if self.total_riders > 0 else 0)
        
        return {
            "total_riders": self.total_riders,
            "violation_count": self.violation_count,
            "violation_rate": violation_rate,
            "compliance_rate": 100 - violation_rate,
            "helmet_confidence_threshold": self.helmet_confidence_threshold
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.violation_count = 0
        self.total_riders = 0
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        self.helmet_confidence_threshold = self.config.get("helmet_confidence_threshold", 0.3)
        self.person_vehicle_distance_threshold = self.config.get("person_vehicle_distance_threshold", 100)
        self.head_region_ratio = self.config.get("head_region_ratio", 0.25)
        
        # 更新基础检测器配置
        self.base_detector.update_config(new_config)
        
        self.logger.info("Helmet detector configuration updated")
