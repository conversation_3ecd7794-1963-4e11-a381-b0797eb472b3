"""
系统管理器
统一管理所有核心组件
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .ai.ai_engine import AIEngine
from .business.business_engine import BusinessEngine
from .hardware.hardware_manager import HardwareManager
from .data.data_manager import DataManager
from .llm.llm_engine import LLMEngine


class SystemManager:
    """系统管理器 - 统一管理所有核心组件"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.ai_engine: Optional[AIEngine] = None
        self.business_engine: Optional[BusinessEngine] = None
        self.hardware_manager: Optional[HardwareManager] = None
        self.data_manager: Optional[DataManager] = None
        self.llm_engine: Optional[LLMEngine] = None
        
        # 系统状态
        self.is_initialized = False
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 统计信息
        self.stats = {
            "total_frames": 0,
            "total_detections": 0,
            "total_tracks": 0,
            "total_violations": 0,
            "total_accidents": 0
        }
    
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            self.logger.info("开始初始化系统...")
            
            # 初始化数据管理器
            self.logger.info("初始化数据管理器...")
            self.data_manager = DataManager()
            await self.data_manager.initialize()
            
            # 初始化硬件管理器
            self.logger.info("初始化硬件管理器...")
            self.hardware_manager = HardwareManager()
            await self.hardware_manager.initialize()
            
            # 初始化AI引擎
            self.logger.info("初始化AI引擎...")
            self.ai_engine = AIEngine()
            await self.ai_engine.initialize()
            
            # 初始化业务引擎
            self.logger.info("初始化业务引擎...")
            self.business_engine = BusinessEngine()
            await self.business_engine.initialize()
            
            # 初始化LLM引擎
            self.logger.info("初始化LLM引擎...")
            self.llm_engine = LLMEngine()
            await self.llm_engine.initialize()
            
            self.is_initialized = True
            self.logger.info("✅ 系统初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}", exc_info=True)
            return False
    
    async def start(self) -> bool:
        """启动系统"""
        try:
            if not self.is_initialized:
                self.logger.error("系统未初始化，无法启动")
                return False
            
            self.logger.info("启动系统...")
            self.start_time = datetime.utcnow()
            
            # 启动各个组件
            if self.hardware_manager:
                await self.hardware_manager.start()
            
            if self.ai_engine:
                await self.ai_engine.start()
            
            if self.business_engine:
                await self.business_engine.start()
            
            if self.llm_engine:
                await self.llm_engine.start()
            
            self.is_running = True
            self.logger.info("✅ 系统启动完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}", exc_info=True)
            return False
    
    async def stop(self) -> bool:
        """停止系统"""
        try:
            self.logger.info("停止系统...")
            
            # 停止各个组件
            if self.llm_engine:
                await self.llm_engine.stop()
            
            if self.business_engine:
                await self.business_engine.stop()
            
            if self.ai_engine:
                await self.ai_engine.stop()
            
            if self.hardware_manager:
                await self.hardware_manager.stop()
            
            if self.data_manager:
                await self.data_manager.stop()
            
            self.is_running = False
            self.logger.info("✅ 系统停止完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统停止失败: {e}", exc_info=True)
            return False
    
    async def shutdown(self) -> bool:
        """关闭系统"""
        try:
            self.logger.info("关闭系统...")
            
            # 先停止系统
            await self.stop()
            
            # 清理资源
            self.ai_engine = None
            self.business_engine = None
            self.hardware_manager = None
            self.data_manager = None
            self.llm_engine = None
            
            self.is_initialized = False
            self.logger.info("✅ 系统关闭完成")
            return True
            
        except Exception as e:
            self.logger.error(f"系统关闭失败: {e}", exc_info=True)
            return False
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            uptime = None
            if self.start_time:
                uptime = (datetime.utcnow() - self.start_time).total_seconds()
            
            status = {
                "initialized": self.is_initialized,
                "running": self.is_running,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "uptime_seconds": uptime,
                "components": {
                    "ai_engine": self.ai_engine is not None,
                    "business_engine": self.business_engine is not None,
                    "hardware_manager": self.hardware_manager is not None,
                    "data_manager": self.data_manager is not None,
                    "llm_engine": self.llm_engine is not None
                },
                "stats": self.stats.copy()
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            info = {
                "name": "无人机交通警察系统",
                "version": "1.0.0",
                "description": "基于AI的智能交通监控系统",
                "components": [
                    "AI引擎 - 目标检测与跟踪",
                    "业务引擎 - 交通分析与决策",
                    "硬件管理 - 无人机与摄像头",
                    "数据管理 - 存储与缓存",
                    "LLM引擎 - 智能报告生成"
                ],
                "capabilities": [
                    "实时目标检测",
                    "多目标跟踪",
                    "交通流分析",
                    "事故检测",
                    "违法识别",
                    "智能报告",
                    "语音播报"
                ]
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {"error": str(e)}
    
    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            health = {
                "overall": "healthy",
                "components": {},
                "issues": []
            }
            
            # 检查各组件健康状态
            if self.ai_engine:
                try:
                    ai_health = await self.ai_engine.get_health_status()
                    health["components"]["ai_engine"] = ai_health
                except:
                    health["components"]["ai_engine"] = "error"
                    health["issues"].append("AI引擎健康检查失败")
            
            if self.business_engine:
                try:
                    business_health = await self.business_engine.get_health_status()
                    health["components"]["business_engine"] = business_health
                except:
                    health["components"]["business_engine"] = "error"
                    health["issues"].append("业务引擎健康检查失败")
            
            # 根据问题数量确定整体健康状态
            if len(health["issues"]) > 0:
                health["overall"] = "warning" if len(health["issues"]) < 3 else "critical"
            
            return health
            
        except Exception as e:
            self.logger.error(f"获取健康状态失败: {e}")
            return {"overall": "error", "error": str(e)}
    
    async def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        try:
            stats = {
                "system": self.stats.copy(),
                "performance": {
                    "uptime": None,
                    "memory_usage": "N/A",
                    "cpu_usage": "N/A"
                },
                "components": {}
            }
            
            # 计算运行时间
            if self.start_time:
                uptime = (datetime.utcnow() - self.start_time).total_seconds()
                stats["performance"]["uptime"] = uptime
            
            # 获取各组件统计
            if self.ai_engine:
                try:
                    ai_stats = await self.ai_engine.get_statistics()
                    stats["components"]["ai_engine"] = ai_stats
                except:
                    stats["components"]["ai_engine"] = "error"
            
            if self.business_engine:
                try:
                    business_stats = await self.business_engine.get_statistics()
                    stats["components"]["business_engine"] = business_stats
                except:
                    stats["components"]["business_engine"] = "error"
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def update_stats(self, **kwargs):
        """更新统计信息"""
        for key, value in kwargs.items():
            if key in self.stats:
                self.stats[key] += value
