"""
数据模型定义
定义数据库表结构和数据模型
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any

Base = declarative_base()


class TrafficSession(Base):
    """交通监控会话"""
    __tablename__ = "traffic_sessions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), unique=True, nullable=False)
    start_time = Column(DateTime, default=datetime.utcnow)
    end_time = Column(DateTime)
    status = Column(String(20), default="active")  # active, stopped, error
    config = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    detections = relationship("Detection", back_populates="session")
    tracks = relationship("Track", back_populates="session")
    accidents = relationship("Accident", back_populates="session")
    violations = relationship("Violation", back_populates="session")


class Detection(Base):
    """目标检测记录"""
    __tablename__ = "detections"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    frame_id = Column(Integer)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 检测结果
    bbox_x1 = Column(Float)
    bbox_y1 = Column(Float)
    bbox_x2 = Column(Float)
    bbox_y2 = Column(Float)
    confidence = Column(Float)
    class_id = Column(Integer)
    class_name = Column(String(50))
    
    # 扩展信息
    track_id = Column(Integer)
    features = Column(Text)  # JSON格式的特征向量
    extra_data = Column(JSON)  # 重命名为extra_data避免与SQLAlchemy保留字段冲突
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    session = relationship("TrafficSession", back_populates="detections")


class Track(Base):
    """目标跟踪记录"""
    __tablename__ = "tracks"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    track_id = Column(Integer)
    class_name = Column(String(50))
    
    # 轨迹信息
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    duration = Column(Float)  # 秒
    total_frames = Column(Integer)
    
    # 统计信息
    avg_confidence = Column(Float)
    max_confidence = Column(Float)
    avg_speed = Column(Float)
    max_speed = Column(Float)
    total_distance = Column(Float)
    
    # 轨迹数据
    trajectory = Column(Text)  # JSON格式的轨迹点
    velocity_history = Column(Text)  # JSON格式的速度历史
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    last_seen = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    session = relationship("TrafficSession", back_populates="tracks")


class Accident(Base):
    """事故记录"""
    __tablename__ = "accidents"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    accident_id = Column(String(50), unique=True)
    
    # 事故基本信息
    accident_type = Column(String(50))  # collision, rollover, stationary, etc.
    severity = Column(String(20))  # minor, moderate, serious, fatal
    confidence = Column(Float)
    timestamp = Column(DateTime)
    
    # 位置信息
    location_x = Column(Float)
    location_y = Column(Float)
    
    # 涉及对象
    involved_tracks = Column(Text)  # JSON格式的轨迹ID列表
    vehicle_count = Column(Integer, default=0)
    person_count = Column(Integer, default=0)
    
    # 详细信息
    description = Column(Text)
    evidence = Column(JSON)
    
    # 处理状态
    status = Column(String(20), default="detected")  # detected, confirmed, resolved
    response_time = Column(Float)  # 响应时间（秒）
    resolution_time = Column(Float)  # 处理时间（秒）
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    session = relationship("TrafficSession", back_populates="accidents")


class Violation(Base):
    """违法记录"""
    __tablename__ = "violations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    violation_id = Column(String(50), unique=True)
    
    # 违法基本信息
    violation_type = Column(String(50))  # speeding, wrong_direction, etc.
    severity = Column(String(20))  # minor, moderate, serious
    confidence = Column(Float)
    timestamp = Column(DateTime)
    
    # 位置信息
    location_x = Column(Float)
    location_y = Column(Float)
    
    # 涉及对象
    track_id = Column(Integer)
    vehicle_type = Column(String(50))
    
    # 违法详情
    description = Column(Text)
    evidence = Column(JSON)
    
    # 法律信息
    law_article = Column(String(100))  # 法条
    penalty_points = Column(Integer)  # 扣分
    fine_amount = Column(Float)  # 罚款金额
    
    # 处理状态
    status = Column(String(20), default="detected")  # detected, processed, appealed
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    session = relationship("TrafficSession", back_populates="violations")


class TrafficMetrics(Base):
    """交通指标记录"""
    __tablename__ = "traffic_metrics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 基础指标
    vehicle_count = Column(Integer)
    person_count = Column(Integer)
    vehicle_density = Column(Float)  # 车辆/m²
    person_density = Column(Float)   # 人/m²
    
    # 速度指标
    avg_vehicle_speed = Column(Float)
    avg_person_speed = Column(Float)
    max_vehicle_speed = Column(Float)
    min_vehicle_speed = Column(Float)
    
    # 流量指标
    flow_rate = Column(Float)  # 车辆/秒
    occupancy_rate = Column(Float)  # 占有率%
    congestion_level = Column(String(20))  # free_flow, light, moderate, heavy, severe
    
    # 分类统计
    car_count = Column(Integer, default=0)
    truck_count = Column(Integer, default=0)
    bus_count = Column(Integer, default=0)
    motorcycle_count = Column(Integer, default=0)
    bicycle_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=datetime.utcnow)


class SafetyAlert(Base):
    """安全警报记录"""
    __tablename__ = "safety_alerts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    alert_id = Column(String(50), unique=True)
    
    # 警报信息
    alert_type = Column(String(50))
    risk_level = Column(String(20))  # low, moderate, high, critical
    timestamp = Column(DateTime)
    
    # 位置信息
    location_x = Column(Float)
    location_y = Column(Float)
    
    # 警报内容
    title = Column(String(200))
    description = Column(Text)
    recommendations = Column(Text)  # JSON格式的建议列表
    
    # 涉及对象
    affected_tracks = Column(Text)  # JSON格式的轨迹ID列表
    evidence = Column(JSON)
    
    # 处理状态
    status = Column(String(20), default="active")  # active, acknowledged, resolved
    acknowledged_at = Column(DateTime)
    resolved_at = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class Decision(Base):
    """决策记录"""
    __tablename__ = "decisions"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    decision_id = Column(String(50), unique=True)
    
    # 决策信息
    action_type = Column(String(50))  # traffic_control, emergency_response, etc.
    priority = Column(String(20))  # low, medium, high, urgent
    timestamp = Column(DateTime)
    
    # 决策内容
    title = Column(String(200))
    description = Column(Text)
    recommendations = Column(Text)  # JSON格式的建议列表
    
    # 目标信息
    target_location_x = Column(Float)
    target_location_y = Column(Float)
    affected_tracks = Column(Text)  # JSON格式的轨迹ID列表
    
    # 执行信息
    estimated_impact = Column(Text)
    execution_time = Column(Integer)  # 预计执行时间（秒）
    
    # 状态信息
    status = Column(String(20), default="pending")  # pending, executing, completed, cancelled
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SystemLog(Base):
    """系统日志"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50))
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 日志信息
    level = Column(String(10))  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    module = Column(String(50))
    message = Column(Text)
    
    # 扩展信息
    exception = Column(Text)
    stack_trace = Column(Text)
    context = Column(JSON)
    
    created_at = Column(DateTime, default=datetime.utcnow)


class PerformanceMetrics(Base):
    """性能指标记录"""
    __tablename__ = "performance_metrics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey("traffic_sessions.session_id"))
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 处理性能
    frame_count = Column(Integer)
    fps = Column(Float)
    processing_time = Column(Float)  # 秒
    
    # AI性能
    detection_time = Column(Float)
    tracking_time = Column(Float)
    analysis_time = Column(Float)
    
    # 系统性能
    cpu_usage = Column(Float)  # %
    memory_usage = Column(Float)  # %
    gpu_usage = Column(Float)  # %
    disk_usage = Column(Float)  # %
    
    # 网络性能
    network_latency = Column(Float)  # ms
    bandwidth_usage = Column(Float)  # Mbps
    
    created_at = Column(DateTime, default=datetime.utcnow)


class Configuration(Base):
    """配置记录"""
    __tablename__ = "configurations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    config_key = Column(String(100), unique=True)
    config_value = Column(Text)
    config_type = Column(String(20))  # string, integer, float, boolean, json
    description = Column(Text)
    
    # 版本控制
    version = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# 数据模型工具函数
def to_dict(model_instance) -> Dict[str, Any]:
    """将模型实例转换为字典"""
    result = {}
    for column in model_instance.__table__.columns:
        value = getattr(model_instance, column.name)
        if isinstance(value, datetime):
            result[column.name] = value.isoformat()
        else:
            result[column.name] = value
    return result


def from_dict(model_class, data: Dict[str, Any]):
    """从字典创建模型实例"""
    # 过滤掉不存在的字段
    valid_fields = {column.name for column in model_class.__table__.columns}
    filtered_data = {k: v for k, v in data.items() if k in valid_fields}
    
    # 处理datetime字段
    for column in model_class.__table__.columns:
        if column.name in filtered_data and column.type.python_type == datetime:
            value = filtered_data[column.name]
            if isinstance(value, str):
                try:
                    filtered_data[column.name] = datetime.fromisoformat(value)
                except ValueError:
                    # 如果解析失败，使用当前时间
                    filtered_data[column.name] = datetime.utcnow()
    
    return model_class(**filtered_data)
