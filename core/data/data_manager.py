"""
数据管理器
整合数据库和缓存管理，提供统一的数据访问接口
"""
import logging
import asyncio
import json
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime, timedelta

from config import database_config
from .database import DatabaseManager
from .cache_manager import CacheManager
from .models import (
    TrafficSession, Detection, Track, Accident, Violation,
    TrafficMetrics, SafetyAlert, Decision, SystemLog, 
    PerformanceMetrics, Configuration
)


class DataManager:
    """数据管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.database = DatabaseManager(self.config.get("database", {}))
        self.cache = CacheManager(self.config.get("cache", {}))
        
        # 状态管理
        self.initialized = False
        self.current_session_id: Optional[str] = None
        
        # 缓存配置
        self.cache_enabled = self.config.get("cache_enabled", True)
        self.cache_ttl = self.config.get("cache_ttl", 3600)
        
        # 统计信息
        self.total_operations = 0
        self.cache_hits = 0
        self.cache_misses = 0
    
    async def initialize(self) -> bool:
        """初始化数据管理器"""
        try:
            self.logger.info("Initializing data manager...")
            
            # 初始化数据库
            db_success = await self.database.initialize()
            if not db_success:
                self.logger.error("Failed to initialize database")
                return False
            
            # 初始化缓存
            cache_success = await self.cache.initialize()
            if not cache_success:
                self.logger.warning("Failed to initialize cache, continuing without cache")
                self.cache_enabled = False
            
            self.initialized = True
            self.logger.info("Data manager initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize data manager: {e}")
            return False
    
    async def create_session(self, config: Optional[Dict] = None) -> str:
        """创建新的交通监控会话"""
        try:
            session_id = f"session_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            session = TrafficSession(
                session_id=session_id,
                start_time=datetime.utcnow(),
                status="active",
                config=config or {}
            )
            
            success = await self.database.insert(session)
            if success:
                self.current_session_id = session_id
                self.logger.info(f"Created new session: {session_id}")
                return session_id
            else:
                raise Exception("Failed to insert session")
                
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            raise
    
    async def end_session(self, session_id: Optional[str] = None) -> bool:
        """结束交通监控会话"""
        try:
            session_id = session_id or self.current_session_id
            if not session_id:
                return False
            
            success = await self.database.update(
                TrafficSession,
                {"session_id": session_id},
                {
                    "end_time": datetime.utcnow(),
                    "status": "stopped"
                }
            )
            
            if success and session_id == self.current_session_id:
                self.current_session_id = None
            
            return success > 0
            
        except Exception as e:
            self.logger.error(f"Failed to end session: {e}")
            return False
    
    async def save_detections(self, detections: List[Dict[str, Any]]) -> bool:
        """保存检测结果"""
        try:
            if not self.current_session_id:
                self.logger.warning("No active session for saving detections")
                return False
            
            detection_objects = []
            for detection_data in detections:
                detection = Detection(
                    session_id=self.current_session_id,
                    frame_id=detection_data.get("frame_id"),
                    timestamp=datetime.utcnow(),
                    bbox_x1=detection_data["bbox"][0],
                    bbox_y1=detection_data["bbox"][1],
                    bbox_x2=detection_data["bbox"][2],
                    bbox_y2=detection_data["bbox"][3],
                    confidence=detection_data["confidence"],
                    class_id=detection_data["class_id"],
                    class_name=detection_data["class_name"],
                    track_id=detection_data.get("track_id"),
                    metadata=detection_data.get("metadata", {})
                )
                detection_objects.append(detection)
            
            success = await self.database.insert_many(detection_objects)
            self.total_operations += 1
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to save detections: {e}")
            return False
    
    async def save_tracks(self, tracks: List[Dict[str, Any]]) -> bool:
        """保存跟踪结果"""
        try:
            if not self.current_session_id:
                return False
            
            track_objects = []
            for track_data in tracks:
                # 检查轨迹是否已存在
                existing = await self.database.query_one(
                    Track,
                    {
                        "session_id": self.current_session_id,
                        "track_id": track_data["track_id"]
                    }
                )
                
                if existing:
                    # 更新现有轨迹
                    await self.database.update(
                        Track,
                        {
                            "session_id": self.current_session_id,
                            "track_id": track_data["track_id"]
                        },
                        {
                            "end_time": datetime.utcnow(),
                            "avg_confidence": track_data.get("avg_confidence"),
                            "avg_speed": track_data.get("speed"),
                            "trajectory": json.dumps(track_data.get("trajectory", [])),
                            "is_active": track_data.get("is_active", True),
                            "last_seen": datetime.utcnow()
                        }
                    )
                else:
                    # 创建新轨迹
                    track = Track(
                        session_id=self.current_session_id,
                        track_id=track_data["track_id"],
                        class_name=track_data["class_name"],
                        start_time=datetime.utcnow(),
                        avg_confidence=track_data.get("avg_confidence"),
                        avg_speed=track_data.get("speed"),
                        trajectory=json.dumps(track_data.get("trajectory", [])),
                        is_active=track_data.get("is_active", True),
                        last_seen=datetime.utcnow()
                    )
                    track_objects.append(track)
            
            if track_objects:
                success = await self.database.insert_many(track_objects)
            else:
                success = True
            
            self.total_operations += 1
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to save tracks: {e}")
            return False
    
    async def save_accident(self, accident_data: Dict[str, Any]) -> bool:
        """保存事故记录"""
        try:
            if not self.current_session_id:
                return False
            
            accident = Accident(
                session_id=self.current_session_id,
                accident_id=accident_data["accident_id"],
                accident_type=accident_data["accident_type"],
                severity=accident_data["severity"],
                confidence=accident_data["confidence"],
                timestamp=datetime.fromisoformat(accident_data["timestamp"]) if isinstance(accident_data["timestamp"], str) else accident_data["timestamp"],
                location_x=accident_data["location"][0],
                location_y=accident_data["location"][1],
                involved_tracks=json.dumps(accident_data["involved_tracks"]),
                description=accident_data["description"],
                evidence=accident_data.get("evidence", {})
            )
            
            success = await self.database.insert(accident)
            
            # 缓存最近事故
            if success and self.cache_enabled:
                await self.cache.set(
                    f"recent_accidents:{accident_data['accident_id']}",
                    accident_data,
                    ttl=3600
                )
            
            self.total_operations += 1
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to save accident: {e}")
            return False
    
    async def save_violation(self, violation_data: Dict[str, Any]) -> bool:
        """保存违法记录"""
        try:
            if not self.current_session_id:
                return False
            
            violation = Violation(
                session_id=self.current_session_id,
                violation_id=violation_data["violation_id"],
                violation_type=violation_data["violation_type"],
                severity=violation_data["severity"],
                confidence=violation_data["confidence"],
                timestamp=datetime.fromisoformat(violation_data["timestamp"]) if isinstance(violation_data["timestamp"], str) else violation_data["timestamp"],
                location_x=violation_data["location"][0],
                location_y=violation_data["location"][1],
                track_id=violation_data["track_id"],
                vehicle_type=violation_data["vehicle_type"],
                description=violation_data["description"],
                evidence=violation_data.get("evidence", {})
            )
            
            success = await self.database.insert(violation)
            
            # 缓存最近违法
            if success and self.cache_enabled:
                await self.cache.set(
                    f"recent_violations:{violation_data['violation_id']}",
                    violation_data,
                    ttl=3600
                )
            
            self.total_operations += 1
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to save violation: {e}")
            return False
    
    async def save_traffic_metrics(self, metrics_data: Dict[str, Any]) -> bool:
        """保存交通指标"""
        try:
            if not self.current_session_id:
                return False
            
            metrics = TrafficMetrics(
                session_id=self.current_session_id,
                timestamp=datetime.fromisoformat(metrics_data["timestamp"]) if isinstance(metrics_data["timestamp"], str) else metrics_data["timestamp"],
                vehicle_count=metrics_data["vehicle_count"],
                person_count=metrics_data["person_count"],
                vehicle_density=metrics_data["vehicle_density"],
                person_density=metrics_data["person_density"],
                avg_vehicle_speed=metrics_data["avg_vehicle_speed"],
                avg_person_speed=metrics_data["avg_person_speed"],
                flow_rate=metrics_data["flow_rate"],
                occupancy_rate=metrics_data["occupancy_rate"],
                congestion_level=metrics_data["congestion_level"]
            )
            
            success = await self.database.insert(metrics)
            
            # 缓存最新指标
            if success and self.cache_enabled:
                await self.cache.set(
                    f"latest_metrics:{self.current_session_id}",
                    metrics_data,
                    ttl=300  # 5分钟
                )
            
            self.total_operations += 1
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to save traffic metrics: {e}")
            return False
    
    async def get_recent_accidents(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的事故记录"""
        try:
            cache_key = f"recent_accidents_{hours}h"
            
            # 尝试从缓存获取
            if self.cache_enabled:
                cached_data = await self.cache.get(cache_key)
                if cached_data:
                    self.cache_hits += 1
                    return cached_data
                self.cache_misses += 1
            
            # 从数据库查询
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            accidents = await self.database.query(
                Accident,
                filters={"timestamp": (">=", cutoff_time)},
                order_by="timestamp",
                limit=100
            )
            
            # 缓存结果
            if self.cache_enabled:
                await self.cache.set(cache_key, accidents, ttl=300)
            
            self.total_operations += 1
            return accidents
            
        except Exception as e:
            self.logger.error(f"Failed to get recent accidents: {e}")
            return []
    
    async def get_recent_violations(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的违法记录"""
        try:
            cache_key = f"recent_violations_{hours}h"
            
            # 尝试从缓存获取
            if self.cache_enabled:
                cached_data = await self.cache.get(cache_key)
                if cached_data:
                    self.cache_hits += 1
                    return cached_data
                self.cache_misses += 1
            
            # 从数据库查询
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            violations = await self.database.query(
                Violation,
                filters={"timestamp": (">=", cutoff_time)},
                order_by="timestamp",
                limit=100
            )
            
            # 缓存结果
            if self.cache_enabled:
                await self.cache.set(cache_key, violations, ttl=300)
            
            self.total_operations += 1
            return violations
            
        except Exception as e:
            self.logger.error(f"Failed to get recent violations: {e}")
            return []
    
    async def get_traffic_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取交通统计信息"""
        try:
            cache_key = f"traffic_stats_{hours}h"
            
            # 尝试从缓存获取
            if self.cache_enabled:
                cached_data = await self.cache.get(cache_key)
                if cached_data:
                    self.cache_hits += 1
                    return cached_data
                self.cache_misses += 1
            
            # 从数据库查询统计信息
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # 获取基础统计
            stats = {}
            
            # 事故统计
            accident_count = await self.database.count(
                Accident,
                {"timestamp": (">=", cutoff_time)}
            )
            stats["accident_count"] = accident_count
            
            # 违法统计
            violation_count = await self.database.count(
                Violation,
                {"timestamp": (">=", cutoff_time)}
            )
            stats["violation_count"] = violation_count
            
            # 检测统计
            detection_count = await self.database.count(
                Detection,
                {"timestamp": (">=", cutoff_time)}
            )
            stats["detection_count"] = detection_count
            
            # 活跃轨迹统计
            active_tracks = await self.database.count(
                Track,
                {"is_active": True}
            )
            stats["active_tracks"] = active_tracks
            
            # 缓存结果
            if self.cache_enabled:
                await self.cache.set(cache_key, stats, ttl=600)  # 10分钟
            
            self.total_operations += 1
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get traffic statistics: {e}")
            return {}
    
    async def cleanup_old_data(self, retention_days: int = 30) -> int:
        """清理旧数据"""
        try:
            deleted_count = await self.database.cleanup_old_data(retention_days)
            
            # 清理相关缓存
            if self.cache_enabled:
                await self.cache.clear("recent_*")
                await self.cache.clear("traffic_stats_*")
            
            self.logger.info(f"Cleaned up {deleted_count} old records")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {e}")
            return 0
    
    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        try:
            # 数据库统计
            db_stats = await self.database.get_database_stats()
            
            # 缓存统计
            cache_stats = await self.cache.get_stats()
            
            # 数据管理器统计
            manager_stats = {
                "total_operations": self.total_operations,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "cache_hit_rate": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "current_session_id": self.current_session_id,
                "initialized": self.initialized
            }
            
            return {
                "data_manager": manager_stats,
                "database": db_stats,
                "cache": cache_stats
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get comprehensive stats: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 数据库健康检查
            db_health = await self.database.health_check()
            
            # 缓存健康检查
            cache_health = await self.cache.health_check()
            
            # 整体状态
            overall_status = "healthy"
            if db_health.get("status") != "healthy":
                overall_status = "unhealthy"
            elif cache_health.get("status") not in ["healthy", "disabled"]:
                overall_status = "degraded"
            
            return {
                "status": overall_status,
                "database": db_health,
                "cache": cache_health,
                "initialized": self.initialized
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "initialized": self.initialized
            }
    
    async def close(self):
        """关闭数据管理器"""
        try:
            await self.database.close()
            await self.cache.close()
            self.initialized = False
            self.logger.info("Data manager closed")
        except Exception as e:
            self.logger.error(f"Error closing data manager: {e}")
