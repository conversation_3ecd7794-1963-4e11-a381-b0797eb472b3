"""
数据库管理器
负责数据库连接、操作和维护
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any, Type, Union
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import json

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool

from config import database_config
from .models import Base, to_dict, from_dict


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or database_config.get_database_config()
        self.logger = logging.getLogger(__name__)
        
        # 数据库连接
        self.engine = None
        self.SessionLocal = None
        self.connected = False
        
        # 连接池配置
        self.pool_size = self.config.get("pool_size", 10)
        self.max_overflow = self.config.get("max_overflow", 20)
        self.pool_timeout = self.config.get("pool_timeout", 30)
        self.pool_recycle = self.config.get("pool_recycle", 3600)
        
        # 统计信息
        self.total_queries = 0
        self.total_inserts = 0
        self.total_updates = 0
        self.total_deletes = 0
        self.connection_errors = 0
    
    async def initialize(self) -> bool:
        """初始化数据库连接"""
        try:
            self.logger.info("Initializing database connection...")
            
            # 创建数据库引擎
            database_url = self.config.get("url")
            if not database_url:
                raise ValueError("Database URL not configured")
            
            self.engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_timeout=self.pool_timeout,
                pool_recycle=self.pool_recycle,
                echo=self.config.get("enable_logging", False)
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 测试连接
            await self._test_connection()
            
            # 创建表
            await self._create_tables()
            
            self.connected = True
            self.logger.info("Database initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            self.connection_errors += 1
            return False
    
    async def _test_connection(self):
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            self.logger.info("Database connection test successful")
        except Exception as e:
            raise Exception(f"Database connection test failed: {e}")
    
    async def _create_tables(self):
        """创建数据库表"""
        try:
            # 在线程池中执行
            await asyncio.to_thread(Base.metadata.create_all, self.engine)
            self.logger.info("Database tables created/verified")
        except Exception as e:
            raise Exception(f"Failed to create tables: {e}")
    
    @asynccontextmanager
    async def get_session(self):
        """获取数据库会话（异步上下文管理器）"""
        if not self.connected:
            raise RuntimeError("Database not connected")
        
        session = self.SessionLocal()
        try:
            yield session
            await asyncio.to_thread(session.commit)
        except Exception as e:
            await asyncio.to_thread(session.rollback)
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            await asyncio.to_thread(session.close)
    
    async def insert(self, model_instance) -> bool:
        """插入数据"""
        try:
            async with self.get_session() as session:
                await asyncio.to_thread(session.add, model_instance)
                self.total_inserts += 1
                return True
        except Exception as e:
            self.logger.error(f"Failed to insert data: {e}")
            return False
    
    async def insert_many(self, model_instances: List) -> bool:
        """批量插入数据"""
        try:
            async with self.get_session() as session:
                await asyncio.to_thread(session.add_all, model_instances)
                self.total_inserts += len(model_instances)
                return True
        except Exception as e:
            self.logger.error(f"Failed to insert batch data: {e}")
            return False
    
    async def update(self, model_class: Type, filters: Dict, updates: Dict) -> int:
        """更新数据"""
        try:
            async with self.get_session() as session:
                query = session.query(model_class)
                
                # 应用过滤条件
                for key, value in filters.items():
                    if hasattr(model_class, key):
                        query = query.filter(getattr(model_class, key) == value)
                
                # 执行更新
                count = await asyncio.to_thread(query.update, updates)
                self.total_updates += count
                return count
        except Exception as e:
            self.logger.error(f"Failed to update data: {e}")
            return 0
    
    async def delete(self, model_class: Type, filters: Dict) -> int:
        """删除数据"""
        try:
            async with self.get_session() as session:
                query = session.query(model_class)
                
                # 应用过滤条件
                for key, value in filters.items():
                    if hasattr(model_class, key):
                        query = query.filter(getattr(model_class, key) == value)
                
                # 执行删除
                count = await asyncio.to_thread(query.delete)
                self.total_deletes += count
                return count
        except Exception as e:
            self.logger.error(f"Failed to delete data: {e}")
            return 0
    
    async def query(self, model_class: Type, 
                   filters: Optional[Dict] = None,
                   limit: Optional[int] = None,
                   offset: Optional[int] = None,
                   order_by: Optional[str] = None) -> List[Dict]:
        """查询数据"""
        try:
            async with self.get_session() as session:
                query = session.query(model_class)
                
                # 应用过滤条件
                if filters:
                    for key, value in filters.items():
                        if hasattr(model_class, key):
                            query = query.filter(getattr(model_class, key) == value)
                
                # 排序
                if order_by and hasattr(model_class, order_by):
                    query = query.order_by(getattr(model_class, order_by))
                
                # 分页
                if offset:
                    query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                # 执行查询
                results = await asyncio.to_thread(query.all)
                self.total_queries += 1
                
                # 转换为字典列表
                return [to_dict(result) for result in results]
                
        except Exception as e:
            self.logger.error(f"Failed to query data: {e}")
            return []
    
    async def query_one(self, model_class: Type, filters: Dict) -> Optional[Dict]:
        """查询单条数据"""
        try:
            async with self.get_session() as session:
                query = session.query(model_class)
                
                # 应用过滤条件
                for key, value in filters.items():
                    if hasattr(model_class, key):
                        query = query.filter(getattr(model_class, key) == value)
                
                # 执行查询
                result = await asyncio.to_thread(query.first)
                self.total_queries += 1
                
                return to_dict(result) if result else None
                
        except Exception as e:
            self.logger.error(f"Failed to query single data: {e}")
            return None
    
    async def count(self, model_class: Type, filters: Optional[Dict] = None) -> int:
        """统计数据数量"""
        try:
            async with self.get_session() as session:
                query = session.query(model_class)
                
                # 应用过滤条件
                if filters:
                    for key, value in filters.items():
                        if hasattr(model_class, key):
                            query = query.filter(getattr(model_class, key) == value)
                
                # 执行统计
                count = await asyncio.to_thread(query.count)
                self.total_queries += 1
                return count
                
        except Exception as e:
            self.logger.error(f"Failed to count data: {e}")
            return 0
    
    async def execute_raw_sql(self, sql: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行原生SQL"""
        try:
            async with self.get_session() as session:
                result = await asyncio.to_thread(
                    session.execute, 
                    text(sql), 
                    params or {}
                )
                
                # 获取结果
                rows = await asyncio.to_thread(result.fetchall)
                columns = result.keys()
                
                # 转换为字典列表
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            self.logger.error(f"Failed to execute raw SQL: {e}")
            return []
    
    async def cleanup_old_data(self, retention_days: int = 30):
        """清理旧数据"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            # 导入模型类
            from .models import (
                Detection, Track, Accident, Violation, 
                TrafficMetrics, SafetyAlert, Decision, 
                SystemLog, PerformanceMetrics
            )
            
            # 清理各类数据
            models_to_clean = [
                Detection, TrafficMetrics, PerformanceMetrics, SystemLog
            ]
            
            total_deleted = 0
            for model_class in models_to_clean:
                count = await self.delete(
                    model_class, 
                    {"created_at": ("<=", cutoff_date)}
                )
                total_deleted += count
                self.logger.info(f"Deleted {count} old records from {model_class.__tablename__}")
            
            self.logger.info(f"Cleanup completed, deleted {total_deleted} old records")
            return total_deleted
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {e}")
            return 0
    
    async def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            # 这里实现数据库备份逻辑
            # 具体实现取决于数据库类型
            self.logger.info(f"Database backup to {backup_path} - not implemented")
            return True
        except Exception as e:
            self.logger.error(f"Failed to backup database: {e}")
            return False
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            from .models import (
                TrafficSession, Detection, Track, Accident, 
                Violation, TrafficMetrics, SafetyAlert, Decision
            )
            
            stats = {}
            
            # 统计各表记录数
            tables = [
                ("sessions", TrafficSession),
                ("detections", Detection),
                ("tracks", Track),
                ("accidents", Accident),
                ("violations", Violation),
                ("traffic_metrics", TrafficMetrics),
                ("safety_alerts", SafetyAlert),
                ("decisions", Decision)
            ]
            
            for table_name, model_class in tables:
                count = await self.count(model_class)
                stats[f"{table_name}_count"] = count
            
            # 添加操作统计
            stats.update({
                "total_queries": self.total_queries,
                "total_inserts": self.total_inserts,
                "total_updates": self.total_updates,
                "total_deletes": self.total_deletes,
                "connection_errors": self.connection_errors,
                "connected": self.connected
            })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}
    
    async def optimize_database(self):
        """优化数据库"""
        try:
            # 执行数据库优化操作
            if "sqlite" in self.config.get("url", "").lower():
                await self.execute_raw_sql("VACUUM")
                await self.execute_raw_sql("ANALYZE")
            elif "postgresql" in self.config.get("url", "").lower():
                await self.execute_raw_sql("VACUUM ANALYZE")
            
            self.logger.info("Database optimization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to optimize database: {e}")
    
    async def close(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                await asyncio.to_thread(self.engine.dispose)
                self.engine = None
                self.SessionLocal = None
                self.connected = False
                self.logger.info("Database connection closed")
        except Exception as e:
            self.logger.error(f"Error closing database: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self.connected
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.connected:
                return {"status": "disconnected", "error": "Not connected"}
            
            # 测试查询
            start_time = datetime.utcnow()
            await self.execute_raw_sql("SELECT 1")
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "response_time": response_time,
                "connected": self.connected,
                "pool_size": self.pool_size,
                "total_queries": self.total_queries
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connected": False
            }
