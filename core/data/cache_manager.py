"""
缓存管理器
负责数据缓存、过期管理和性能优化
"""
import logging
import asyncio
import json
import time
import pickle
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from collections import OrderedDict

# 尝试导入Redis
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available, using memory cache only")

from config import database_config


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict = OrderedDict()
        self.expiry_times: Dict[str, float] = {}
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            # 检查是否过期
            if key in self.expiry_times:
                if time.time() > self.expiry_times[key]:
                    await self._remove_key(key)
                    return None
            
            if key in self.cache:
                # 移动到末尾（LRU）
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        async with self._lock:
            # 如果缓存已满，删除最旧的项
            if len(self.cache) >= self.max_size and key not in self.cache:
                oldest_key = next(iter(self.cache))
                await self._remove_key(oldest_key)
            
            self.cache[key] = value
            
            # 设置过期时间
            ttl = ttl or self.default_ttl
            self.expiry_times[key] = time.time() + ttl
            
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        async with self._lock:
            return await self._remove_key(key)
    
    async def _remove_key(self, key: str) -> bool:
        """内部删除键方法"""
        removed = False
        if key in self.cache:
            del self.cache[key]
            removed = True
        if key in self.expiry_times:
            del self.expiry_times[key]
        return removed
    
    async def clear(self):
        """清空缓存"""
        async with self._lock:
            self.cache.clear()
            self.expiry_times.clear()
    
    async def keys(self) -> List[str]:
        """获取所有键"""
        async with self._lock:
            # 清理过期键
            current_time = time.time()
            expired_keys = [
                key for key, expiry in self.expiry_times.items()
                if current_time > expiry
            ]
            for key in expired_keys:
                await self._remove_key(key)
            
            return list(self.cache.keys())
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or database_config.get_cache_config()
        self.logger = logging.getLogger(__name__)
        
        # 缓存配置
        self.cache_type = self.config.get("type", "memory")
        self.enabled = self.config.get("enabled", True)
        self.default_ttl = self.config.get("ttl", 3600)
        self.max_memory_size = self.config.get("max_memory_size", 1000)
        
        # 缓存实例
        self.redis_client = None
        self.memory_cache = MemoryCache(self.max_memory_size, self.default_ttl)
        
        # 统计信息
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.errors = 0
        
        # 缓存键前缀
        self.key_prefix = "air_traffic_police:"
    
    async def initialize(self) -> bool:
        """初始化缓存管理器"""
        try:
            if not self.enabled:
                self.logger.info("Cache disabled")
                return True
            
            if self.cache_type == "redis" and REDIS_AVAILABLE:
                await self._initialize_redis()
            
            self.logger.info(f"Cache manager initialized with {self.cache_type} backend")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize cache manager: {e}")
            self.errors += 1
            return False
    
    async def _initialize_redis(self):
        """初始化Redis连接"""
        try:
            redis_config = {
                "host": self.config.get("host", "localhost"),
                "port": self.config.get("port", 6379),
                "db": self.config.get("db", 0),
                "password": self.config.get("password"),
                "decode_responses": True,
                "socket_timeout": self.config.get("timeout", 5),
                "socket_connect_timeout": self.config.get("connect_timeout", 5),
                "retry_on_timeout": True,
                "max_connections": self.config.get("max_connections", 10)
            }
            
            # 移除None值
            redis_config = {k: v for k, v in redis_config.items() if v is not None}
            
            self.redis_client = redis.Redis(**redis_config)
            
            # 测试连接
            await self.redis_client.ping()
            self.logger.info("Redis connection established")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
            # 回退到内存缓存
            self.cache_type = "memory"
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.enabled:
            return None
        
        try:
            cache_key = self._make_key(key)
            
            if self.cache_type == "redis" and self.redis_client:
                value = await self.redis_client.get(cache_key)
                if value is not None:
                    self.hits += 1
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        # 尝试pickle反序列化
                        try:
                            return pickle.loads(value.encode('latin1'))
                        except:
                            return value
                else:
                    self.misses += 1
                    return None
            else:
                # 使用内存缓存
                value = await self.memory_cache.get(cache_key)
                if value is not None:
                    self.hits += 1
                    return value
                else:
                    self.misses += 1
                    return None
                    
        except Exception as e:
            self.logger.error(f"Cache get error for key {key}: {e}")
            self.errors += 1
            self.misses += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.enabled:
            return False
        
        try:
            cache_key = self._make_key(key)
            ttl = ttl or self.default_ttl
            
            if self.cache_type == "redis" and self.redis_client:
                # 序列化值
                try:
                    serialized_value = json.dumps(value)
                except (TypeError, ValueError):
                    # JSON序列化失败，使用pickle
                    serialized_value = pickle.dumps(value).decode('latin1')
                
                await self.redis_client.setex(cache_key, ttl, serialized_value)
            else:
                # 使用内存缓存
                await self.memory_cache.set(cache_key, value, ttl)
            
            self.sets += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Cache set error for key {key}: {e}")
            self.errors += 1
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self.enabled:
            return False
        
        try:
            cache_key = self._make_key(key)
            
            if self.cache_type == "redis" and self.redis_client:
                result = await self.redis_client.delete(cache_key)
                success = result > 0
            else:
                success = await self.memory_cache.delete(cache_key)
            
            if success:
                self.deletes += 1
            
            return success
            
        except Exception as e:
            self.logger.error(f"Cache delete error for key {key}: {e}")
            self.errors += 1
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self.enabled:
            return False
        
        try:
            cache_key = self._make_key(key)
            
            if self.cache_type == "redis" and self.redis_client:
                return await self.redis_client.exists(cache_key) > 0
            else:
                value = await self.memory_cache.get(cache_key)
                return value is not None
                
        except Exception as e:
            self.logger.error(f"Cache exists error for key {key}: {e}")
            self.errors += 1
            return False
    
    async def clear(self, pattern: Optional[str] = None):
        """清空缓存"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                if pattern:
                    # 删除匹配模式的键
                    pattern_key = self._make_key(pattern)
                    keys = await self.redis_client.keys(pattern_key)
                    if keys:
                        await self.redis_client.delete(*keys)
                else:
                    # 删除所有带前缀的键
                    keys = await self.redis_client.keys(f"{self.key_prefix}*")
                    if keys:
                        await self.redis_client.delete(*keys)
            else:
                await self.memory_cache.clear()
            
            self.logger.info("Cache cleared")
            
        except Exception as e:
            self.logger.error(f"Cache clear error: {e}")
            self.errors += 1
    
    async def get_keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的键"""
        try:
            pattern_key = self._make_key(pattern)
            
            if self.cache_type == "redis" and self.redis_client:
                keys = await self.redis_client.keys(pattern_key)
                # 移除前缀
                return [key.replace(self.key_prefix, "") for key in keys]
            else:
                all_keys = await self.memory_cache.keys()
                # 简单的模式匹配
                if pattern == "*":
                    return [key.replace(self.key_prefix, "") for key in all_keys]
                else:
                    # 简化的通配符匹配
                    import fnmatch
                    return [
                        key.replace(self.key_prefix, "") 
                        for key in all_keys 
                        if fnmatch.fnmatch(key, pattern_key)
                    ]
                    
        except Exception as e:
            self.logger.error(f"Cache get_keys error: {e}")
            self.errors += 1
            return []
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """递增计数器"""
        try:
            cache_key = self._make_key(key)
            
            if self.cache_type == "redis" and self.redis_client:
                return await self.redis_client.incrby(cache_key, amount)
            else:
                # 内存缓存的简单实现
                current = await self.memory_cache.get(cache_key) or 0
                new_value = current + amount
                await self.memory_cache.set(cache_key, new_value)
                return new_value
                
        except Exception as e:
            self.logger.error(f"Cache increment error for key {key}: {e}")
            self.errors += 1
            return None
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置键的过期时间"""
        try:
            cache_key = self._make_key(key)
            
            if self.cache_type == "redis" and self.redis_client:
                return await self.redis_client.expire(cache_key, ttl)
            else:
                # 内存缓存需要重新设置值
                value = await self.memory_cache.get(cache_key)
                if value is not None:
                    return await self.memory_cache.set(cache_key, value, ttl)
                return False
                
        except Exception as e:
            self.logger.error(f"Cache expire error for key {key}: {e}")
            self.errors += 1
            return False
    
    def get_hit_rate(self) -> float:
        """获取缓存命中率"""
        total_requests = self.hits + self.misses
        return self.hits / total_requests if total_requests > 0 else 0.0
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            "cache_type": self.cache_type,
            "enabled": self.enabled,
            "hits": self.hits,
            "misses": self.misses,
            "sets": self.sets,
            "deletes": self.deletes,
            "errors": self.errors,
            "hit_rate": self.get_hit_rate(),
            "memory_cache_size": self.memory_cache.size()
        }
        
        # Redis特定统计
        if self.cache_type == "redis" and self.redis_client:
            try:
                redis_info = await self.redis_client.info()
                stats.update({
                    "redis_connected": True,
                    "redis_memory_used": redis_info.get("used_memory_human"),
                    "redis_connected_clients": redis_info.get("connected_clients"),
                    "redis_total_commands": redis_info.get("total_commands_processed")
                })
            except:
                stats["redis_connected"] = False
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.enabled:
                return {"status": "disabled"}
            
            # 测试缓存操作
            test_key = "health_check"
            test_value = {"timestamp": time.time()}
            
            # 设置测试值
            set_success = await self.set(test_key, test_value, 60)
            if not set_success:
                return {"status": "unhealthy", "error": "Failed to set test value"}
            
            # 获取测试值
            retrieved_value = await self.get(test_key)
            if retrieved_value != test_value:
                return {"status": "unhealthy", "error": "Failed to retrieve test value"}
            
            # 删除测试值
            await self.delete(test_key)
            
            return {
                "status": "healthy",
                "cache_type": self.cache_type,
                "hit_rate": self.get_hit_rate()
            }
            
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    async def close(self):
        """关闭缓存连接"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                self.redis_client = None
            
            await self.memory_cache.clear()
            self.logger.info("Cache manager closed")
            
        except Exception as e:
            self.logger.error(f"Error closing cache manager: {e}")
    
    def reset_stats(self):
        """重置统计信息"""
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.errors = 0
