"""
提示词模板
定义各种场景下的提示词模板
"""
from typing import Dict, Any
from enum import Enum


class PromptType(str, Enum):
    """提示词类型枚举"""
    ACCIDENT_REPORT = "accident_report"
    VIOLATION_REPORT = "violation_report"
    TRAFFIC_ANALYSIS = "traffic_analysis"
    SAFETY_ALERT = "safety_alert"
    DECISION_SUMMARY = "decision_summary"
    VOICE_ANNOUNCEMENT = "voice_announcement"


class PromptTemplates:
    """提示词模板类"""
    
    # 事故报告模板
    ACCIDENT_REPORT_TEMPLATE = """
你是一名专业的交通事故分析专家。请根据以下信息生成一份详细的事故报告：

事故基本信息：
- 事故ID：{accident_id}
- 事故类型：{accident_type}
- 严重程度：{severity}
- 发生时间：{timestamp}
- 发生地点：{location}
- 涉及轨迹：{involved_tracks}
- 置信度：{confidence}

事故描述：{description}

证据信息：{evidence}

请生成一份包含以下内容的专业事故报告：
1. 事故概述
2. 事故原因分析
3. 影响评估
4. 处理建议
5. 预防措施

报告应该客观、准确、专业，适合提交给交通管理部门。
"""

    # 违法行为报告模板
    VIOLATION_REPORT_TEMPLATE = """
你是一名交通执法专家。请根据以下违法行为信息生成执法报告：

违法基本信息：
- 违法ID：{violation_id}
- 违法类型：{violation_type}
- 严重程度：{severity}
- 发生时间：{timestamp}
- 发生地点：{location}
- 涉及车辆：{track_id}
- 车辆类型：{vehicle_type}
- 置信度：{confidence}

违法描述：{description}

证据信息：{evidence}

请生成一份包含以下内容的执法报告：
1. 违法行为概述
2. 违法事实认定
3. 法律依据
4. 处罚建议
5. 教育提醒

报告应该严谨、准确，符合执法规范。
"""

    # 交通分析报告模板
    TRAFFIC_ANALYSIS_TEMPLATE = """
你是一名交通流分析专家。请根据以下交通数据生成分析报告：

交通指标：
- 车辆数量：{vehicle_count}
- 行人数量：{person_count}
- 车辆密度：{vehicle_density} 车辆/平方米
- 平均车速：{avg_vehicle_speed} 米/秒
- 拥堵等级：{congestion_level}
- 流量：{flow_rate} 车辆/秒
- 占有率：{occupancy_rate}%

时间段：{timestamp}

请生成一份包含以下内容的交通分析报告：
1. 交通状况概述
2. 关键指标分析
3. 拥堵原因分析
4. 改善建议
5. 趋势预测

报告应该数据详实、分析深入、建议可行。
"""

    # 安全警报模板
    SAFETY_ALERT_TEMPLATE = """
你是一名交通安全专家。请根据以下安全警报信息生成警报通知：

警报信息：
- 警报ID：{alert_id}
- 警报类型：{alert_type}
- 风险等级：{risk_level}
- 发生时间：{timestamp}
- 位置：{location}
- 涉及轨迹：{affected_tracks}

警报描述：{description}

建议措施：{recommendations}

证据信息：{evidence}

请生成一份包含以下内容的安全警报：
1. 警报概要
2. 风险分析
3. 紧急程度评估
4. 应对措施
5. 后续监控要求

警报应该简洁明了、重点突出、便于快速响应。
"""

    # 决策总结模板
    DECISION_SUMMARY_TEMPLATE = """
你是一名交通管理决策专家。请根据以下决策信息生成决策总结：

决策信息：
- 决策ID：{decision_id}
- 行动类型：{action_type}
- 优先级：{priority}
- 决策时间：{timestamp}
- 目标位置：{target_location}
- 涉及轨迹：{affected_tracks}

决策标题：{title}
决策描述：{description}
建议措施：{recommendations}
预期影响：{estimated_impact}
执行时间：{execution_time}秒

请生成一份包含以下内容的决策总结：
1. 决策背景
2. 决策依据
3. 实施方案
4. 预期效果
5. 风险评估

总结应该逻辑清晰、可操作性强。
"""

    # 语音播报模板
    VOICE_ANNOUNCEMENT_TEMPLATE = """
你是一名交通广播员。请根据以下信息生成适合语音播报的内容：

播报类型：{announcement_type}
目标对象：{target_audience}
紧急程度：{urgency_level}

具体信息：{content}

位置信息：{location}

请生成一段适合语音播报的内容，要求：
1. 语言简洁明了
2. 语调适中，不引起恐慌
3. 信息准确完整
4. 便于理解和执行
5. 长度控制在30秒以内

播报内容应该专业、友善、有效。
"""

    @classmethod
    def get_template(cls, prompt_type: PromptType) -> str:
        """获取指定类型的模板"""
        template_map = {
            PromptType.ACCIDENT_REPORT: cls.ACCIDENT_REPORT_TEMPLATE,
            PromptType.VIOLATION_REPORT: cls.VIOLATION_REPORT_TEMPLATE,
            PromptType.TRAFFIC_ANALYSIS: cls.TRAFFIC_ANALYSIS_TEMPLATE,
            PromptType.SAFETY_ALERT: cls.SAFETY_ALERT_TEMPLATE,
            PromptType.DECISION_SUMMARY: cls.DECISION_SUMMARY_TEMPLATE,
            PromptType.VOICE_ANNOUNCEMENT: cls.VOICE_ANNOUNCEMENT_TEMPLATE
        }
        return template_map.get(prompt_type, "")
    
    @classmethod
    def format_template(cls, prompt_type: PromptType, **kwargs) -> str:
        """格式化模板"""
        template = cls.get_template(prompt_type)
        try:
            return template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for template {prompt_type}: {e}")
    
    @classmethod
    def get_voice_templates(cls) -> Dict[str, str]:
        """获取语音播报模板"""
        return {
            "traffic_congestion": "前方道路出现拥堵，当前车速较慢，请耐心等待，注意保持安全车距。",
            "accident_warning": "前方发生交通事故，请减速慢行，注意避让救援车辆。",
            "speed_violation": "您的车速过快，请减速行驶，注意交通安全。",
            "wrong_direction": "检测到逆向行驶，请立即调整行驶方向，确保安全。",
            "no_helmet": "骑行人员请注意，检测到您未佩戴安全头盔，请立即佩戴以确保安全。",
            "illegal_parking": "您的车辆涉嫌违法停车，请尽快驶离。",
            "lane_violation": "请注意车道行驶，避免压线或随意变道。",
            "emergency_response": "紧急情况，请所有车辆立即靠边停车，为救援车辆让行。",
            "weather_warning": "当前天气条件不佳，请减速慢行，注意行车安全。",
            "general_safety": "请遵守交通规则，安全文明出行。"
        }
    
    @classmethod
    def get_report_templates(cls) -> Dict[str, str]:
        """获取报告模板"""
        return {
            "daily_summary": """
# 交通管理日报

## 基本统计
- 监控时间：{date}
- 总车辆数：{total_vehicles}
- 总行人数：{total_persons}
- 平均拥堵等级：{avg_congestion}

## 事故情况
- 事故总数：{accident_count}
- 轻微事故：{minor_accidents}
- 严重事故：{serious_accidents}

## 违法情况
- 违法总数：{violation_count}
- 超速违法：{speeding_violations}
- 其他违法：{other_violations}

## 处理措施
{actions_taken}

## 建议改进
{recommendations}
""",
            
            "incident_report": """
# 交通事件报告

## 事件概述
- 事件类型：{incident_type}
- 发生时间：{timestamp}
- 发生地点：{location}
- 严重程度：{severity}

## 详细描述
{description}

## 处理过程
{handling_process}

## 影响评估
{impact_assessment}

## 经验教训
{lessons_learned}
"""
        }
