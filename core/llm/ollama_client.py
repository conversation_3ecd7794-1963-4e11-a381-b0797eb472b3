#!/usr/bin/env python3
"""
Ollama 客户端
支持本地运行的各种开源大模型
"""

import json
import asyncio
import aiohttp
import requests
from typing import Dict, List, Any, Optional, AsyncGenerator
import logging

logger = logging.getLogger(__name__)

class OllamaClient:
    """Ollama客户端类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get("base_url", "http://localhost:11434")
        self.timeout = config.get("timeout", 60)
        self.max_retries = config.get("max_retries", 3)
        self.models = config.get("models", {})
        self.recommended = config.get("recommended", {})
        
    async def check_connection(self) -> bool:
        """检查Ollama服务连接"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/version",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        version_info = await response.json()
                        logger.info(f"Ollama连接成功，版本: {version_info.get('version', 'unknown')}")
                        return True
                    else:
                        logger.error(f"Ollama连接失败，状态码: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"Ollama连接异常: {e}")
            return False
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """获取已安装的模型列表"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = data.get("models", [])
                        logger.info(f"发现 {len(models)} 个已安装的模型")
                        return models
                    else:
                        logger.error(f"获取模型列表失败，状态码: {response.status}")
                        return []
        except Exception as e:
            logger.error(f"获取模型列表异常: {e}")
            return []
    
    async def pull_model(self, model_name: str) -> bool:
        """下载模型"""
        try:
            logger.info(f"开始下载模型: {model_name}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/pull",
                    json={"name": model_name},
                    timeout=aiohttp.ClientTimeout(total=3600)  # 1小时超时
                ) as response:
                    if response.status == 200:
                        async for line in response.content:
                            if line:
                                try:
                                    data = json.loads(line.decode())
                                    status = data.get("status", "")
                                    if "completed" in status.lower():
                                        logger.info(f"模型 {model_name} 下载完成")
                                        return True
                                    elif "error" in data:
                                        logger.error(f"模型下载错误: {data['error']}")
                                        return False
                                except json.JSONDecodeError:
                                    continue
                    else:
                        logger.error(f"模型下载失败，状态码: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"模型下载异常: {e}")
            return False
    
    async def generate_response(self, 
                              model_name: str, 
                              prompt: str, 
                              **kwargs) -> str:
        """生成响应"""
        try:
            # 获取模型配置
            model_config = self.models.get(model_name, {})
            
            # 构建请求参数
            request_data = {
                "model": model_config.get("model_name", model_name),
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
                    "top_p": kwargs.get("top_p", model_config.get("top_p", 0.9)),
                    "top_k": kwargs.get("top_k", model_config.get("top_k", 40)),
                    "repeat_penalty": kwargs.get("repeat_penalty", model_config.get("repeat_penalty", 1.1)),
                    "num_ctx": kwargs.get("context_length", model_config.get("context_length", 2048))
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("response", "")
                    else:
                        error_text = await response.text()
                        logger.error(f"生成响应失败，状态码: {response.status}, 错误: {error_text}")
                        return "抱歉，生成响应时出现错误。"
                        
        except Exception as e:
            logger.error(f"生成响应异常: {e}")
            return "抱歉，生成响应时出现异常。"
    
    async def generate_stream(self, 
                            model_name: str, 
                            prompt: str, 
                            **kwargs) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        try:
            # 获取模型配置
            model_config = self.models.get(model_name, {})
            
            # 构建请求参数
            request_data = {
                "model": model_config.get("model_name", model_name),
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
                    "top_p": kwargs.get("top_p", model_config.get("top_p", 0.9)),
                    "top_k": kwargs.get("top_k", model_config.get("top_k", 40)),
                    "repeat_penalty": kwargs.get("repeat_penalty", model_config.get("repeat_penalty", 1.1)),
                    "num_ctx": kwargs.get("context_length", model_config.get("context_length", 2048))
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status == 200:
                        async for line in response.content:
                            if line:
                                try:
                                    data = json.loads(line.decode())
                                    if "response" in data:
                                        yield data["response"]
                                    if data.get("done", False):
                                        break
                                except json.JSONDecodeError:
                                    continue
                    else:
                        error_text = await response.text()
                        logger.error(f"流式生成失败，状态码: {response.status}, 错误: {error_text}")
                        yield "抱歉，生成响应时出现错误。"
                        
        except Exception as e:
            logger.error(f"流式生成异常: {e}")
            yield "抱歉，生成响应时出现异常。"
    
    async def chat(self, 
                   model_name: str, 
                   messages: List[Dict[str, str]], 
                   **kwargs) -> str:
        """聊天对话"""
        try:
            # 获取模型配置
            model_config = self.models.get(model_name, {})
            
            # 构建请求参数
            request_data = {
                "model": model_config.get("model_name", model_name),
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
                    "top_p": kwargs.get("top_p", model_config.get("top_p", 0.9)),
                    "top_k": kwargs.get("top_k", model_config.get("top_k", 40)),
                    "repeat_penalty": kwargs.get("repeat_penalty", model_config.get("repeat_penalty", 1.1)),
                    "num_ctx": kwargs.get("context_length", model_config.get("context_length", 2048))
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/chat",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        message = data.get("message", {})
                        return message.get("content", "")
                    else:
                        error_text = await response.text()
                        logger.error(f"聊天对话失败，状态码: {response.status}, 错误: {error_text}")
                        return "抱歉，对话时出现错误。"
                        
        except Exception as e:
            logger.error(f"聊天对话异常: {e}")
            return "抱歉，对话时出现异常。"
    
    async def generate_traffic_report(self, detections: List[Dict]) -> str:
        """生成交通报告"""
        # 构建提示词
        detection_summary = self._summarize_detections(detections)
        
        prompt = f"""基于以下交通检测数据，生成一份专业的交通状况报告：

检测数据：
{detection_summary}

请分析以下方面：
1. 交通流量状况
2. 车辆类型分布
3. 潜在安全隐患
4. 违法行为识别
5. 改善建议

要求：
- 语言专业、简洁
- 突出重点问题
- 提供可行建议
- 字数控制在500字以内"""

        # 使用推荐的通用模型
        model_name = self.recommended.get("general", "qwen")
        return await self.generate_response(model_name, prompt)
    
    def _summarize_detections(self, detections: List[Dict]) -> str:
        """总结检测数据"""
        if not detections:
            return "暂无检测数据"
        
        # 统计各类目标数量
        stats = {}
        for detection in detections:
            obj_class = detection.get("class", "unknown")
            stats[obj_class] = stats.get(obj_class, 0) + 1
        
        # 构建摘要
        summary_lines = []
        for obj_class, count in stats.items():
            summary_lines.append(f"- {obj_class}: {count}个")
        
        return f"检测到的目标:\n" + "\n".join(summary_lines)
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.models.keys())
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        return self.models.get(model_name, {})
    
    def get_recommended_model(self, task_type: str = "general") -> str:
        """获取推荐模型"""
        return self.recommended.get(task_type, "qwen")

# 同步版本的客户端（用于兼容性）
class OllamaClientSync:
    """Ollama同步客户端"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get("base_url", "http://localhost:11434")
        self.timeout = config.get("timeout", 60)
        self.models = config.get("models", {})
        self.recommended = config.get("recommended", {})
    
    def generate_response(self, model_name: str, prompt: str, **kwargs) -> str:
        """同步生成响应"""
        try:
            model_config = self.models.get(model_name, {})
            
            request_data = {
                "model": model_config.get("model_name", model_name),
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
                    "top_p": kwargs.get("top_p", model_config.get("top_p", 0.9)),
                    "top_k": kwargs.get("top_k", model_config.get("top_k", 40)),
                    "repeat_penalty": kwargs.get("repeat_penalty", model_config.get("repeat_penalty", 1.1)),
                    "num_ctx": kwargs.get("context_length", model_config.get("context_length", 2048))
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=request_data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("response", "")
            else:
                logger.error(f"同步生成失败，状态码: {response.status_code}")
                return "抱歉，生成响应时出现错误。"
                
        except Exception as e:
            logger.error(f"同步生成异常: {e}")
            return "抱歉，生成响应时出现异常。"

    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        return list(self.models.keys())

    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        return self.models.get(model_name, {})

    def get_recommended_model(self, task_type: str = "general") -> str:
        """获取推荐模型"""
        return self.recommended.get(task_type, "qwen")
