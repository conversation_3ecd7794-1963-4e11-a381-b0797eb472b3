"""
语音合成器
将文本转换为语音，支持多种TTS服务
"""
import logging
import asyncio
import io
import time
from typing import Dict, Optional, Any, Union, List
from pathlib import Path

from config import api_config
from .prompt_templates import PromptTemplates

# 尝试导入语音合成库
try:
    import azure.cognitiveservices.speech as speechsdk
    AZURE_SPEECH_AVAILABLE = True
except ImportError:
    AZURE_SPEECH_AVAILABLE = False
    logging.warning("Azure Speech SDK not available")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("pyttsx3 not available")


class VoiceSynthesizer:
    """语音合成器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or api_config.get_tts_config()
        self.logger = logging.getLogger(__name__)
        
        # TTS配置
        self.provider = self.config.get("provider", "azure_speech")
        self.enabled = self.config.get("enabled", True)
        self.speed = self.config.get("speed", 1.0)
        self.pitch = self.config.get("pitch", 0.0)
        self.volume = self.config.get("volume", 1.0)
        
        # 语音配置
        self.voice = self.config.get("voice", "zh-CN-XiaoxiaoNeural")
        self.language = "zh-CN"
        
        # 初始化合成器
        self.synthesizer = None
        if self.enabled:
            self._initialize_synthesizer()
        
        # 统计信息
        self.total_syntheses = 0
        self.total_characters = 0
        self.synthesis_times = []
        
        # 缓存
        self.audio_cache: Dict[str, bytes] = {}
        self.cache_enabled = self.config.get("cache_enabled", True)
        self.max_cache_size = 100
    
    def _initialize_synthesizer(self):
        """初始化语音合成器"""
        try:
            if self.provider == "azure_speech" and AZURE_SPEECH_AVAILABLE:
                self._initialize_azure_speech()
            elif self.provider == "local" and PYTTSX3_AVAILABLE:
                self._initialize_pyttsx3()
            else:
                self.logger.warning(f"TTS provider {self.provider} not available")
                self.synthesizer = None
                
        except Exception as e:
            self.logger.error(f"Failed to initialize TTS: {e}")
            self.synthesizer = None
    
    def _initialize_azure_speech(self):
        """初始化Azure语音服务"""
        api_key = self.config.get("api_key")
        region = self.config.get("region", "eastus")
        
        if not api_key:
            self.logger.warning("Azure Speech API key not provided")
            return
        
        # 创建语音配置
        speech_config = speechsdk.SpeechConfig(
            subscription=api_key,
            region=region
        )
        speech_config.speech_synthesis_voice_name = self.voice
        
        # 创建合成器
        self.synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config)
        self.logger.info("Azure Speech synthesizer initialized")
    
    def _initialize_pyttsx3(self):
        """初始化pyttsx3本地TTS"""
        self.synthesizer = pyttsx3.init()
        
        # 设置语音参数
        voices = self.synthesizer.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                self.synthesizer.setProperty('voice', voice.id)
                break
        
        self.synthesizer.setProperty('rate', int(200 * self.speed))
        self.synthesizer.setProperty('volume', self.volume)
        
        self.logger.info("pyttsx3 synthesizer initialized")
    
    async def synthesize_text(self, text: str, output_file: Optional[str] = None) -> Optional[bytes]:
        """合成文本为语音"""
        if not self.enabled or not text.strip():
            return None
        
        # 检查缓存
        cache_key = f"{text}_{self.voice}_{self.speed}"
        if self.cache_enabled and cache_key in self.audio_cache:
            self.logger.debug("Using cached audio")
            return self.audio_cache[cache_key]
        
        start_time = time.time()
        
        try:
            if self.provider == "azure_speech":
                audio_data = await self._synthesize_azure(text)
            elif self.provider == "local":
                audio_data = await self._synthesize_local(text, output_file)
            else:
                self.logger.warning("No TTS provider available, generating mock audio")
                audio_data = self._generate_mock_audio(text)
            
            # 更新统计
            synthesis_time = time.time() - start_time
            self.synthesis_times.append(synthesis_time)
            self.total_syntheses += 1
            self.total_characters += len(text)
            
            # 缓存结果
            if self.cache_enabled and audio_data:
                self._cache_audio(cache_key, audio_data)
            
            # 保存文件
            if output_file and audio_data:
                await self._save_audio_file(audio_data, output_file)
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Speech synthesis failed: {e}")
            return None
    
    async def _synthesize_azure(self, text: str) -> Optional[bytes]:
        """使用Azure语音服务合成"""
        if not self.synthesizer:
            return None
        
        try:
            # 创建SSML
            ssml = self._create_ssml(text)
            
            # 合成语音
            result = await asyncio.to_thread(
                self.synthesizer.speak_ssml_async(ssml).get
            )
            
            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                return result.audio_data
            else:
                self.logger.error(f"Azure TTS failed: {result.reason}")
                return None
                
        except Exception as e:
            self.logger.error(f"Azure TTS error: {e}")
            return None
    
    async def _synthesize_local(self, text: str, output_file: Optional[str] = None) -> Optional[bytes]:
        """使用本地TTS合成"""
        if not self.synthesizer:
            return None
        
        try:
            if output_file:
                # 保存到文件
                await asyncio.to_thread(self.synthesizer.save_to_file, text, output_file)
                
                # 读取文件内容
                with open(output_file, 'rb') as f:
                    return f.read()
            else:
                # 直接播放（无法返回音频数据）
                await asyncio.to_thread(self.synthesizer.say, text)
                await asyncio.to_thread(self.synthesizer.runAndWait)
                return b"local_tts_played"
                
        except Exception as e:
            self.logger.error(f"Local TTS error: {e}")
            return None
    
    def _create_ssml(self, text: str) -> str:
        """创建SSML格式的文本"""
        # 计算语速和音调
        rate_percent = int((self.speed - 1.0) * 100)
        rate_str = f"{rate_percent:+d}%" if rate_percent != 0 else "0%"
        
        pitch_hz = int(self.pitch)
        pitch_str = f"{pitch_hz:+d}Hz" if pitch_hz != 0 else "0Hz"
        
        ssml = f"""
        <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="{self.language}">
            <voice name="{self.voice}">
                <prosody rate="{rate_str}" pitch="{pitch_str}" volume="{self.volume}">
                    {text}
                </prosody>
            </voice>
        </speak>
        """
        return ssml.strip()
    
    def _generate_mock_audio(self, text: str) -> bytes:
        """生成模拟音频数据"""
        # 简单的模拟：返回文本长度对应的空字节
        mock_duration = len(text) * 0.1  # 假设每个字符0.1秒
        mock_size = int(mock_duration * 16000 * 2)  # 16kHz, 16bit
        return b'\x00' * mock_size
    
    def _cache_audio(self, cache_key: str, audio_data: bytes):
        """缓存音频数据"""
        if len(self.audio_cache) >= self.max_cache_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self.audio_cache))
            del self.audio_cache[oldest_key]
        
        self.audio_cache[cache_key] = audio_data
    
    async def _save_audio_file(self, audio_data: bytes, output_file: str):
        """保存音频文件"""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'wb') as f:
                f.write(audio_data)
            
            self.logger.debug(f"Audio saved to {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save audio file: {e}")
    
    async def synthesize_announcement(self, announcement_type: str, 
                                    content: str, 
                                    urgency_level: str = "normal") -> Optional[bytes]:
        """合成交通播报"""
        try:
            # 获取播报模板
            voice_templates = PromptTemplates.get_voice_templates()
            
            if announcement_type in voice_templates:
                # 使用预定义模板
                announcement_text = voice_templates[announcement_type]
            else:
                # 使用自定义内容
                announcement_text = content
            
            # 根据紧急程度调整语音参数
            original_speed = self.speed
            original_volume = self.volume
            
            if urgency_level == "urgent":
                self.speed = min(self.speed * 1.2, 2.0)
                self.volume = min(self.volume * 1.1, 1.0)
            elif urgency_level == "low":
                self.speed = max(self.speed * 0.9, 0.5)
            
            # 合成语音
            audio_data = await self.synthesize_text(announcement_text)
            
            # 恢复原始参数
            self.speed = original_speed
            self.volume = original_volume
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Failed to synthesize announcement: {e}")
            return None
    
    async def synthesize_alert(self, alert_text: str, alert_level: str = "medium") -> Optional[bytes]:
        """合成警报语音"""
        # 根据警报级别调整语音
        if alert_level == "high":
            prefix = "紧急提醒："
            self.speed = min(self.speed * 1.3, 2.0)
        elif alert_level == "medium":
            prefix = "注意："
        else:
            prefix = "提示："
            self.speed = max(self.speed * 0.9, 0.5)
        
        full_text = f"{prefix}{alert_text}"
        return await self.synthesize_text(full_text)
    
    def get_available_voices(self) -> List[str]:
        """获取可用的语音列表"""
        if self.provider == "azure_speech":
            # Azure支持的中文语音
            return [
                "zh-CN-XiaoxiaoNeural",  # 女声
                "zh-CN-YunxiNeural",     # 男声
                "zh-CN-YunjianNeural",   # 男声
                "zh-CN-XiaoyiNeural",    # 女声
                "zh-CN-YunyangNeural",   # 男声
                "zh-CN-XiaochenNeural",  # 女声
                "zh-CN-XiaohanNeural",   # 女声
                "zh-CN-XiaomengNeural",  # 女声
                "zh-CN-XiaomoNeural",    # 女声
                "zh-CN-XiaoqiuNeural",   # 女声
                "zh-CN-XiaoruiNeural",   # 女声
                "zh-CN-XiaoshuangNeural", # 女声
                "zh-CN-XiaoxuanNeural",  # 女声
                "zh-CN-XiaoyanNeural",   # 女声
                "zh-CN-XiaoyouNeural",   # 女声
                "zh-CN-XiaozhenNeural",  # 女声
                "zh-CN-YunfengNeural",   # 男声
                "zh-CN-YunhaoNeural",    # 男声
                "zh-CN-YunjieNeural",    # 男声
                "zh-CN-YunxiaNeural",    # 男声
                "zh-CN-YunyeNeural",     # 男声
                "zh-CN-YunzeNeural",     # 男声
            ]
        elif self.provider == "local" and self.synthesizer:
            try:
                voices = self.synthesizer.getProperty('voices')
                return [voice.id for voice in voices]
            except:
                return ["default"]
        else:
            return ["default"]
    
    def set_voice(self, voice_name: str):
        """设置语音"""
        self.voice = voice_name
        if self.provider == "azure_speech" and self.synthesizer:
            # 重新初始化Azure合成器
            self._initialize_azure_speech()
        elif self.provider == "local" and self.synthesizer:
            try:
                self.synthesizer.setProperty('voice', voice_name)
            except:
                self.logger.warning(f"Failed to set voice: {voice_name}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_synthesis_time = (sum(self.synthesis_times) / len(self.synthesis_times) 
                            if self.synthesis_times else 0)
        
        return {
            "total_syntheses": self.total_syntheses,
            "total_characters": self.total_characters,
            "avg_synthesis_time": avg_synthesis_time,
            "cache_size": len(self.audio_cache),
            "provider": self.provider,
            "voice": self.voice,
            "enabled": self.enabled
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.audio_cache.clear()
        self.logger.info("Audio cache cleared")
    
    def reset(self):
        """重置合成器"""
        self.total_syntheses = 0
        self.total_characters = 0
        self.synthesis_times.clear()
        self.clear_cache()
        self.logger.info("Voice synthesizer reset")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'synthesizer') and self.synthesizer:
            try:
                if self.provider == "local":
                    self.synthesizer.stop()
            except:
                pass
