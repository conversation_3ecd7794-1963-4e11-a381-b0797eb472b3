"""
大语言模型引擎
整合报告生成、语音合成、NLP处理等功能
"""
import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
import time

from config import api_config
from .report_generator import ReportGenerator
from .voice_synthesizer import VoiceSynthesizer
from .nlp_processor import NLPProcessor, TextAnalysisResult


class LLMEngine:
    """大语言模型引擎"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.report_generator = ReportGenerator(self.config.get("report", {}))
        self.voice_synthesizer = VoiceSynthesizer(self.config.get("voice", {}))
        self.nlp_processor = NLPProcessor(self.config.get("nlp", {}))
        
        # 状态管理
        self.initialized = False
        self.enabled = self.config.get("enabled", True)
        
        # 事件回调
        self.on_report_generated: Optional[Callable] = None
        self.on_voice_synthesized: Optional[Callable] = None
        self.on_text_analyzed: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 统计信息
        self.total_operations = 0
        self.operation_times = []

    async def initialize(self) -> bool:
        """初始化LLM引擎"""
        try:
            self.logger.info("Initializing LLM engine...")

            # 初始化各个组件
            # 这里可以添加组件的初始化逻辑

            self.initialized = True
            self.logger.info("LLM engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize LLM engine: {e}")
            return False

    async def start(self) -> bool:
        """启动LLM引擎"""
        try:
            if not self.initialized:
                self.logger.error("LLM engine not initialized")
                return False

            self.logger.info("Starting LLM engine...")
            # 这里可以添加启动逻辑

            self.logger.info("LLM engine started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start LLM engine: {e}")
            return False

    async def stop(self) -> bool:
        """停止LLM引擎"""
        try:
            self.logger.info("Stopping LLM engine...")

            # 这里可以添加停止逻辑

            self.logger.info("LLM engine stopped successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to stop LLM engine: {e}")
            return False
    
    async def generate_comprehensive_report(self, 
                                          traffic_data: Dict[str, Any],
                                          accidents: List[Dict[str, Any]],
                                          violations: List[Dict[str, Any]],
                                          alerts: List[Dict[str, Any]],
                                          decisions: List[Dict[str, Any]]) -> Dict[str, str]:
        """生成综合报告"""
        if not self.enabled:
            return {}
        
        start_time = time.time()
        reports = {}
        
        try:
            # 并发生成各类报告
            tasks = []
            
            # 交通分析报告
            if traffic_data:
                tasks.append(self._generate_traffic_report(traffic_data))
            
            # 事故报告
            for accident in accidents:
                tasks.append(self._generate_accident_report(accident))
            
            # 违法报告
            for violation in violations:
                tasks.append(self._generate_violation_report(violation))
            
            # 安全警报
            for alert in alerts:
                tasks.append(self._generate_alert_report(alert))
            
            # 决策总结
            for decision in decisions:
                tasks.append(self._generate_decision_report(decision))
            
            # 等待所有任务完成
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 整理结果
                report_index = 0
                
                if traffic_data:
                    if report_index < len(results) and not isinstance(results[report_index], Exception):
                        reports["traffic_analysis"] = results[report_index]
                    report_index += 1
                
                for i, accident in enumerate(accidents):
                    if report_index < len(results) and not isinstance(results[report_index], Exception):
                        reports[f"accident_{accident.get('accident_id', i)}"] = results[report_index]
                    report_index += 1
                
                for i, violation in enumerate(violations):
                    if report_index < len(results) and not isinstance(results[report_index], Exception):
                        reports[f"violation_{violation.get('violation_id', i)}"] = results[report_index]
                    report_index += 1
                
                for i, alert in enumerate(alerts):
                    if report_index < len(results) and not isinstance(results[report_index], Exception):
                        reports[f"alert_{alert.get('alert_id', i)}"] = results[report_index]
                    report_index += 1
                
                for i, decision in enumerate(decisions):
                    if report_index < len(results) and not isinstance(results[report_index], Exception):
                        reports[f"decision_{decision.get('decision_id', i)}"] = results[report_index]
                    report_index += 1
            
            # 更新统计
            operation_time = time.time() - start_time
            self.operation_times.append(operation_time)
            self.total_operations += 1
            
            # 触发回调
            if self.on_report_generated:
                await self._safe_callback(self.on_report_generated, reports)
            
            return reports
            
        except Exception as e:
            self.logger.error(f"Failed to generate comprehensive report: {e}")
            if self.on_error:
                await self._safe_callback(self.on_error, e)
            return {}
    
    async def _generate_traffic_report(self, traffic_data: Dict[str, Any]) -> str:
        """生成交通报告"""
        return await self.report_generator.generate_traffic_analysis(traffic_data)
    
    async def _generate_accident_report(self, accident_data: Dict[str, Any]) -> str:
        """生成事故报告"""
        return await self.report_generator.generate_accident_report(accident_data)
    
    async def _generate_violation_report(self, violation_data: Dict[str, Any]) -> str:
        """生成违法报告"""
        return await self.report_generator.generate_violation_report(violation_data)
    
    async def _generate_alert_report(self, alert_data: Dict[str, Any]) -> str:
        """生成警报报告"""
        return await self.report_generator.generate_safety_alert(alert_data)
    
    async def _generate_decision_report(self, decision_data: Dict[str, Any]) -> str:
        """生成决策报告"""
        return await self.report_generator.generate_decision_summary(decision_data)
    
    async def synthesize_voice_announcements(self, 
                                           announcements: List[Dict[str, Any]]) -> Dict[str, bytes]:
        """合成语音播报"""
        if not self.enabled:
            return {}
        
        voice_data = {}
        
        try:
            tasks = []
            for announcement in announcements:
                task = self.voice_synthesizer.synthesize_announcement(
                    announcement.get("type", "general"),
                    announcement.get("content", ""),
                    announcement.get("urgency", "normal")
                )
                tasks.append(task)
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, (announcement, result) in enumerate(zip(announcements, results)):
                    if not isinstance(result, Exception) and result:
                        announcement_id = announcement.get("id", f"announcement_{i}")
                        voice_data[announcement_id] = result
            
            # 触发回调
            if self.on_voice_synthesized:
                await self._safe_callback(self.on_voice_synthesized, voice_data)
            
            return voice_data
            
        except Exception as e:
            self.logger.error(f"Failed to synthesize voice announcements: {e}")
            if self.on_error:
                await self._safe_callback(self.on_error, e)
            return {}
    
    async def analyze_text_content(self, texts: List[str]) -> List[TextAnalysisResult]:
        """分析文本内容"""
        if not self.enabled:
            return []
        
        try:
            # 并发分析文本
            tasks = [
                asyncio.to_thread(self.nlp_processor.analyze_text, text)
                for text in texts
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            analysis_results = [
                result for result in results 
                if not isinstance(result, Exception)
            ]
            
            # 触发回调
            if self.on_text_analyzed:
                await self._safe_callback(self.on_text_analyzed, analysis_results)
            
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"Failed to analyze text content: {e}")
            if self.on_error:
                await self._safe_callback(self.on_error, e)
            return []
    
    async def generate_smart_announcement(self, 
                                        event_type: str,
                                        event_data: Dict[str, Any],
                                        target_audience: str = "drivers") -> Optional[bytes]:
        """生成智能播报"""
        try:
            # 根据事件类型生成播报内容
            announcement_text = self._generate_announcement_text(event_type, event_data, target_audience)
            
            if not announcement_text:
                return None
            
            # 确定紧急程度
            urgency_level = self._determine_urgency_level(event_type, event_data)
            
            # 合成语音
            audio_data = await self.voice_synthesizer.synthesize_announcement(
                event_type, announcement_text, urgency_level
            )
            
            return audio_data
            
        except Exception as e:
            self.logger.error(f"Failed to generate smart announcement: {e}")
            return None
    
    def _generate_announcement_text(self, 
                                  event_type: str, 
                                  event_data: Dict[str, Any],
                                  target_audience: str) -> str:
        """生成播报文本"""
        if event_type == "accident":
            severity = event_data.get("severity", "moderate")
            location = event_data.get("location", "未知位置")
            
            if severity == "serious":
                return f"前方{location}发生严重交通事故，请减速慢行，注意避让救援车辆。"
            else:
                return f"前方{location}发生交通事故，请注意安全，减速通过。"
        
        elif event_type == "violation":
            violation_type = event_data.get("violation_type", "违法行为")
            
            if violation_type == "speeding":
                return "检测到超速行驶，请减速慢行，注意交通安全。"
            elif violation_type == "wrong_direction":
                return "检测到逆向行驶，请立即调整行驶方向，确保安全。"
            elif violation_type == "no_helmet":
                return "骑行人员请注意，请佩戴安全头盔，确保出行安全。"
            else:
                return f"检测到{violation_type}，请遵守交通规则，安全文明出行。"
        
        elif event_type == "congestion":
            congestion_level = event_data.get("congestion_level", "moderate")
            
            if congestion_level == "severe":
                return "前方道路严重拥堵，建议选择其他路线或耐心等待。"
            else:
                return "前方道路出现拥堵，请保持安全车距，耐心等待。"
        
        elif event_type == "emergency":
            return "紧急情况，请所有车辆立即靠边停车，为救援车辆让行。"
        
        else:
            return "请注意交通安全，遵守交通规则。"
    
    def _determine_urgency_level(self, event_type: str, event_data: Dict[str, Any]) -> str:
        """确定紧急程度"""
        if event_type == "emergency":
            return "urgent"
        elif event_type == "accident":
            severity = event_data.get("severity", "moderate")
            return "urgent" if severity == "serious" else "normal"
        elif event_type == "violation":
            severity = event_data.get("severity", "moderate")
            return "normal" if severity == "minor" else "medium"
        else:
            return "normal"
    
    async def _safe_callback(self, callback: Callable, *args):
        """安全调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            self.logger.error(f"Error in callback: {e}")
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        avg_operation_time = (sum(self.operation_times) / len(self.operation_times) 
                            if self.operation_times else 0)
        
        return {
            "llm_engine": {
                "total_operations": self.total_operations,
                "avg_operation_time": avg_operation_time,
                "enabled": self.enabled
            },
            "report_generator": self.report_generator.get_statistics(),
            "voice_synthesizer": self.voice_synthesizer.get_statistics(),
            "nlp_processor": self.nlp_processor.get_statistics()
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        self.enabled = self.config.get("enabled", True)
        
        # 更新各组件配置
        if "report" in new_config:
            # 报告生成器配置更新
            pass
        
        if "voice" in new_config:
            # 语音合成器配置更新
            pass
        
        if "nlp" in new_config:
            # NLP处理器配置更新
            pass
        
        self.logger.info("LLM engine configuration updated")
    
    def reset(self):
        """重置LLM引擎"""
        # 重置各组件
        self.report_generator.reset()
        self.voice_synthesizer.reset()
        self.nlp_processor.reset()
        
        # 重置统计信息
        self.total_operations = 0
        self.operation_times.clear()
        
        self.logger.info("LLM engine reset")
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 清理各组件
            self.report_generator.clear_cache()
            self.voice_synthesizer.clear_cache()
            
            self.initialized = False
            self.logger.info("LLM engine cleaned up")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self.initialized and self.enabled
