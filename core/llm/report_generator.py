"""
报告生成器
使用大语言模型生成各种类型的报告
"""
import logging
import time
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from config import api_config
from .prompt_templates import PromptTemplates, PromptType

# 尝试导入OpenAI
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("OpenAI not available, using mock generator")


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or api_config.get_llm_config()
        self.logger = logging.getLogger(__name__)
        
        # LLM配置
        self.provider = self.config.get("provider", "openai")
        self.enabled = self.config.get("enabled", True)
        self.model = self.config.get("model", "gpt-3.5-turbo")
        self.max_tokens = self.config.get("max_tokens", 1000)
        self.temperature = self.config.get("temperature", 0.7)
        
        # 初始化客户端
        self.client = None
        if self.enabled and OPENAI_AVAILABLE:
            self._initialize_client()
        
        # 统计信息
        self.total_reports = 0
        self.total_tokens_used = 0
        self.generation_times = []
        
        # 缓存
        self.report_cache: Dict[str, str] = {}
        self.cache_enabled = self.config.get("cache_enabled", True)
    
    def _initialize_client(self):
        """初始化LLM客户端"""
        try:
            if self.provider == "openai":
                api_key = self.config.get("api_key")
                base_url = self.config.get("base_url", "https://api.openai.com/v1")
                
                if api_key:
                    openai.api_key = api_key
                    openai.base_url = base_url
                    self.client = openai
                    self.logger.info("OpenAI client initialized")
                else:
                    self.logger.warning("OpenAI API key not provided")
            
            elif self.provider == "azure_openai":
                # Azure OpenAI配置
                api_key = self.config.get("api_key")
                endpoint = self.config.get("endpoint")
                api_version = self.config.get("api_version", "2023-05-15")
                
                if api_key and endpoint:
                    openai.api_key = api_key
                    openai.api_base = endpoint
                    openai.api_type = "azure"
                    openai.api_version = api_version
                    self.client = openai
                    self.logger.info("Azure OpenAI client initialized")
                else:
                    self.logger.warning("Azure OpenAI configuration incomplete")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM client: {e}")
            self.client = None
    
    async def generate_accident_report(self, accident_data: Dict[str, Any]) -> str:
        """生成事故报告"""
        try:
            # 格式化时间
            timestamp = datetime.fromtimestamp(accident_data.get("timestamp", time.time()))
            accident_data["timestamp"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 格式化位置
            location = accident_data.get("location", (0, 0))
            accident_data["location"] = f"坐标({location[0]:.1f}, {location[1]:.1f})"
            
            # 格式化涉及轨迹
            involved_tracks = accident_data.get("involved_tracks", [])
            accident_data["involved_tracks"] = ", ".join(map(str, involved_tracks))
            
            # 格式化证据
            evidence = accident_data.get("evidence", {})
            evidence_str = "\n".join([f"- {k}: {v}" for k, v in evidence.items()])
            accident_data["evidence"] = evidence_str
            
            # 生成提示词
            prompt = PromptTemplates.format_template(
                PromptType.ACCIDENT_REPORT, 
                **accident_data
            )
            
            # 生成报告
            report = await self._generate_text(prompt, "accident_report")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Failed to generate accident report: {e}")
            return self._generate_fallback_accident_report(accident_data)
    
    async def generate_violation_report(self, violation_data: Dict[str, Any]) -> str:
        """生成违法报告"""
        try:
            # 格式化时间
            timestamp = datetime.fromtimestamp(violation_data.get("timestamp", time.time()))
            violation_data["timestamp"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 格式化位置
            location = violation_data.get("location", (0, 0))
            violation_data["location"] = f"坐标({location[0]:.1f}, {location[1]:.1f})"
            
            # 格式化证据
            evidence = violation_data.get("evidence", {})
            evidence_str = "\n".join([f"- {k}: {v}" for k, v in evidence.items()])
            violation_data["evidence"] = evidence_str
            
            # 生成提示词
            prompt = PromptTemplates.format_template(
                PromptType.VIOLATION_REPORT,
                **violation_data
            )
            
            # 生成报告
            report = await self._generate_text(prompt, "violation_report")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Failed to generate violation report: {e}")
            return self._generate_fallback_violation_report(violation_data)
    
    async def generate_traffic_analysis(self, traffic_data: Dict[str, Any]) -> str:
        """生成交通分析报告"""
        try:
            # 格式化时间
            timestamp = datetime.fromtimestamp(traffic_data.get("timestamp", time.time()))
            traffic_data["timestamp"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 生成提示词
            prompt = PromptTemplates.format_template(
                PromptType.TRAFFIC_ANALYSIS,
                **traffic_data
            )
            
            # 生成报告
            report = await self._generate_text(prompt, "traffic_analysis")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Failed to generate traffic analysis: {e}")
            return self._generate_fallback_traffic_analysis(traffic_data)
    
    async def generate_safety_alert(self, alert_data: Dict[str, Any]) -> str:
        """生成安全警报"""
        try:
            # 格式化时间
            timestamp = datetime.fromtimestamp(alert_data.get("timestamp", time.time()))
            alert_data["timestamp"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 格式化位置
            location = alert_data.get("location", (0, 0))
            alert_data["location"] = f"坐标({location[0]:.1f}, {location[1]:.1f})"
            
            # 格式化涉及轨迹
            affected_tracks = alert_data.get("affected_tracks", [])
            alert_data["affected_tracks"] = ", ".join(map(str, affected_tracks))
            
            # 格式化建议措施
            recommendations = alert_data.get("recommendations", [])
            alert_data["recommendations"] = "\n".join([f"- {rec}" for rec in recommendations])
            
            # 格式化证据
            evidence = alert_data.get("evidence", {})
            evidence_str = "\n".join([f"- {k}: {v}" for k, v in evidence.items()])
            alert_data["evidence"] = evidence_str
            
            # 生成提示词
            prompt = PromptTemplates.format_template(
                PromptType.SAFETY_ALERT,
                **alert_data
            )
            
            # 生成警报
            alert = await self._generate_text(prompt, "safety_alert")
            
            return alert
            
        except Exception as e:
            self.logger.error(f"Failed to generate safety alert: {e}")
            return self._generate_fallback_safety_alert(alert_data)
    
    async def generate_decision_summary(self, decision_data: Dict[str, Any]) -> str:
        """生成决策总结"""
        try:
            # 格式化时间
            timestamp = datetime.fromtimestamp(decision_data.get("timestamp", time.time()))
            decision_data["timestamp"] = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            # 格式化位置
            target_location = decision_data.get("target_location")
            if target_location:
                decision_data["target_location"] = f"坐标({target_location[0]:.1f}, {target_location[1]:.1f})"
            else:
                decision_data["target_location"] = "全区域"
            
            # 格式化涉及轨迹
            affected_tracks = decision_data.get("affected_tracks", [])
            decision_data["affected_tracks"] = ", ".join(map(str, affected_tracks))
            
            # 格式化建议措施
            recommendations = decision_data.get("recommendations", [])
            decision_data["recommendations"] = "\n".join([f"- {rec}" for rec in recommendations])
            
            # 生成提示词
            prompt = PromptTemplates.format_template(
                PromptType.DECISION_SUMMARY,
                **decision_data
            )
            
            # 生成总结
            summary = await self._generate_text(prompt, "decision_summary")
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to generate decision summary: {e}")
            return self._generate_fallback_decision_summary(decision_data)
    
    async def _generate_text(self, prompt: str, cache_key: str) -> str:
        """生成文本"""
        # 检查缓存
        if self.cache_enabled and cache_key in self.report_cache:
            return self.report_cache[cache_key]
        
        if not self.enabled or not self.client:
            return self._generate_mock_response(prompt)
        
        start_time = time.time()
        
        try:
            if self.provider in ["openai", "azure_openai"]:
                response = await self._generate_openai_text(prompt)
            else:
                response = self._generate_mock_response(prompt)
            
            # 更新统计
            generation_time = time.time() - start_time
            self.generation_times.append(generation_time)
            self.total_reports += 1
            
            # 缓存结果
            if self.cache_enabled:
                self.report_cache[cache_key] = response
            
            return response
            
        except Exception as e:
            self.logger.error(f"Text generation failed: {e}")
            return self._generate_mock_response(prompt)
    
    async def _generate_openai_text(self, prompt: str) -> str:
        """使用OpenAI生成文本"""
        try:
            response = await asyncio.to_thread(
                self.client.ChatCompletion.create,
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一名专业的交通管理专家，请根据用户提供的信息生成专业、准确的报告。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            content = response.choices[0].message.content
            self.total_tokens_used += response.usage.total_tokens
            
            return content.strip()
            
        except Exception as e:
            self.logger.error(f"OpenAI API call failed: {e}")
            raise
    
    def _generate_mock_response(self, prompt: str) -> str:
        """生成模拟响应"""
        if "事故" in prompt:
            return """
# 交通事故报告

## 事故概述
根据系统检测，发生了一起交通事故。事故已被及时发现并记录。

## 事故原因分析
初步分析显示，事故可能由以下因素导致：
- 车辆速度过快
- 跟车距离过近
- 驾驶员注意力不集中

## 影响评估
事故对交通流产生了一定影响，建议及时处理。

## 处理建议
1. 立即派遣救援人员到现场
2. 疏导周边交通
3. 调查事故原因
4. 做好安全防护

## 预防措施
建议加强该路段的安全监控和管理。
"""
        elif "违法" in prompt:
            return """
# 交通违法报告

## 违法行为概述
系统检测到一起交通违法行为，已自动记录相关信息。

## 违法事实认定
根据监控数据，确认存在违法行为。

## 法律依据
根据《道路交通安全法》相关规定。

## 处罚建议
建议按照相关法规进行处罚。

## 教育提醒
提醒驾驶员遵守交通规则，安全文明出行。
"""
        else:
            return """
# 系统分析报告

## 概述
系统已完成相关分析，生成本报告供参考。

## 分析结果
根据当前数据分析，情况基本正常。

## 建议措施
建议继续保持监控，确保交通安全。
"""
    
    def _generate_fallback_accident_report(self, accident_data: Dict[str, Any]) -> str:
        """生成备用事故报告"""
        return f"""
# 交通事故报告

**事故ID**: {accident_data.get('accident_id', 'N/A')}
**事故类型**: {accident_data.get('accident_type', 'N/A')}
**严重程度**: {accident_data.get('severity', 'N/A')}
**发生时间**: {accident_data.get('timestamp', 'N/A')}
**发生地点**: {accident_data.get('location', 'N/A')}

**事故描述**: {accident_data.get('description', '系统检测到交通事故')}

**处理建议**:
- 立即派遣救援人员
- 疏导周边交通
- 确保现场安全
- 调查事故原因
"""
    
    def _generate_fallback_violation_report(self, violation_data: Dict[str, Any]) -> str:
        """生成备用违法报告"""
        return f"""
# 交通违法报告

**违法ID**: {violation_data.get('violation_id', 'N/A')}
**违法类型**: {violation_data.get('violation_type', 'N/A')}
**严重程度**: {violation_data.get('severity', 'N/A')}
**发生时间**: {violation_data.get('timestamp', 'N/A')}
**车辆类型**: {violation_data.get('vehicle_type', 'N/A')}

**违法描述**: {violation_data.get('description', '系统检测到交通违法行为')}

**处理建议**:
- 按照相关法规处罚
- 加强该区域监管
- 教育提醒驾驶员
"""
    
    def _generate_fallback_traffic_analysis(self, traffic_data: Dict[str, Any]) -> str:
        """生成备用交通分析"""
        return f"""
# 交通分析报告

**分析时间**: {traffic_data.get('timestamp', 'N/A')}
**车辆数量**: {traffic_data.get('vehicle_count', 0)}
**拥堵等级**: {traffic_data.get('congestion_level', 'N/A')}
**平均车速**: {traffic_data.get('avg_vehicle_speed', 0):.1f} 米/秒

**分析结果**: 当前交通状况基本正常，建议继续监控。
"""
    
    def _generate_fallback_safety_alert(self, alert_data: Dict[str, Any]) -> str:
        """生成备用安全警报"""
        return f"""
# 安全警报

**警报类型**: {alert_data.get('alert_type', 'N/A')}
**风险等级**: {alert_data.get('risk_level', 'N/A')}
**发生时间**: {alert_data.get('timestamp', 'N/A')}

**警报描述**: {alert_data.get('description', '检测到安全风险')}

**应对措施**: 请立即采取相应的安全措施。
"""
    
    def _generate_fallback_decision_summary(self, decision_data: Dict[str, Any]) -> str:
        """生成备用决策总结"""
        return f"""
# 决策总结

**决策类型**: {decision_data.get('action_type', 'N/A')}
**优先级**: {decision_data.get('priority', 'N/A')}
**决策时间**: {decision_data.get('timestamp', 'N/A')}

**决策描述**: {decision_data.get('description', '系统生成决策建议')}

**实施建议**: 请根据实际情况执行相应措施。
"""
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_generation_time = (sum(self.generation_times) / len(self.generation_times) 
                             if self.generation_times else 0)
        
        return {
            "total_reports": self.total_reports,
            "total_tokens_used": self.total_tokens_used,
            "avg_generation_time": avg_generation_time,
            "cache_size": len(self.report_cache),
            "provider": self.provider,
            "enabled": self.enabled
        }
    
    def clear_cache(self):
        """清空缓存"""
        self.report_cache.clear()
        self.logger.info("Report cache cleared")
    
    def reset(self):
        """重置生成器"""
        self.total_reports = 0
        self.total_tokens_used = 0
        self.generation_times.clear()
        self.clear_cache()
        self.logger.info("Report generator reset")
