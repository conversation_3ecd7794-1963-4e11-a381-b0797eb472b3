"""
自然语言处理器
处理文本分析、关键词提取、情感分析等NLP任务
"""
import logging
import re
import time
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter
from dataclasses import dataclass

# 尝试导入NLP库
try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("jieba not available, using basic NLP")


@dataclass
class TextAnalysisResult:
    """文本分析结果"""
    text: str
    keywords: List[str]
    sentiment: str
    confidence: float
    entities: List[Dict[str, Any]]
    summary: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "text": self.text,
            "keywords": self.keywords,
            "sentiment": self.sentiment,
            "confidence": self.confidence,
            "entities": self.entities,
            "summary": self.summary
        }


class NLPProcessor:
    """自然语言处理器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # NLP配置
        self.max_keywords = self.config.get("max_keywords", 10)
        self.min_keyword_length = self.config.get("min_keyword_length", 2)
        
        # 初始化分词器
        if JIEBA_AVAILABLE:
            self._initialize_jieba()
        
        # 交通相关词典
        self.traffic_keywords = {
            "vehicles": ["汽车", "车辆", "轿车", "卡车", "货车", "客车", "公交车", "摩托车", "电动车", "自行车"],
            "traffic_conditions": ["拥堵", "畅通", "缓慢", "堵塞", "排队", "通行", "流量", "车流"],
            "accidents": ["事故", "碰撞", "追尾", "侧翻", "刮擦", "撞击", "相撞", "翻车"],
            "violations": ["违法", "超速", "闯红灯", "逆行", "压线", "违停", "不系安全带", "不戴头盔"],
            "locations": ["路口", "十字路口", "环岛", "桥梁", "隧道", "高速", "国道", "省道", "市区"],
            "directions": ["东", "南", "西", "北", "左", "右", "前", "后", "直行", "转弯"],
            "emergency": ["紧急", "急救", "救援", "警察", "消防", "医院", "危险", "警告"]
        }
        
        # 情感词典
        self.sentiment_words = {
            "positive": ["安全", "畅通", "正常", "良好", "稳定", "有序", "顺利"],
            "negative": ["危险", "拥堵", "事故", "违法", "混乱", "严重", "紧急"],
            "neutral": ["监控", "检测", "分析", "报告", "统计", "记录", "观察"]
        }
        
        # 统计信息
        self.total_analyses = 0
        self.processing_times = []
    
    def _initialize_jieba(self):
        """初始化jieba分词器"""
        try:
            # 添加交通相关词汇
            for category, words in self.traffic_keywords.items():
                for word in words:
                    jieba.add_word(word)
            
            # 设置词性标注
            jieba.analyse.set_stop_words("stopwords.txt")  # 如果有停用词文件
            
            self.logger.info("jieba initialized with traffic vocabulary")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize jieba: {e}")
    
    def analyze_text(self, text: str) -> TextAnalysisResult:
        """分析文本"""
        start_time = time.time()
        
        try:
            # 提取关键词
            keywords = self.extract_keywords(text)
            
            # 情感分析
            sentiment, confidence = self.analyze_sentiment(text)
            
            # 实体识别
            entities = self.extract_entities(text)
            
            # 生成摘要
            summary = self.generate_summary(text)
            
            result = TextAnalysisResult(
                text=text,
                keywords=keywords,
                sentiment=sentiment,
                confidence=confidence,
                entities=entities,
                summary=summary
            )
            
            # 更新统计
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            self.total_analyses += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Text analysis failed: {e}")
            return TextAnalysisResult(
                text=text,
                keywords=[],
                sentiment="neutral",
                confidence=0.0,
                entities=[],
                summary=text[:100] + "..." if len(text) > 100 else text
            )
    
    def extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            if JIEBA_AVAILABLE:
                # 使用jieba提取关键词
                keywords = jieba.analyse.extract_tags(
                    text, 
                    topK=self.max_keywords,
                    withWeight=False
                )
                
                # 过滤长度
                keywords = [kw for kw in keywords if len(kw) >= self.min_keyword_length]
                
                return keywords
            else:
                # 简单的关键词提取
                return self._simple_keyword_extraction(text)
                
        except Exception as e:
            self.logger.error(f"Keyword extraction failed: {e}")
            return []
    
    def _simple_keyword_extraction(self, text: str) -> List[str]:
        """简单的关键词提取"""
        # 移除标点符号
        text = re.sub(r'[^\w\s]', '', text)
        
        # 分词（简单按空格分割）
        words = text.split()
        
        # 过滤长度和常见词
        stop_words = {"的", "了", "在", "是", "有", "和", "与", "或", "但", "而", "因为", "所以"}
        words = [w for w in words if len(w) >= self.min_keyword_length and w not in stop_words]
        
        # 统计词频
        word_counts = Counter(words)
        
        # 返回最常见的词
        return [word for word, count in word_counts.most_common(self.max_keywords)]
    
    def analyze_sentiment(self, text: str) -> Tuple[str, float]:
        """分析情感"""
        try:
            positive_score = 0
            negative_score = 0
            neutral_score = 0
            
            # 计算情感得分
            for word in self.sentiment_words["positive"]:
                positive_score += text.count(word)
            
            for word in self.sentiment_words["negative"]:
                negative_score += text.count(word)
            
            for word in self.sentiment_words["neutral"]:
                neutral_score += text.count(word)
            
            # 确定主要情感
            total_score = positive_score + negative_score + neutral_score
            
            if total_score == 0:
                return "neutral", 0.5
            
            if positive_score > negative_score and positive_score > neutral_score:
                sentiment = "positive"
                confidence = positive_score / total_score
            elif negative_score > positive_score and negative_score > neutral_score:
                sentiment = "negative"
                confidence = negative_score / total_score
            else:
                sentiment = "neutral"
                confidence = neutral_score / total_score if neutral_score > 0 else 0.5
            
            return sentiment, min(confidence, 1.0)
            
        except Exception as e:
            self.logger.error(f"Sentiment analysis failed: {e}")
            return "neutral", 0.0
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """提取实体"""
        entities = []
        
        try:
            # 提取交通相关实体
            for category, words in self.traffic_keywords.items():
                for word in words:
                    if word in text:
                        # 找到所有出现位置
                        start = 0
                        while True:
                            pos = text.find(word, start)
                            if pos == -1:
                                break
                            
                            entities.append({
                                "text": word,
                                "category": category,
                                "start": pos,
                                "end": pos + len(word),
                                "confidence": 0.9
                            })
                            
                            start = pos + 1
            
            # 提取数字实体
            number_pattern = r'\d+(?:\.\d+)?'
            for match in re.finditer(number_pattern, text):
                entities.append({
                    "text": match.group(),
                    "category": "number",
                    "start": match.start(),
                    "end": match.end(),
                    "confidence": 1.0
                })
            
            # 提取时间实体
            time_patterns = [
                r'\d{1,2}:\d{2}(?::\d{2})?',  # 时间格式
                r'\d{4}-\d{1,2}-\d{1,2}',     # 日期格式
                r'\d{1,2}月\d{1,2}日',         # 中文日期
                r'\d{1,2}点\d{1,2}分'          # 中文时间
            ]
            
            for pattern in time_patterns:
                for match in re.finditer(pattern, text):
                    entities.append({
                        "text": match.group(),
                        "category": "time",
                        "start": match.start(),
                        "end": match.end(),
                        "confidence": 0.8
                    })
            
            # 去重并排序
            entities = self._deduplicate_entities(entities)
            entities.sort(key=lambda x: x["start"])
            
            return entities
            
        except Exception as e:
            self.logger.error(f"Entity extraction failed: {e}")
            return []
    
    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重复实体"""
        unique_entities = []
        seen_spans = set()
        
        for entity in entities:
            span = (entity["start"], entity["end"])
            if span not in seen_spans:
                unique_entities.append(entity)
                seen_spans.add(span)
        
        return unique_entities
    
    def generate_summary(self, text: str, max_length: int = 100) -> str:
        """生成摘要"""
        try:
            # 简单的摘要生成：提取前几句话
            sentences = re.split(r'[。！？]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if not sentences:
                return text[:max_length] + "..." if len(text) > max_length else text
            
            summary = ""
            for sentence in sentences:
                if len(summary + sentence) <= max_length:
                    summary += sentence + "。"
                else:
                    break
            
            if not summary:
                summary = sentences[0][:max_length] + "..."
            
            return summary.strip()
            
        except Exception as e:
            self.logger.error(f"Summary generation failed: {e}")
            return text[:max_length] + "..." if len(text) > max_length else text
    
    def classify_traffic_text(self, text: str) -> Dict[str, float]:
        """分类交通相关文本"""
        try:
            categories = {
                "accident": 0.0,
                "violation": 0.0,
                "congestion": 0.0,
                "normal": 0.0,
                "emergency": 0.0
            }
            
            # 事故相关
            accident_words = ["事故", "碰撞", "追尾", "撞击", "翻车", "受伤"]
            for word in accident_words:
                if word in text:
                    categories["accident"] += 0.2
            
            # 违法相关
            violation_words = ["违法", "超速", "闯红灯", "逆行", "违停"]
            for word in violation_words:
                if word in text:
                    categories["violation"] += 0.2
            
            # 拥堵相关
            congestion_words = ["拥堵", "堵塞", "缓慢", "排队", "车多"]
            for word in congestion_words:
                if word in text:
                    categories["congestion"] += 0.2
            
            # 紧急相关
            emergency_words = ["紧急", "急救", "危险", "警告", "立即"]
            for word in emergency_words:
                if word in text:
                    categories["emergency"] += 0.2
            
            # 正常相关
            normal_words = ["正常", "畅通", "安全", "良好", "稳定"]
            for word in normal_words:
                if word in text:
                    categories["normal"] += 0.2
            
            # 归一化
            total_score = sum(categories.values())
            if total_score > 0:
                for key in categories:
                    categories[key] = min(categories[key] / total_score, 1.0)
            else:
                categories["normal"] = 1.0
            
            return categories
            
        except Exception as e:
            self.logger.error(f"Text classification failed: {e}")
            return {"normal": 1.0, "accident": 0.0, "violation": 0.0, "congestion": 0.0, "emergency": 0.0}
    
    def extract_traffic_info(self, text: str) -> Dict[str, Any]:
        """提取交通信息"""
        try:
            info = {
                "vehicles": [],
                "locations": [],
                "times": [],
                "speeds": [],
                "violations": [],
                "accidents": []
            }
            
            # 提取车辆信息
            for vehicle in self.traffic_keywords["vehicles"]:
                if vehicle in text:
                    info["vehicles"].append(vehicle)
            
            # 提取位置信息
            for location in self.traffic_keywords["locations"]:
                if location in text:
                    info["locations"].append(location)
            
            # 提取速度信息
            speed_pattern = r'(\d+(?:\.\d+)?)\s*(?:公里|千米|km|米|m)?/?(?:小时|时|h|秒|s)'
            speeds = re.findall(speed_pattern, text)
            info["speeds"] = [float(speed) for speed in speeds]
            
            # 提取违法信息
            for violation in self.traffic_keywords["violations"]:
                if violation in text:
                    info["violations"].append(violation)
            
            # 提取事故信息
            for accident in self.traffic_keywords["accidents"]:
                if accident in text:
                    info["accidents"].append(accident)
            
            # 去重
            for key in info:
                if isinstance(info[key], list):
                    info[key] = list(set(info[key]))
            
            return info
            
        except Exception as e:
            self.logger.error(f"Traffic info extraction failed: {e}")
            return {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        avg_processing_time = (sum(self.processing_times) / len(self.processing_times) 
                             if self.processing_times else 0)
        
        return {
            "total_analyses": self.total_analyses,
            "avg_processing_time": avg_processing_time,
            "jieba_available": JIEBA_AVAILABLE,
            "vocabulary_size": sum(len(words) for words in self.traffic_keywords.values())
        }
    
    def reset(self):
        """重置处理器"""
        self.total_analyses = 0
        self.processing_times.clear()
        self.logger.info("NLP processor reset")
