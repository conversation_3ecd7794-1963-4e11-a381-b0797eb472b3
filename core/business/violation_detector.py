"""
违法行为检测器
检测各种交通违法行为
"""
import logging
import time
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from collections import deque, defaultdict
from dataclasses import dataclass
from enum import Enum

from core.ai.detection import Detection
from core.ai.tracking import Track


class ViolationType(str, Enum):
    """违法类型枚举"""
    SPEEDING = "speeding"                    # 超速
    SLOW_DRIVING = "slow_driving"            # 低速行驶
    WRONG_DIRECTION = "wrong_direction"      # 逆向行驶
    LANE_VIOLATION = "lane_violation"        # 压线/变道违法
    RED_LIGHT_RUNNING = "red_light_running"  # 闯红灯
    ILLEGAL_PARKING = "illegal_parking"      # 违法停车
    ILLEGAL_OVERTAKING = "illegal_overtaking" # 违法超车
    NO_HELMET = "no_helmet"                  # 未戴头盔
    NO_SEATBELT = "no_seatbelt"             # 未系安全带


class ViolationSeverity(str, Enum):
    """违法严重程度枚举"""
    MINOR = "minor"        # 轻微违法
    MODERATE = "moderate"  # 一般违法
    SERIOUS = "serious"    # 严重违法


@dataclass
class ViolationEvent:
    """违法事件数据类"""
    violation_id: str
    violation_type: ViolationType
    severity: ViolationSeverity
    confidence: float
    timestamp: float
    location: Tuple[float, float]
    track_id: int
    vehicle_type: str
    description: str
    evidence: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "violation_id": self.violation_id,
            "violation_type": self.violation_type.value,
            "severity": self.severity.value,
            "confidence": self.confidence,
            "timestamp": self.timestamp,
            "location": self.location,
            "track_id": self.track_id,
            "vehicle_type": self.vehicle_type,
            "description": self.description,
            "evidence": self.evidence
        }


class ViolationDetector:
    """违法行为检测器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 检测参数
        self.speed_limit = self.config.get("speed_limit", 60.0)  # 像素/秒
        self.min_speed_limit = self.config.get("min_speed_limit", 20.0)  # 像素/秒
        self.lane_width = self.config.get("lane_width", 100.0)  # 像素
        self.parking_time_threshold = self.config.get("parking_time", 300.0)  # 秒
        
        # 道路信息（简化）
        self.road_direction = self.config.get("road_direction", "horizontal")  # horizontal/vertical
        self.lane_lines = self.config.get("lane_lines", [])  # 车道线坐标
        self.traffic_lights = self.config.get("traffic_lights", [])  # 红绿灯位置
        
        # 历史数据
        self.track_histories: Dict[int, deque] = defaultdict(lambda: deque(maxlen=60))
        self.violation_events: List[ViolationEvent] = []
        self.recent_violations: deque = deque(maxlen=100)
        self.stationary_tracks: Dict[int, float] = {}  # 轨迹ID -> 开始静止时间
        
        # 统计信息
        self.total_violations = 0
        self.violation_type_counts = defaultdict(int)
        self.severity_counts = defaultdict(int)
        self.next_violation_id = 1
    
    def detect(self, tracks: List[Track]) -> List[ViolationEvent]:
        """检测违法行为"""
        current_time = time.time()
        detected_violations = []
        
        # 更新轨迹历史
        self._update_track_histories(tracks, current_time)
        
        for track in tracks:
            if not track.is_active or track.hits < 3:
                continue
            
            # 速度违法检测
            speed_violations = self._detect_speed_violations(track, current_time)
            detected_violations.extend(speed_violations)
            
            # 方向违法检测
            direction_violations = self._detect_direction_violations(track, current_time)
            detected_violations.extend(direction_violations)
            
            # 车道违法检测
            lane_violations = self._detect_lane_violations(track, current_time)
            detected_violations.extend(lane_violations)
            
            # 停车违法检测
            parking_violations = self._detect_parking_violations(track, current_time)
            detected_violations.extend(parking_violations)
            
            # 安全装备检测
            safety_violations = self._detect_safety_violations(track, current_time)
            detected_violations.extend(safety_violations)
        
        # 过滤重复违法
        detected_violations = self._filter_duplicate_violations(detected_violations)
        
        # 更新违法记录
        for violation in detected_violations:
            self.violation_events.append(violation)
            self.recent_violations.append(violation)
            self.total_violations += 1
            self.violation_type_counts[violation.violation_type] += 1
            self.severity_counts[violation.severity] += 1
        
        return detected_violations
    
    def _update_track_histories(self, tracks: List[Track], current_time: float):
        """更新轨迹历史"""
        for track in tracks:
            if track.last_detection:
                history_entry = {
                    "timestamp": current_time,
                    "position": track.last_detection.center,
                    "velocity": track.velocity,
                    "speed": track.get_speed(),
                    "direction": track.get_direction(),
                    "bbox": track.last_detection.bbox
                }
                self.track_histories[track.track_id].append(history_entry)
    
    def _detect_speed_violations(self, track: Track, current_time: float) -> List[ViolationEvent]:
        """检测速度违法"""
        violations = []
        current_speed = track.get_speed()
        
        # 超速检测
        if current_speed > self.speed_limit:
            violation = ViolationEvent(
                violation_id=f"VIO_{self.next_violation_id:06d}",
                violation_type=ViolationType.SPEEDING,
                severity=self._assess_speeding_severity(current_speed),
                confidence=0.9,
                timestamp=current_time,
                location=track.last_detection.center,
                track_id=track.track_id,
                vehicle_type=track.class_name,
                description=f"超速行驶，当前速度 {current_speed:.1f}，限速 {self.speed_limit:.1f}",
                evidence={
                    "current_speed": current_speed,
                    "speed_limit": self.speed_limit,
                    "excess_speed": current_speed - self.speed_limit
                }
            )
            violations.append(violation)
            self.next_violation_id += 1
        
        # 低速行驶检测（仅针对机动车）
        elif (track.class_name in ["car", "truck", "bus"] and 
              current_speed < self.min_speed_limit and 
              current_speed > 5):  # 排除静止车辆
            
            violation = ViolationEvent(
                violation_id=f"VIO_{self.next_violation_id:06d}",
                violation_type=ViolationType.SLOW_DRIVING,
                severity=ViolationSeverity.MINOR,
                confidence=0.7,
                timestamp=current_time,
                location=track.last_detection.center,
                track_id=track.track_id,
                vehicle_type=track.class_name,
                description=f"低速行驶，当前速度 {current_speed:.1f}，最低限速 {self.min_speed_limit:.1f}",
                evidence={
                    "current_speed": current_speed,
                    "min_speed_limit": self.min_speed_limit,
                    "speed_deficit": self.min_speed_limit - current_speed
                }
            )
            violations.append(violation)
            self.next_violation_id += 1
        
        return violations
    
    def _detect_direction_violations(self, track: Track, current_time: float) -> List[ViolationEvent]:
        """检测方向违法（逆行）"""
        violations = []
        history = self.track_histories[track.track_id]
        
        if len(history) < 5:
            return violations
        
        # 分析运动方向
        recent_positions = [entry["position"] for entry in list(history)[-5:]]
        
        if len(recent_positions) >= 2:
            # 计算主要运动方向
            start_pos = recent_positions[0]
            end_pos = recent_positions[-1]
            
            if self.road_direction == "horizontal":
                # 水平道路，检查是否向左行驶（逆行）
                if end_pos[0] < start_pos[0] and abs(end_pos[0] - start_pos[0]) > 50:
                    violation = ViolationEvent(
                        violation_id=f"VIO_{self.next_violation_id:06d}",
                        violation_type=ViolationType.WRONG_DIRECTION,
                        severity=ViolationSeverity.SERIOUS,
                        confidence=0.8,
                        timestamp=current_time,
                        location=track.last_detection.center,
                        track_id=track.track_id,
                        vehicle_type=track.class_name,
                        description="逆向行驶",
                        evidence={
                            "start_position": start_pos,
                            "end_position": end_pos,
                            "movement_direction": "left",
                            "expected_direction": "right"
                        }
                    )
                    violations.append(violation)
                    self.next_violation_id += 1
            
            elif self.road_direction == "vertical":
                # 垂直道路，检查是否向上行驶（逆行）
                if end_pos[1] < start_pos[1] and abs(end_pos[1] - start_pos[1]) > 50:
                    violation = ViolationEvent(
                        violation_id=f"VIO_{self.next_violation_id:06d}",
                        violation_type=ViolationType.WRONG_DIRECTION,
                        severity=ViolationSeverity.SERIOUS,
                        confidence=0.8,
                        timestamp=current_time,
                        location=track.last_detection.center,
                        track_id=track.track_id,
                        vehicle_type=track.class_name,
                        description="逆向行驶",
                        evidence={
                            "start_position": start_pos,
                            "end_position": end_pos,
                            "movement_direction": "up",
                            "expected_direction": "down"
                        }
                    )
                    violations.append(violation)
                    self.next_violation_id += 1
        
        return violations
    
    def _detect_lane_violations(self, track: Track, current_time: float) -> List[ViolationEvent]:
        """检测车道违法"""
        violations = []
        
        # 简化的压线检测
        if track.last_detection:
            bbox = track.last_detection.bbox
            vehicle_width = bbox[2] - bbox[0]
            
            # 检查是否跨越多个车道（简化判断）
            if vehicle_width > self.lane_width * 1.5:
                violation = ViolationEvent(
                    violation_id=f"VIO_{self.next_violation_id:06d}",
                    violation_type=ViolationType.LANE_VIOLATION,
                    severity=ViolationSeverity.MODERATE,
                    confidence=0.6,
                    timestamp=current_time,
                    location=track.last_detection.center,
                    track_id=track.track_id,
                    vehicle_type=track.class_name,
                    description="疑似压线行驶",
                    evidence={
                        "vehicle_width": vehicle_width,
                        "lane_width": self.lane_width,
                        "width_ratio": vehicle_width / self.lane_width
                    }
                )
                violations.append(violation)
                self.next_violation_id += 1
        
        return violations
    
    def _detect_parking_violations(self, track: Track, current_time: float) -> List[ViolationEvent]:
        """检测停车违法"""
        violations = []
        
        # 检测长时间停车
        if track.is_stationary():
            if track.track_id not in self.stationary_tracks:
                self.stationary_tracks[track.track_id] = current_time
            else:
                stationary_duration = current_time - self.stationary_tracks[track.track_id]
                
                if stationary_duration > self.parking_time_threshold:
                    violation = ViolationEvent(
                        violation_id=f"VIO_{self.next_violation_id:06d}",
                        violation_type=ViolationType.ILLEGAL_PARKING,
                        severity=ViolationSeverity.MODERATE,
                        confidence=0.8,
                        timestamp=current_time,
                        location=track.last_detection.center,
                        track_id=track.track_id,
                        vehicle_type=track.class_name,
                        description=f"违法停车 {stationary_duration:.1f} 秒",
                        evidence={
                            "stationary_duration": stationary_duration,
                            "parking_threshold": self.parking_time_threshold
                        }
                    )
                    violations.append(violation)
                    self.next_violation_id += 1
                    
                    # 移除记录，避免重复检测
                    del self.stationary_tracks[track.track_id]
        else:
            # 车辆开始移动，清除静止记录
            if track.track_id in self.stationary_tracks:
                del self.stationary_tracks[track.track_id]
        
        return violations
    
    def _detect_safety_violations(self, track: Track, current_time: float) -> List[ViolationEvent]:
        """检测安全装备违法"""
        violations = []
        
        # 简化的头盔检测（针对摩托车和电动车）
        if track.class_name in ["motorcycle", "bicycle"]:
            # 这里应该结合图像分析来检测头盔
            # 简化实现：随机模拟检测结果
            if np.random.random() < 0.1:  # 10%的概率检测到未戴头盔
                violation = ViolationEvent(
                    violation_id=f"VIO_{self.next_violation_id:06d}",
                    violation_type=ViolationType.NO_HELMET,
                    severity=ViolationSeverity.MODERATE,
                    confidence=0.7,
                    timestamp=current_time,
                    location=track.last_detection.center,
                    track_id=track.track_id,
                    vehicle_type=track.class_name,
                    description="未佩戴安全头盔",
                    evidence={
                        "detection_method": "image_analysis",
                        "helmet_detected": False
                    }
                )
                violations.append(violation)
                self.next_violation_id += 1
        
        return violations
    
    def _assess_speeding_severity(self, current_speed: float) -> ViolationSeverity:
        """评估超速严重程度"""
        excess_speed = current_speed - self.speed_limit
        excess_ratio = excess_speed / self.speed_limit
        
        if excess_ratio > 0.5:  # 超速50%以上
            return ViolationSeverity.SERIOUS
        elif excess_ratio > 0.2:  # 超速20%-50%
            return ViolationSeverity.MODERATE
        else:  # 超速20%以内
            return ViolationSeverity.MINOR
    
    def _filter_duplicate_violations(self, violations: List[ViolationEvent]) -> List[ViolationEvent]:
        """过滤重复违法"""
        filtered = []
        
        for violation in violations:
            is_duplicate = False
            
            # 检查最近的违法记录
            for recent_violation in list(self.recent_violations)[-20:]:
                if (recent_violation.track_id == violation.track_id and
                    recent_violation.violation_type == violation.violation_type and
                    abs(recent_violation.timestamp - violation.timestamp) < 60):  # 60秒内
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(violation)
        
        return filtered
    
    def get_recent_violations(self, time_window: float = 300.0) -> List[ViolationEvent]:
        """获取最近的违法记录"""
        current_time = time.time()
        recent = []
        
        for violation in reversed(self.violation_events):
            if current_time - violation.timestamp <= time_window:
                recent.append(violation)
            else:
                break
        
        return list(reversed(recent))
    
    def get_violation_statistics(self) -> Dict[str, Any]:
        """获取违法统计信息"""
        return {
            "total_violations": self.total_violations,
            "violation_type_counts": dict(self.violation_type_counts),
            "severity_counts": dict(self.severity_counts),
            "recent_violations_count": len(self.recent_violations),
            "active_tracks": len(self.track_histories),
            "stationary_tracks": len(self.stationary_tracks),
            "config": self.config
        }
    
    def reset(self):
        """重置检测器"""
        self.track_histories.clear()
        self.violation_events.clear()
        self.recent_violations.clear()
        self.stationary_tracks.clear()
        self.total_violations = 0
        self.violation_type_counts.clear()
        self.severity_counts.clear()
        self.next_violation_id = 1
        self.logger.info("Violation detector reset")
