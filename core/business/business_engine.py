"""
业务引擎
整合所有业务逻辑模块，提供统一的业务处理接口
"""
import logging
import time
import asyncio
from typing import List, Dict, Optional, Any, Callable
import numpy as np

from core.ai.tracking import Track
from core.ai.behavior_analyzer import BehaviorEvent
from .traffic_analyzer import TrafficAnalyzer, TrafficMetrics
from .accident_detector import AccidentDetector, AccidentEvent
from .violation_detector import ViolationDetector, ViolationEvent
from .safety_monitor import SafetyMonitor, SafetyAlert, RiskLevel
from .decision_engine import DecisionEngine, Decision


class BusinessEngine:
    """业务引擎类"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 业务组件
        self.traffic_analyzer = TrafficAnalyzer(self.config.get("traffic", {}))
        self.accident_detector = AccidentDetector(self.config.get("accident", {}))
        self.violation_detector = ViolationDetector(self.config.get("violation", {}))
        self.safety_monitor = SafetyMonitor(self.config.get("safety", {}))
        self.decision_engine = DecisionEngine(self.config.get("decision", {}))
        
        # 状态管理
        self.initialized = True
        self.processing = False
        
        # 事件回调
        self.on_traffic_analysis: Optional[Callable] = None
        self.on_accident_detected: Optional[Callable] = None
        self.on_violation_detected: Optional[Callable] = None
        self.on_safety_alert: Optional[Callable] = None
        self.on_decision_made: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 性能统计
        self.frame_count = 0
        self.total_processing_time = 0.0
        self.last_analysis_time = time.time()
        
        # 结果缓存
        self.last_traffic_metrics: Optional[TrafficMetrics] = None
        self.last_accidents: List[AccidentEvent] = []
        self.last_violations: List[ViolationEvent] = []
        self.last_alerts: List[SafetyAlert] = []
        self.last_decisions: List[Decision] = []
        self.current_risk_level = RiskLevel.LOW
    
    async def process_analysis(self, 
                             tracks: List[Track], 
                             behavior_events: List[BehaviorEvent],
                             frame_area: Optional[tuple] = None) -> Dict[str, Any]:
        """处理业务分析"""
        if self.processing:
            self.logger.warning("Previous analysis still processing, skipping")
            return self._get_last_results()
        
        self.processing = True
        start_time = time.time()
        
        try:
            results = {}
            
            # 交通流分析
            traffic_metrics = self.traffic_analyzer.analyze(tracks, frame_area)
            self.last_traffic_metrics = traffic_metrics
            results["traffic_metrics"] = traffic_metrics.to_dict()
            
            # 触发交通分析回调
            if self.on_traffic_analysis:
                await self._safe_callback(self.on_traffic_analysis, traffic_metrics)
            
            # 事故检测
            accidents = self.accident_detector.detect(tracks, behavior_events)
            self.last_accidents = accidents
            results["accidents"] = [acc.to_dict() for acc in accidents]
            
            # 触发事故检测回调
            if accidents and self.on_accident_detected:
                await self._safe_callback(self.on_accident_detected, accidents)
            
            # 违法检测
            violations = self.violation_detector.detect(tracks)
            self.last_violations = violations
            results["violations"] = [vio.to_dict() for vio in violations]
            
            # 触发违法检测回调
            if violations and self.on_violation_detected:
                await self._safe_callback(self.on_violation_detected, violations)
            
            # 安全监控
            recent_accidents = self.accident_detector.get_recent_accidents()
            recent_violations = self.violation_detector.get_recent_violations()
            
            risk_level, safety_alerts = self.safety_monitor.assess_safety(
                traffic_metrics, recent_accidents, recent_violations, tracks
            )
            
            self.current_risk_level = risk_level
            self.last_alerts = safety_alerts
            results["risk_level"] = risk_level.value
            results["safety_alerts"] = [alert.to_dict() for alert in safety_alerts]
            
            # 触发安全警报回调
            if safety_alerts and self.on_safety_alert:
                await self._safe_callback(self.on_safety_alert, safety_alerts)
            
            # 决策生成
            decisions = self.decision_engine.make_decisions(
                traffic_metrics, recent_accidents, recent_violations, 
                safety_alerts, risk_level
            )
            
            self.last_decisions = decisions
            results["decisions"] = [dec.to_dict() for dec in decisions]
            
            # 触发决策回调
            if decisions and self.on_decision_made:
                await self._safe_callback(self.on_decision_made, decisions)
            
            # 更新性能统计
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time
            self.frame_count += 1
            self.last_analysis_time = time.time()
            
            # 添加性能信息
            results["performance"] = {
                "processing_time": processing_time,
                "frame_count": self.frame_count,
                "avg_processing_time": self.total_processing_time / self.frame_count
            }
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in business analysis: {e}")
            if self.on_error:
                await self._safe_callback(self.on_error, e)
            return self._get_last_results()
        
        finally:
            self.processing = False
    
    def _get_last_results(self) -> Dict[str, Any]:
        """获取最后的分析结果"""
        return {
            "traffic_metrics": self.last_traffic_metrics.to_dict() if self.last_traffic_metrics else {},
            "accidents": [acc.to_dict() for acc in self.last_accidents],
            "violations": [vio.to_dict() for vio in self.last_violations],
            "risk_level": self.current_risk_level.value,
            "safety_alerts": [alert.to_dict() for alert in self.last_alerts],
            "decisions": [dec.to_dict() for dec in self.last_decisions],
            "performance": {
                "processing_time": 0.0,
                "frame_count": self.frame_count,
                "avg_processing_time": self.total_processing_time / self.frame_count if self.frame_count > 0 else 0
            }
        }
    
    async def _safe_callback(self, callback: Callable, *args):
        """安全调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            self.logger.error(f"Error in callback: {e}")
    
    def get_traffic_trends(self, time_window: int = 300) -> Dict[str, Any]:
        """获取交通趋势"""
        return self.traffic_analyzer.get_traffic_trends(time_window)
    
    def get_recent_accidents(self, time_window: float = 300.0) -> List[Dict[str, Any]]:
        """获取最近事故"""
        accidents = self.accident_detector.get_recent_accidents(time_window)
        return [acc.to_dict() for acc in accidents]
    
    def get_recent_violations(self, time_window: float = 300.0) -> List[Dict[str, Any]]:
        """获取最近违法"""
        violations = self.violation_detector.get_recent_violations(time_window)
        return [vio.to_dict() for vio in violations]
    
    def get_recent_alerts(self, time_window: float = 300.0) -> List[Dict[str, Any]]:
        """获取最近警报"""
        alerts = self.safety_monitor.get_recent_alerts(time_window)
        return [alert.to_dict() for alert in alerts]
    
    def get_recent_decisions(self, time_window: float = 300.0) -> List[Dict[str, Any]]:
        """获取最近决策"""
        decisions = self.decision_engine.get_recent_decisions(time_window)
        return [dec.to_dict() for dec in decisions]
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            "risk_level": self.current_risk_level.value,
            "traffic_metrics": self.last_traffic_metrics.to_dict() if self.last_traffic_metrics else {},
            "active_accidents": len(self.last_accidents),
            "active_violations": len(self.last_violations),
            "active_alerts": len(self.last_alerts),
            "pending_decisions": len(self.last_decisions),
            "processing": self.processing,
            "last_analysis_time": self.last_analysis_time
        }
    
    def get_comprehensive_statistics(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        return {
            "business_engine": {
                "frame_count": self.frame_count,
                "total_processing_time": self.total_processing_time,
                "avg_processing_time": self.total_processing_time / self.frame_count if self.frame_count > 0 else 0,
                "current_risk_level": self.current_risk_level.value
            },
            "traffic_analysis": self.traffic_analyzer.get_statistics(),
            "accident_detection": self.accident_detector.get_accident_statistics(),
            "violation_detection": self.violation_detector.get_violation_statistics(),
            "safety_monitoring": self.safety_monitor.get_safety_statistics(),
            "decision_making": self.decision_engine.get_decision_statistics()
        }
    
    def predict_congestion(self, prediction_horizon: int = 300) -> Dict[str, Any]:
        """预测拥堵情况"""
        return self.traffic_analyzer.predict_congestion(prediction_horizon)
    
    def get_congestion_hotspots(self, grid_size: int = 10) -> List[Dict[str, Any]]:
        """获取拥堵热点"""
        return self.traffic_analyzer.get_congestion_hotspots(grid_size)
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        
        # 更新各组件配置
        if "traffic" in new_config:
            # 交通分析器配置更新
            pass
        
        if "accident" in new_config:
            # 事故检测器配置更新
            pass
        
        if "violation" in new_config:
            # 违法检测器配置更新
            pass
        
        if "safety" in new_config:
            # 安全监控器配置更新
            pass
        
        if "decision" in new_config:
            # 决策引擎配置更新
            pass
        
        self.logger.info("Business engine configuration updated")
    
    def reset(self):
        """重置业务引擎"""
        # 重置各组件
        self.traffic_analyzer.reset()
        self.accident_detector.reset()
        self.violation_detector.reset()
        self.safety_monitor.reset()
        self.decision_engine.reset()
        
        # 重置统计信息
        self.frame_count = 0
        self.total_processing_time = 0.0
        self.last_analysis_time = time.time()
        
        # 清空结果缓存
        self.last_traffic_metrics = None
        self.last_accidents.clear()
        self.last_violations.clear()
        self.last_alerts.clear()
        self.last_decisions.clear()
        self.current_risk_level = RiskLevel.LOW
        
        self.logger.info("Business engine reset")
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.processing = False
            self.initialized = False
            self.logger.info("Business engine cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self.initialized and not self.processing
