"""
交通流分析器
分析交通流量、密度、速度等交通参数
"""
import logging
import time
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from collections import deque, defaultdict
from dataclasses import dataclass
from enum import Enum

from core.ai.detection import Detection
from core.ai.tracking import Track


class CongestionLevel(str, Enum):
    """拥堵等级枚举"""
    FREE_FLOW = "free_flow"      # 自由流
    LIGHT = "light"              # 轻度拥堵
    MODERATE = "moderate"        # 中度拥堵
    HEAVY = "heavy"              # 重度拥堵
    SEVERE = "severe"            # 严重拥堵


@dataclass
class TrafficMetrics:
    """交通指标数据类"""
    timestamp: float
    vehicle_count: int
    person_count: int
    vehicle_density: float  # 车辆/m²
    person_density: float   # 人/m²
    avg_vehicle_speed: float  # 平均车速 像素/秒
    avg_person_speed: float   # 平均人速 像素/秒
    congestion_level: CongestionLevel
    flow_rate: float  # 流量 车辆/秒
    occupancy_rate: float  # 占有率 %
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp,
            "vehicle_count": self.vehicle_count,
            "person_count": self.person_count,
            "vehicle_density": self.vehicle_density,
            "person_density": self.person_density,
            "avg_vehicle_speed": self.avg_vehicle_speed,
            "avg_person_speed": self.avg_person_speed,
            "congestion_level": self.congestion_level.value,
            "flow_rate": self.flow_rate,
            "occupancy_rate": self.occupancy_rate
        }


class TrafficAnalyzer:
    """交通流分析器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 分析参数
        self.area_m2 = self.config.get("area_m2", 1000.0)  # 监控区域面积
        self.pixel_to_meter = self.config.get("pixel_to_meter", 0.1)  # 像素到米的转换比例
        self.analysis_window = self.config.get("analysis_window", 30)  # 分析窗口大小（帧）
        
        # 拥堵阈值
        self.congestion_thresholds = {
            CongestionLevel.FREE_FLOW: 0.01,    # < 0.01 车辆/m²
            CongestionLevel.LIGHT: 0.02,        # 0.01-0.02 车辆/m²
            CongestionLevel.MODERATE: 0.04,     # 0.02-0.04 车辆/m²
            CongestionLevel.HEAVY: 0.06,        # 0.04-0.06 车辆/m²
            CongestionLevel.SEVERE: float('inf') # > 0.06 车辆/m²
        }
        
        # 历史数据
        self.metrics_history: deque = deque(maxlen=1000)  # 保存最近1000帧的数据
        self.flow_counters: Dict[str, int] = defaultdict(int)  # 流量计数器
        self.last_positions: Dict[int, Tuple[float, float]] = {}  # 上一帧位置
        
        # 统计信息
        self.total_vehicles_passed = 0
        self.total_persons_passed = 0
        self.peak_vehicle_count = 0
        self.peak_person_count = 0
        self.analysis_count = 0
    
    def analyze(self, tracks: List[Track], frame_area: Optional[Tuple[int, int]] = None) -> TrafficMetrics:
        """分析交通流"""
        current_time = time.time()
        
        # 分离车辆和行人轨迹
        vehicle_tracks = [t for t in tracks if t.class_name in ["car", "truck", "bus", "motorcycle"]]
        person_tracks = [t for t in tracks if t.class_name == "person"]
        
        # 计算基础指标
        vehicle_count = len(vehicle_tracks)
        person_count = len(person_tracks)
        
        # 更新峰值统计
        self.peak_vehicle_count = max(self.peak_vehicle_count, vehicle_count)
        self.peak_person_count = max(self.peak_person_count, person_count)
        
        # 计算密度
        vehicle_density = vehicle_count / self.area_m2
        person_density = person_count / self.area_m2
        
        # 计算平均速度
        avg_vehicle_speed = self._calculate_average_speed(vehicle_tracks)
        avg_person_speed = self._calculate_average_speed(person_tracks)
        
        # 计算拥堵等级
        congestion_level = self._calculate_congestion_level(vehicle_density, avg_vehicle_speed)
        
        # 计算流量
        flow_rate = self._calculate_flow_rate(vehicle_tracks)
        
        # 计算占有率
        occupancy_rate = self._calculate_occupancy_rate(vehicle_tracks, frame_area)
        
        # 创建交通指标
        metrics = TrafficMetrics(
            timestamp=current_time,
            vehicle_count=vehicle_count,
            person_count=person_count,
            vehicle_density=vehicle_density,
            person_density=person_density,
            avg_vehicle_speed=avg_vehicle_speed,
            avg_person_speed=avg_person_speed,
            congestion_level=congestion_level,
            flow_rate=flow_rate,
            occupancy_rate=occupancy_rate
        )
        
        # 保存历史数据
        self.metrics_history.append(metrics)
        self.analysis_count += 1
        
        # 更新位置记录
        self._update_position_records(tracks)
        
        return metrics
    
    def _calculate_average_speed(self, tracks: List[Track]) -> float:
        """计算平均速度"""
        if not tracks:
            return 0.0
        
        speeds = []
        for track in tracks:
            speed_pixels = track.get_speed()
            speed_meters = speed_pixels * self.pixel_to_meter
            speeds.append(speed_meters)
        
        return np.mean(speeds) if speeds else 0.0
    
    def _calculate_congestion_level(self, vehicle_density: float, avg_speed: float) -> CongestionLevel:
        """计算拥堵等级"""
        # 基于密度的初步判断
        if vehicle_density < self.congestion_thresholds[CongestionLevel.FREE_FLOW]:
            base_level = CongestionLevel.FREE_FLOW
        elif vehicle_density < self.congestion_thresholds[CongestionLevel.LIGHT]:
            base_level = CongestionLevel.LIGHT
        elif vehicle_density < self.congestion_thresholds[CongestionLevel.MODERATE]:
            base_level = CongestionLevel.MODERATE
        elif vehicle_density < self.congestion_thresholds[CongestionLevel.HEAVY]:
            base_level = CongestionLevel.HEAVY
        else:
            base_level = CongestionLevel.SEVERE
        
        # 结合速度进行调整
        if avg_speed < 1.0:  # 速度很低
            if base_level in [CongestionLevel.FREE_FLOW, CongestionLevel.LIGHT]:
                return CongestionLevel.MODERATE
            elif base_level == CongestionLevel.MODERATE:
                return CongestionLevel.HEAVY
        elif avg_speed > 5.0:  # 速度较高
            if base_level == CongestionLevel.HEAVY:
                return CongestionLevel.MODERATE
            elif base_level == CongestionLevel.SEVERE:
                return CongestionLevel.HEAVY
        
        return base_level
    
    def _calculate_flow_rate(self, vehicle_tracks: List[Track]) -> float:
        """计算流量（车辆/秒）"""
        if len(self.metrics_history) < 2:
            return 0.0
        
        # 计算最近几帧的流量变化
        recent_metrics = list(self.metrics_history)[-10:]  # 最近10帧
        if len(recent_metrics) < 2:
            return 0.0
        
        time_span = recent_metrics[-1].timestamp - recent_metrics[0].timestamp
        if time_span <= 0:
            return 0.0
        
        # 简化的流量计算：基于车辆数量变化
        vehicle_count_change = abs(recent_metrics[-1].vehicle_count - recent_metrics[0].vehicle_count)
        flow_rate = vehicle_count_change / time_span
        
        return flow_rate
    
    def _calculate_occupancy_rate(self, vehicle_tracks: List[Track], 
                                frame_area: Optional[Tuple[int, int]]) -> float:
        """计算占有率"""
        if not vehicle_tracks or not frame_area:
            return 0.0
        
        frame_width, frame_height = frame_area
        total_frame_area = frame_width * frame_height
        
        # 计算所有车辆占用的面积
        occupied_area = 0
        for track in vehicle_tracks:
            if track.last_detection:
                bbox = track.last_detection.bbox
                vehicle_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
                occupied_area += vehicle_area
        
        occupancy_rate = (occupied_area / total_frame_area) * 100
        return min(occupancy_rate, 100.0)  # 限制在100%以内
    
    def _update_position_records(self, tracks: List[Track]):
        """更新位置记录"""
        current_positions = {}
        for track in tracks:
            if track.last_detection:
                current_positions[track.track_id] = track.last_detection.center
        
        self.last_positions = current_positions
    
    def get_traffic_trends(self, time_window: int = 300) -> Dict[str, Any]:
        """获取交通趋势（最近5分钟）"""
        if not self.metrics_history:
            return {}
        
        current_time = time.time()
        recent_metrics = [
            m for m in self.metrics_history 
            if current_time - m.timestamp <= time_window
        ]
        
        if not recent_metrics:
            return {}
        
        # 计算趋势
        vehicle_counts = [m.vehicle_count for m in recent_metrics]
        person_counts = [m.person_count for m in recent_metrics]
        vehicle_densities = [m.vehicle_density for m in recent_metrics]
        avg_speeds = [m.avg_vehicle_speed for m in recent_metrics]
        
        return {
            "time_window": time_window,
            "data_points": len(recent_metrics),
            "vehicle_count": {
                "current": vehicle_counts[-1] if vehicle_counts else 0,
                "avg": np.mean(vehicle_counts),
                "max": np.max(vehicle_counts),
                "min": np.min(vehicle_counts),
                "trend": self._calculate_trend(vehicle_counts)
            },
            "person_count": {
                "current": person_counts[-1] if person_counts else 0,
                "avg": np.mean(person_counts),
                "max": np.max(person_counts),
                "min": np.min(person_counts),
                "trend": self._calculate_trend(person_counts)
            },
            "vehicle_density": {
                "current": vehicle_densities[-1] if vehicle_densities else 0,
                "avg": np.mean(vehicle_densities),
                "max": np.max(vehicle_densities),
                "trend": self._calculate_trend(vehicle_densities)
            },
            "avg_speed": {
                "current": avg_speeds[-1] if avg_speeds else 0,
                "avg": np.mean(avg_speeds),
                "trend": self._calculate_trend(avg_speeds)
            }
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return "stable"
        
        # 简单的线性趋势计算
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.1:
            return "increasing"
        elif slope < -0.1:
            return "decreasing"
        else:
            return "stable"
    
    def get_congestion_hotspots(self, grid_size: int = 10) -> List[Dict[str, Any]]:
        """获取拥堵热点区域"""
        if not self.metrics_history:
            return []
        
        # 简化实现：基于最近的轨迹位置分析
        recent_tracks = []
        for metrics in list(self.metrics_history)[-30:]:  # 最近30帧
            # 这里需要从metrics中获取轨迹信息，简化处理
            pass
        
        # TODO: 实现网格化的热点分析
        hotspots = []
        
        return hotspots
    
    def predict_congestion(self, prediction_horizon: int = 300) -> Dict[str, Any]:
        """预测未来拥堵情况"""
        if len(self.metrics_history) < 10:
            return {"prediction": "insufficient_data"}
        
        recent_metrics = list(self.metrics_history)[-30:]  # 最近30帧数据
        
        # 简单的线性预测
        vehicle_densities = [m.vehicle_density for m in recent_metrics]
        avg_speeds = [m.avg_vehicle_speed for m in recent_metrics]
        
        # 计算趋势
        density_trend = self._calculate_trend(vehicle_densities)
        speed_trend = self._calculate_trend(avg_speeds)
        
        # 预测逻辑
        current_congestion = recent_metrics[-1].congestion_level
        
        prediction = {
            "current_congestion": current_congestion.value,
            "density_trend": density_trend,
            "speed_trend": speed_trend,
            "prediction_horizon": prediction_horizon,
            "predicted_congestion": current_congestion.value,
            "confidence": 0.7,
            "recommendations": []
        }
        
        # 生成建议
        if density_trend == "increasing" and speed_trend == "decreasing":
            prediction["predicted_congestion"] = self._get_next_congestion_level(current_congestion).value
            prediction["recommendations"].append("建议提前疏导交通")
            prediction["recommendations"].append("考虑调整信号灯配时")
        
        return prediction
    
    def _get_next_congestion_level(self, current_level: CongestionLevel) -> CongestionLevel:
        """获取下一个拥堵等级"""
        levels = [
            CongestionLevel.FREE_FLOW,
            CongestionLevel.LIGHT,
            CongestionLevel.MODERATE,
            CongestionLevel.HEAVY,
            CongestionLevel.SEVERE
        ]
        
        try:
            current_index = levels.index(current_level)
            if current_index < len(levels) - 1:
                return levels[current_index + 1]
        except ValueError:
            pass
        
        return current_level
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        return {
            "analysis_count": self.analysis_count,
            "total_vehicles_passed": self.total_vehicles_passed,
            "total_persons_passed": self.total_persons_passed,
            "peak_vehicle_count": self.peak_vehicle_count,
            "peak_person_count": self.peak_person_count,
            "metrics_history_size": len(self.metrics_history),
            "area_m2": self.area_m2,
            "pixel_to_meter": self.pixel_to_meter,
            "congestion_thresholds": {k.value: v for k, v in self.congestion_thresholds.items()}
        }
    
    def reset(self):
        """重置分析器"""
        self.metrics_history.clear()
        self.flow_counters.clear()
        self.last_positions.clear()
        self.total_vehicles_passed = 0
        self.total_persons_passed = 0
        self.peak_vehicle_count = 0
        self.peak_person_count = 0
        self.analysis_count = 0
        self.logger.info("Traffic analyzer reset")
