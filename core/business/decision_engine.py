"""
决策引擎
基于分析结果生成决策和建议
"""
import logging
import time
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .traffic_analyzer import TrafficMetrics, CongestionLevel
from .accident_detector import AccidentEvent, AccidentSeverity
from .violation_detector import ViolationEvent, ViolationSeverity
from .safety_monitor import SafetyAlert, RiskLevel


class ActionType(str, Enum):
    """行动类型枚举"""
    TRAFFIC_CONTROL = "traffic_control"     # 交通控制
    EMERGENCY_RESPONSE = "emergency_response" # 应急响应
    ENFORCEMENT = "enforcement"             # 执法行动
    PUBLIC_NOTIFICATION = "public_notification" # 公众通知
    INFRASTRUCTURE = "infrastructure"       # 基础设施调整


class Priority(str, Enum):
    """优先级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class Decision:
    """决策数据类"""
    decision_id: str
    action_type: ActionType
    priority: Priority
    timestamp: float
    title: str
    description: str
    recommendations: List[str]
    target_location: Optional[tuple] = None
    affected_tracks: List[int] = None
    estimated_impact: str = ""
    execution_time: int = 0  # 预计执行时间（秒）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "decision_id": self.decision_id,
            "action_type": self.action_type.value,
            "priority": self.priority.value,
            "timestamp": self.timestamp,
            "title": self.title,
            "description": self.description,
            "recommendations": self.recommendations,
            "target_location": self.target_location,
            "affected_tracks": self.affected_tracks or [],
            "estimated_impact": self.estimated_impact,
            "execution_time": self.execution_time
        }


class DecisionEngine:
    """决策引擎"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 决策参数
        self.decision_threshold = self.config.get("decision_threshold", 0.7)
        self.max_decisions_per_minute = self.config.get("max_decisions", 10)
        
        # 历史决策
        self.decisions: List[Decision] = []
        self.recent_decisions: List[Decision] = []
        
        # 统计信息
        self.total_decisions = 0
        self.action_type_counts = {action_type: 0 for action_type in ActionType}
        self.priority_counts = {priority: 0 for priority in Priority}
        self.next_decision_id = 1
    
    def make_decisions(self,
                      traffic_metrics: TrafficMetrics,
                      accidents: List[AccidentEvent],
                      violations: List[ViolationEvent],
                      safety_alerts: List[SafetyAlert],
                      risk_level: RiskLevel) -> List[Decision]:
        """生成决策"""
        current_time = time.time()
        decisions = []
        
        # 清理过期的最近决策
        self._cleanup_recent_decisions(current_time)
        
        # 检查决策频率限制
        if len(self.recent_decisions) >= self.max_decisions_per_minute:
            self.logger.warning("Decision rate limit reached")
            return decisions
        
        # 基于交通状况的决策
        traffic_decisions = self._make_traffic_decisions(traffic_metrics, current_time)
        decisions.extend(traffic_decisions)
        
        # 基于事故的决策
        accident_decisions = self._make_accident_decisions(accidents, current_time)
        decisions.extend(accident_decisions)
        
        # 基于违法行为的决策
        violation_decisions = self._make_violation_decisions(violations, current_time)
        decisions.extend(violation_decisions)
        
        # 基于安全警报的决策
        alert_decisions = self._make_alert_decisions(safety_alerts, current_time)
        decisions.extend(alert_decisions)
        
        # 基于风险等级的决策
        risk_decisions = self._make_risk_decisions(risk_level, current_time)
        decisions.extend(risk_decisions)
        
        # 更新记录
        for decision in decisions:
            self.decisions.append(decision)
            self.recent_decisions.append(decision)
            self.total_decisions += 1
            self.action_type_counts[decision.action_type] += 1
            self.priority_counts[decision.priority] += 1
        
        return decisions
    
    def _make_traffic_decisions(self, traffic_metrics: TrafficMetrics, 
                              current_time: float) -> List[Decision]:
        """基于交通状况生成决策"""
        decisions = []
        
        # 拥堵处理决策
        if traffic_metrics.congestion_level in [CongestionLevel.HEAVY, CongestionLevel.SEVERE]:
            decision = Decision(
                decision_id=f"DEC_{self.next_decision_id:06d}",
                action_type=ActionType.TRAFFIC_CONTROL,
                priority=Priority.HIGH if traffic_metrics.congestion_level == CongestionLevel.SEVERE else Priority.MEDIUM,
                timestamp=current_time,
                title="交通拥堵疏导",
                description=f"当前拥堵等级：{traffic_metrics.congestion_level.value}，需要采取疏导措施",
                recommendations=[
                    "调整信号灯配时，延长绿灯时间",
                    "启动可变车道，增加通行能力",
                    "发布路况信息，引导车辆绕行",
                    "派遣交警现场指挥"
                ],
                estimated_impact="预计可减少拥堵30-50%",
                execution_time=300  # 5分钟
            )
            decisions.append(decision)
            self.next_decision_id += 1
        
        # 流量异常决策
        if traffic_metrics.flow_rate > 0.5:  # 流量过大
            decision = Decision(
                decision_id=f"DEC_{self.next_decision_id:06d}",
                action_type=ActionType.PUBLIC_NOTIFICATION,
                priority=Priority.MEDIUM,
                timestamp=current_time,
                title="高流量预警",
                description="检测到异常高的交通流量",
                recommendations=[
                    "通过电子屏发布拥堵预警",
                    "建议市民错峰出行",
                    "启动备用通道"
                ],
                estimated_impact="预计可分散20%的交通流量",
                execution_time=60
            )
            decisions.append(decision)
            self.next_decision_id += 1
        
        return decisions
    
    def _make_accident_decisions(self, accidents: List[AccidentEvent], 
                               current_time: float) -> List[Decision]:
        """基于事故生成决策"""
        decisions = []
        
        for accident in accidents:
            if current_time - accident.timestamp <= 300:  # 5分钟内的事故
                priority = self._accident_severity_to_priority(accident.severity)
                
                decision = Decision(
                    decision_id=f"DEC_{self.next_decision_id:06d}",
                    action_type=ActionType.EMERGENCY_RESPONSE,
                    priority=priority,
                    timestamp=current_time,
                    title=f"{accident.accident_type.value}事故应急响应",
                    description=f"在位置 {accident.location} 发生{accident.severity.value}事故",
                    recommendations=self._get_accident_recommendations(accident),
                    target_location=accident.location,
                    affected_tracks=accident.involved_tracks,
                    estimated_impact="确保事故现场安全，恢复交通秩序",
                    execution_time=self._get_accident_response_time(accident.severity)
                )
                decisions.append(decision)
                self.next_decision_id += 1
        
        return decisions
    
    def _make_violation_decisions(self, violations: List[ViolationEvent], 
                                current_time: float) -> List[Decision]:
        """基于违法行为生成决策"""
        decisions = []
        
        # 统计严重违法行为
        serious_violations = [v for v in violations 
                            if v.severity == ViolationSeverity.SERIOUS and 
                            current_time - v.timestamp <= 600]  # 10分钟内
        
        if len(serious_violations) >= 3:
            decision = Decision(
                decision_id=f"DEC_{self.next_decision_id:06d}",
                action_type=ActionType.ENFORCEMENT,
                priority=Priority.HIGH,
                timestamp=current_time,
                title="加强执法行动",
                description=f"检测到{len(serious_violations)}起严重违法行为",
                recommendations=[
                    "增派执法人员到现场",
                    "设置临时检查点",
                    "启动重点违法行为专项整治",
                    "通过广播提醒驾驶员遵守交规"
                ],
                estimated_impact="预计可减少违法行为60%",
                execution_time=1800  # 30分钟
            )
            decisions.append(decision)
            self.next_decision_id += 1
        
        return decisions
    
    def _make_alert_decisions(self, safety_alerts: List[SafetyAlert], 
                            current_time: float) -> List[Decision]:
        """基于安全警报生成决策"""
        decisions = []
        
        for alert in safety_alerts:
            if current_time - alert.timestamp <= 60:  # 1分钟内的警报
                decision = Decision(
                    decision_id=f"DEC_{self.next_decision_id:06d}",
                    action_type=ActionType.TRAFFIC_CONTROL,
                    priority=self._risk_level_to_priority(alert.risk_level),
                    timestamp=current_time,
                    title=f"安全警报响应：{alert.alert_type}",
                    description=alert.description,
                    recommendations=alert.recommendations,
                    target_location=alert.location,
                    affected_tracks=alert.affected_tracks,
                    estimated_impact="提升区域安全水平",
                    execution_time=180  # 3分钟
                )
                decisions.append(decision)
                self.next_decision_id += 1
        
        return decisions
    
    def _make_risk_decisions(self, risk_level: RiskLevel, 
                           current_time: float) -> List[Decision]:
        """基于风险等级生成决策"""
        decisions = []
        
        if risk_level == RiskLevel.CRITICAL:
            decision = Decision(
                decision_id=f"DEC_{self.next_decision_id:06d}",
                action_type=ActionType.EMERGENCY_RESPONSE,
                priority=Priority.URGENT,
                timestamp=current_time,
                title="启动应急预案",
                description="当前风险等级为极高，需要立即采取应急措施",
                recommendations=[
                    "启动一级应急响应",
                    "调集所有可用资源",
                    "实施交通管制",
                    "发布紧急公告"
                ],
                estimated_impact="最大程度降低安全风险",
                execution_time=120  # 2分钟
            )
            decisions.append(decision)
            self.next_decision_id += 1
        
        elif risk_level == RiskLevel.HIGH:
            decision = Decision(
                decision_id=f"DEC_{self.next_decision_id:06d}",
                action_type=ActionType.TRAFFIC_CONTROL,
                priority=Priority.HIGH,
                timestamp=current_time,
                title="提升警戒等级",
                description="当前风险等级较高，需要加强监控和管理",
                recommendations=[
                    "增加巡逻频次",
                    "加强现场管控",
                    "准备应急资源",
                    "密切监控态势"
                ],
                estimated_impact="有效控制风险升级",
                execution_time=300  # 5分钟
            )
            decisions.append(decision)
            self.next_decision_id += 1
        
        return decisions
    
    def _accident_severity_to_priority(self, severity: AccidentSeverity) -> Priority:
        """将事故严重程度转换为优先级"""
        mapping = {
            AccidentSeverity.MINOR: Priority.MEDIUM,
            AccidentSeverity.MODERATE: Priority.HIGH,
            AccidentSeverity.SERIOUS: Priority.URGENT,
            AccidentSeverity.FATAL: Priority.URGENT
        }
        return mapping.get(severity, Priority.MEDIUM)
    
    def _risk_level_to_priority(self, risk_level: RiskLevel) -> Priority:
        """将风险等级转换为优先级"""
        mapping = {
            RiskLevel.LOW: Priority.LOW,
            RiskLevel.MODERATE: Priority.MEDIUM,
            RiskLevel.HIGH: Priority.HIGH,
            RiskLevel.CRITICAL: Priority.URGENT
        }
        return mapping.get(risk_level, Priority.MEDIUM)
    
    def _get_accident_recommendations(self, accident: AccidentEvent) -> List[str]:
        """获取事故处理建议"""
        base_recommendations = [
            "立即派遣救援人员",
            "确保现场安全",
            "疏导周边交通"
        ]
        
        if accident.severity in [AccidentSeverity.SERIOUS, AccidentSeverity.FATAL]:
            base_recommendations.extend([
                "联系医疗急救",
                "通知事故调查组",
                "设置大范围警戒区"
            ])
        
        return base_recommendations
    
    def _get_accident_response_time(self, severity: AccidentSeverity) -> int:
        """获取事故响应时间"""
        time_mapping = {
            AccidentSeverity.MINOR: 600,    # 10分钟
            AccidentSeverity.MODERATE: 300, # 5分钟
            AccidentSeverity.SERIOUS: 180,  # 3分钟
            AccidentSeverity.FATAL: 120     # 2分钟
        }
        return time_mapping.get(severity, 300)
    
    def _cleanup_recent_decisions(self, current_time: float):
        """清理过期的最近决策"""
        self.recent_decisions = [
            d for d in self.recent_decisions 
            if current_time - d.timestamp <= 60  # 保留1分钟内的决策
        ]
    
    def get_recent_decisions(self, time_window: float = 300.0) -> List[Decision]:
        """获取最近的决策"""
        current_time = time.time()
        recent = []
        
        for decision in reversed(self.decisions):
            if current_time - decision.timestamp <= time_window:
                recent.append(decision)
            else:
                break
        
        return list(reversed(recent))
    
    def get_decision_statistics(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        return {
            "total_decisions": self.total_decisions,
            "action_type_counts": {k.value: v for k, v in self.action_type_counts.items()},
            "priority_counts": {k.value: v for k, v in self.priority_counts.items()},
            "recent_decisions_count": len(self.recent_decisions),
            "config": self.config
        }
    
    def reset(self):
        """重置决策引擎"""
        self.decisions.clear()
        self.recent_decisions.clear()
        self.total_decisions = 0
        self.action_type_counts = {action_type: 0 for action_type in ActionType}
        self.priority_counts = {priority: 0 for priority in Priority}
        self.next_decision_id = 1
        self.logger.info("Decision engine reset")
