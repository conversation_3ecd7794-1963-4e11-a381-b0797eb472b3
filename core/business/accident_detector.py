"""
事故检测器
检测交通事故并评估严重程度
"""
import logging
import time
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from collections import deque, defaultdict
from dataclasses import dataclass
from enum import Enum

from core.ai.detection import Detection
from core.ai.tracking import Track
from core.ai.behavior_analyzer import BehaviorEvent, BehaviorType


class AccidentType(str, Enum):
    """事故类型枚举"""
    COLLISION = "collision"           # 碰撞事故
    ROLLOVER = "rollover"            # 翻车事故
    STATIONARY = "stationary"        # 车辆异常停止
    PEDESTRIAN_ACCIDENT = "pedestrian_accident"  # 行人事故
    MULTI_VEHICLE = "multi_vehicle"  # 多车事故
    UNKNOWN = "unknown"              # 未知类型


class AccidentSeverity(str, Enum):
    """事故严重程度枚举"""
    MINOR = "minor"        # 轻微事故
    MODERATE = "moderate"  # 一般事故
    SERIOUS = "serious"    # 严重事故
    FATAL = "fatal"        # 特重大事故


@dataclass
class AccidentEvent:
    """事故事件数据类"""
    accident_id: str
    accident_type: AccidentType
    severity: AccidentSeverity
    confidence: float
    timestamp: float
    location: Tuple[float, float]
    involved_tracks: List[int]
    description: str
    evidence: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "accident_id": self.accident_id,
            "accident_type": self.accident_type.value,
            "severity": self.severity.value,
            "confidence": self.confidence,
            "timestamp": self.timestamp,
            "location": self.location,
            "involved_tracks": self.involved_tracks,
            "description": self.description,
            "evidence": self.evidence
        }


class AccidentDetector:
    """事故检测器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 检测参数
        self.collision_distance_threshold = self.config.get("collision_distance", 50.0)  # 像素
        self.collision_speed_threshold = self.config.get("collision_speed", 30.0)  # 像素/秒
        self.stationary_time_threshold = self.config.get("stationary_time", 60.0)  # 秒
        self.speed_drop_threshold = self.config.get("speed_drop", 0.7)  # 速度下降比例
        self.trajectory_deviation_threshold = self.config.get("trajectory_deviation", 100.0)  # 像素
        
        # 历史数据
        self.track_histories: Dict[int, deque] = defaultdict(lambda: deque(maxlen=60))  # 60帧历史
        self.accident_events: List[AccidentEvent] = []
        self.recent_accidents: deque = deque(maxlen=50)
        self.stationary_tracks: Dict[int, float] = {}  # 轨迹ID -> 开始静止时间
        
        # 统计信息
        self.total_accidents = 0
        self.accident_type_counts = defaultdict(int)
        self.severity_counts = defaultdict(int)
        self.next_accident_id = 1
    
    def detect(self, tracks: List[Track], behavior_events: List[BehaviorEvent]) -> List[AccidentEvent]:
        """检测事故"""
        current_time = time.time()
        detected_accidents = []
        
        # 更新轨迹历史
        self._update_track_histories(tracks, current_time)
        
        # 基于行为事件的事故检测
        behavior_accidents = self._detect_from_behaviors(behavior_events, current_time)
        detected_accidents.extend(behavior_accidents)
        
        # 基于轨迹分析的事故检测
        trajectory_accidents = self._detect_from_trajectories(tracks, current_time)
        detected_accidents.extend(trajectory_accidents)
        
        # 基于交互分析的事故检测
        interaction_accidents = self._detect_from_interactions(tracks, current_time)
        detected_accidents.extend(interaction_accidents)
        
        # 过滤重复事故
        detected_accidents = self._filter_duplicate_accidents(detected_accidents)
        
        # 更新事故记录
        for accident in detected_accidents:
            self.accident_events.append(accident)
            self.recent_accidents.append(accident)
            self.total_accidents += 1
            self.accident_type_counts[accident.accident_type] += 1
            self.severity_counts[accident.severity] += 1
        
        return detected_accidents
    
    def _update_track_histories(self, tracks: List[Track], current_time: float):
        """更新轨迹历史"""
        for track in tracks:
            if track.last_detection:
                history_entry = {
                    "timestamp": current_time,
                    "position": track.last_detection.center,
                    "velocity": track.velocity,
                    "speed": track.get_speed(),
                    "bbox": track.last_detection.bbox,
                    "confidence": track.last_detection.confidence
                }
                self.track_histories[track.track_id].append(history_entry)
    
    def _detect_from_behaviors(self, behavior_events: List[BehaviorEvent], 
                             current_time: float) -> List[AccidentEvent]:
        """基于行为事件检测事故"""
        accidents = []
        
        for event in behavior_events:
            if event.behavior_type == BehaviorType.COLLISION:
                # 碰撞事故
                accident = AccidentEvent(
                    accident_id=f"ACC_{self.next_accident_id:06d}",
                    accident_type=AccidentType.COLLISION,
                    severity=self._assess_collision_severity(event),
                    confidence=event.confidence,
                    timestamp=current_time,
                    location=event.location,
                    involved_tracks=[event.track_id],
                    description=f"检测到碰撞事故，涉及轨迹 {event.track_id}",
                    evidence={
                        "behavior_event": event.to_dict(),
                        "detection_method": "behavior_analysis"
                    }
                )
                accidents.append(accident)
                self.next_accident_id += 1
            
            elif event.behavior_type == BehaviorType.STATIONARY:
                # 异常停车事故
                if event.metadata.get("duration", 0) > self.stationary_time_threshold:
                    accident = AccidentEvent(
                        accident_id=f"ACC_{self.next_accident_id:06d}",
                        accident_type=AccidentType.STATIONARY,
                        severity=AccidentSeverity.MINOR,
                        confidence=0.7,
                        timestamp=current_time,
                        location=event.location,
                        involved_tracks=[event.track_id],
                        description=f"车辆异常停止超过 {event.metadata.get('duration', 0):.1f} 秒",
                        evidence={
                            "behavior_event": event.to_dict(),
                            "detection_method": "stationary_analysis"
                        }
                    )
                    accidents.append(accident)
                    self.next_accident_id += 1
        
        return accidents
    
    def _detect_from_trajectories(self, tracks: List[Track], 
                                current_time: float) -> List[AccidentEvent]:
        """基于轨迹分析检测事故"""
        accidents = []
        
        for track in tracks:
            history = self.track_histories[track.track_id]
            if len(history) < 10:  # 需要足够的历史数据
                continue
            
            # 检测急停事故
            sudden_stop_accident = self._detect_sudden_stop(track, history, current_time)
            if sudden_stop_accident:
                accidents.append(sudden_stop_accident)
            
            # 检测轨迹异常
            trajectory_accident = self._detect_trajectory_anomaly(track, history, current_time)
            if trajectory_accident:
                accidents.append(trajectory_accident)
        
        return accidents
    
    def _detect_sudden_stop(self, track: Track, history: deque, 
                          current_time: float) -> Optional[AccidentEvent]:
        """检测急停事故"""
        if len(history) < 5:
            return None
        
        recent_speeds = [entry["speed"] for entry in list(history)[-5:]]
        
        # 检查速度是否急剧下降
        if len(recent_speeds) >= 2:
            initial_speed = recent_speeds[0]
            final_speed = recent_speeds[-1]
            
            if (initial_speed > 20 and  # 初始速度较高
                final_speed < 5 and     # 最终速度很低
                (initial_speed - final_speed) / initial_speed > self.speed_drop_threshold):
                
                return AccidentEvent(
                    accident_id=f"ACC_{self.next_accident_id:06d}",
                    accident_type=AccidentType.COLLISION,
                    severity=self._assess_speed_drop_severity(initial_speed, final_speed),
                    confidence=0.8,
                    timestamp=current_time,
                    location=track.last_detection.center,
                    involved_tracks=[track.track_id],
                    description=f"检测到急停事故，速度从 {initial_speed:.1f} 降至 {final_speed:.1f}",
                    evidence={
                        "initial_speed": initial_speed,
                        "final_speed": final_speed,
                        "speed_drop_ratio": (initial_speed - final_speed) / initial_speed,
                        "detection_method": "sudden_stop_analysis"
                    }
                )
        
        return None
    
    def _detect_trajectory_anomaly(self, track: Track, history: deque, 
                                 current_time: float) -> Optional[AccidentEvent]:
        """检测轨迹异常"""
        if len(history) < 10:
            return None
        
        positions = [entry["position"] for entry in history]
        
        # 计算轨迹的平滑度
        deviations = []
        for i in range(2, len(positions)):
            # 计算点到直线的距离
            p1, p2, p3 = positions[i-2], positions[i-1], positions[i]
            deviation = self._point_to_line_distance(p3, p1, p2)
            deviations.append(deviation)
        
        if deviations:
            max_deviation = max(deviations)
            avg_deviation = np.mean(deviations)
            
            if max_deviation > self.trajectory_deviation_threshold:
                return AccidentEvent(
                    accident_id=f"ACC_{self.next_accident_id:06d}",
                    accident_type=AccidentType.UNKNOWN,
                    severity=AccidentSeverity.MODERATE,
                    confidence=0.6,
                    timestamp=current_time,
                    location=track.last_detection.center,
                    involved_tracks=[track.track_id],
                    description=f"检测到轨迹异常，最大偏差 {max_deviation:.1f} 像素",
                    evidence={
                        "max_deviation": max_deviation,
                        "avg_deviation": avg_deviation,
                        "detection_method": "trajectory_analysis"
                    }
                )
        
        return None
    
    def _detect_from_interactions(self, tracks: List[Track], 
                                current_time: float) -> List[AccidentEvent]:
        """基于轨迹交互检测事故"""
        accidents = []
        
        # 检测多车事故
        for i, track1 in enumerate(tracks):
            for j, track2 in enumerate(tracks[i+1:], i+1):
                if not (track1.last_detection and track2.last_detection):
                    continue
                
                # 计算距离
                distance = track1.last_detection.distance_to(track2.last_detection)
                
                if distance < self.collision_distance_threshold:
                    # 检查相对速度
                    v1 = np.array(track1.velocity)
                    v2 = np.array(track2.velocity)
                    relative_speed = np.linalg.norm(v1 - v2)
                    
                    if relative_speed > self.collision_speed_threshold:
                        accident = AccidentEvent(
                            accident_id=f"ACC_{self.next_accident_id:06d}",
                            accident_type=AccidentType.MULTI_VEHICLE,
                            severity=self._assess_multi_vehicle_severity(track1, track2, relative_speed),
                            confidence=0.85,
                            timestamp=current_time,
                            location=track1.last_detection.center,
                            involved_tracks=[track1.track_id, track2.track_id],
                            description=f"检测到多车事故，涉及轨迹 {track1.track_id} 和 {track2.track_id}",
                            evidence={
                                "distance": distance,
                                "relative_speed": relative_speed,
                                "track1_speed": track1.get_speed(),
                                "track2_speed": track2.get_speed(),
                                "detection_method": "interaction_analysis"
                            }
                        )
                        accidents.append(accident)
                        self.next_accident_id += 1
        
        return accidents
    
    def _assess_collision_severity(self, event: BehaviorEvent) -> AccidentSeverity:
        """评估碰撞事故严重程度"""
        confidence = event.confidence
        relative_velocity = event.metadata.get("relative_velocity", 0)
        
        if relative_velocity > 50 or confidence > 0.9:
            return AccidentSeverity.SERIOUS
        elif relative_velocity > 30 or confidence > 0.8:
            return AccidentSeverity.MODERATE
        else:
            return AccidentSeverity.MINOR
    
    def _assess_speed_drop_severity(self, initial_speed: float, final_speed: float) -> AccidentSeverity:
        """评估急停事故严重程度"""
        speed_drop = initial_speed - final_speed
        
        if speed_drop > 50:
            return AccidentSeverity.SERIOUS
        elif speed_drop > 30:
            return AccidentSeverity.MODERATE
        else:
            return AccidentSeverity.MINOR
    
    def _assess_multi_vehicle_severity(self, track1: Track, track2: Track, 
                                     relative_speed: float) -> AccidentSeverity:
        """评估多车事故严重程度"""
        # 考虑车辆类型和相对速度
        vehicle_types = [track1.class_name, track2.class_name]
        
        # 大型车辆事故更严重
        if "truck" in vehicle_types or "bus" in vehicle_types:
            if relative_speed > 40:
                return AccidentSeverity.SERIOUS
            else:
                return AccidentSeverity.MODERATE
        else:
            if relative_speed > 60:
                return AccidentSeverity.SERIOUS
            elif relative_speed > 40:
                return AccidentSeverity.MODERATE
            else:
                return AccidentSeverity.MINOR
    
    def _point_to_line_distance(self, point: Tuple[float, float], 
                              line_start: Tuple[float, float], 
                              line_end: Tuple[float, float]) -> float:
        """计算点到直线的距离"""
        x0, y0 = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        # 直线长度
        line_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
        if line_length == 0:
            return np.sqrt((x0 - x1)**2 + (y0 - y1)**2)
        
        # 点到直线的距离
        distance = abs((y2 - y1) * x0 - (x2 - x1) * y0 + x2 * y1 - y2 * x1) / line_length
        return distance
    
    def _filter_duplicate_accidents(self, accidents: List[AccidentEvent]) -> List[AccidentEvent]:
        """过滤重复事故"""
        filtered = []
        
        for accident in accidents:
            is_duplicate = False
            
            # 检查最近的事故记录
            for recent_accident in list(self.recent_accidents)[-10:]:
                if (abs(recent_accident.timestamp - accident.timestamp) < 30 and  # 30秒内
                    recent_accident.accident_type == accident.accident_type and
                    self._locations_close(recent_accident.location, accident.location, 100)):  # 100像素内
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(accident)
        
        return filtered
    
    def _locations_close(self, loc1: Tuple[float, float], loc2: Tuple[float, float], 
                        threshold: float) -> bool:
        """检查两个位置是否接近"""
        distance = np.sqrt((loc1[0] - loc2[0])**2 + (loc1[1] - loc2[1])**2)
        return distance < threshold
    
    def get_recent_accidents(self, time_window: float = 300.0) -> List[AccidentEvent]:
        """获取最近的事故"""
        current_time = time.time()
        recent = []
        
        for accident in reversed(self.accident_events):
            if current_time - accident.timestamp <= time_window:
                recent.append(accident)
            else:
                break
        
        return list(reversed(recent))
    
    def get_accident_statistics(self) -> Dict[str, Any]:
        """获取事故统计信息"""
        return {
            "total_accidents": self.total_accidents,
            "accident_type_counts": dict(self.accident_type_counts),
            "severity_counts": dict(self.severity_counts),
            "recent_accidents_count": len(self.recent_accidents),
            "active_tracks": len(self.track_histories),
            "config": self.config
        }
    
    def reset(self):
        """重置检测器"""
        self.track_histories.clear()
        self.accident_events.clear()
        self.recent_accidents.clear()
        self.stationary_tracks.clear()
        self.total_accidents = 0
        self.accident_type_counts.clear()
        self.severity_counts.clear()
        self.next_accident_id = 1
        self.logger.info("Accident detector reset")
