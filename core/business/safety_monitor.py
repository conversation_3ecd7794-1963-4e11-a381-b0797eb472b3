"""
安全监控器
监控交通安全状况，评估风险等级
"""
import logging
import time
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from collections import deque, defaultdict
from dataclasses import dataclass
from enum import Enum

from core.ai.tracking import Track
from .traffic_analyzer import TrafficMetrics, CongestionLevel
from .accident_detector import AccidentEvent, AccidentSeverity
from .violation_detector import ViolationEvent, ViolationSeverity


class RiskLevel(str, Enum):
    """风险等级枚举"""
    LOW = "low"           # 低风险
    MODERATE = "moderate" # 中等风险
    HIGH = "high"         # 高风险
    CRITICAL = "critical" # 极高风险


@dataclass
class SafetyAlert:
    """安全警报数据类"""
    alert_id: str
    alert_type: str
    risk_level: RiskLevel
    timestamp: float
    location: Tuple[float, float]
    description: str
    recommendations: List[str]
    affected_tracks: List[int]
    evidence: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "alert_type": self.alert_type,
            "risk_level": self.risk_level.value,
            "timestamp": self.timestamp,
            "location": self.location,
            "description": self.description,
            "recommendations": self.recommendations,
            "affected_tracks": self.affected_tracks,
            "evidence": self.evidence
        }


class SafetyMonitor:
    """安全监控器"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 监控参数
        self.risk_assessment_window = self.config.get("risk_window", 60)  # 风险评估时间窗口（秒）
        self.alert_cooldown = self.config.get("alert_cooldown", 30)  # 警报冷却时间（秒）
        
        # 风险权重
        self.risk_weights = {
            "traffic_density": 0.3,
            "accident_severity": 0.4,
            "violation_frequency": 0.2,
            "weather_condition": 0.1
        }
        
        # 历史数据
        self.safety_alerts: List[SafetyAlert] = []
        self.recent_alerts: deque = deque(maxlen=50)
        self.risk_history: deque = deque(maxlen=100)
        self.last_alert_time: Dict[str, float] = {}
        
        # 统计信息
        self.total_alerts = 0
        self.alert_type_counts = defaultdict(int)
        self.risk_level_counts = defaultdict(int)
        self.next_alert_id = 1
    
    def assess_safety(self, 
                     traffic_metrics: TrafficMetrics,
                     recent_accidents: List[AccidentEvent],
                     recent_violations: List[ViolationEvent],
                     tracks: List[Track]) -> Tuple[RiskLevel, List[SafetyAlert]]:
        """评估安全状况"""
        current_time = time.time()
        
        # 计算综合风险等级
        risk_level = self._calculate_risk_level(
            traffic_metrics, recent_accidents, recent_violations
        )
        
        # 生成安全警报
        alerts = []
        
        # 交通密度警报
        density_alerts = self._check_density_alerts(traffic_metrics, current_time)
        alerts.extend(density_alerts)
        
        # 事故风险警报
        accident_alerts = self._check_accident_alerts(recent_accidents, current_time)
        alerts.extend(accident_alerts)
        
        # 违法行为警报
        violation_alerts = self._check_violation_alerts(recent_violations, current_time)
        alerts.extend(violation_alerts)
        
        # 轨迹风险警报
        trajectory_alerts = self._check_trajectory_alerts(tracks, current_time)
        alerts.extend(trajectory_alerts)
        
        # 过滤重复警报
        alerts = self._filter_duplicate_alerts(alerts)
        
        # 更新记录
        self.risk_history.append({
            "timestamp": current_time,
            "risk_level": risk_level,
            "traffic_density": traffic_metrics.vehicle_density,
            "accident_count": len(recent_accidents),
            "violation_count": len(recent_violations)
        })
        
        for alert in alerts:
            self.safety_alerts.append(alert)
            self.recent_alerts.append(alert)
            self.total_alerts += 1
            self.alert_type_counts[alert.alert_type] += 1
            self.risk_level_counts[alert.risk_level] += 1
        
        return risk_level, alerts
    
    def _calculate_risk_level(self, 
                            traffic_metrics: TrafficMetrics,
                            recent_accidents: List[AccidentEvent],
                            recent_violations: List[ViolationEvent]) -> RiskLevel:
        """计算综合风险等级"""
        risk_score = 0.0
        
        # 交通密度风险
        density_risk = self._assess_density_risk(traffic_metrics)
        risk_score += density_risk * self.risk_weights["traffic_density"]
        
        # 事故风险
        accident_risk = self._assess_accident_risk(recent_accidents)
        risk_score += accident_risk * self.risk_weights["accident_severity"]
        
        # 违法风险
        violation_risk = self._assess_violation_risk(recent_violations)
        risk_score += violation_risk * self.risk_weights["violation_frequency"]
        
        # 天气风险（简化）
        weather_risk = 0.0  # TODO: 集成天气数据
        risk_score += weather_risk * self.risk_weights["weather_condition"]
        
        # 转换为风险等级
        if risk_score >= 0.8:
            return RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            return RiskLevel.HIGH
        elif risk_score >= 0.4:
            return RiskLevel.MODERATE
        else:
            return RiskLevel.LOW
    
    def _assess_density_risk(self, traffic_metrics: TrafficMetrics) -> float:
        """评估密度风险"""
        congestion_level = traffic_metrics.congestion_level
        
        risk_map = {
            CongestionLevel.FREE_FLOW: 0.1,
            CongestionLevel.LIGHT: 0.3,
            CongestionLevel.MODERATE: 0.5,
            CongestionLevel.HEAVY: 0.7,
            CongestionLevel.SEVERE: 0.9
        }
        
        return risk_map.get(congestion_level, 0.5)
    
    def _assess_accident_risk(self, recent_accidents: List[AccidentEvent]) -> float:
        """评估事故风险"""
        if not recent_accidents:
            return 0.0
        
        # 基于事故数量和严重程度
        severity_scores = {
            AccidentSeverity.MINOR: 0.2,
            AccidentSeverity.MODERATE: 0.5,
            AccidentSeverity.SERIOUS: 0.8,
            AccidentSeverity.FATAL: 1.0
        }
        
        total_score = sum(severity_scores.get(acc.severity, 0.5) for acc in recent_accidents)
        normalized_score = min(total_score / 3.0, 1.0)  # 归一化到0-1
        
        return normalized_score
    
    def _assess_violation_risk(self, recent_violations: List[ViolationEvent]) -> float:
        """评估违法风险"""
        if not recent_violations:
            return 0.0
        
        # 基于违法数量和严重程度
        severity_scores = {
            ViolationSeverity.MINOR: 0.1,
            ViolationSeverity.MODERATE: 0.3,
            ViolationSeverity.SERIOUS: 0.6
        }
        
        total_score = sum(severity_scores.get(vio.severity, 0.3) for vio in recent_violations)
        normalized_score = min(total_score / 5.0, 1.0)  # 归一化到0-1
        
        return normalized_score
    
    def _check_density_alerts(self, traffic_metrics: TrafficMetrics, 
                            current_time: float) -> List[SafetyAlert]:
        """检查密度相关警报"""
        alerts = []
        
        if traffic_metrics.congestion_level in [CongestionLevel.HEAVY, CongestionLevel.SEVERE]:
            if self._should_generate_alert("high_density", current_time):
                alert = SafetyAlert(
                    alert_id=f"ALERT_{self.next_alert_id:06d}",
                    alert_type="high_density",
                    risk_level=RiskLevel.HIGH if traffic_metrics.congestion_level == CongestionLevel.HEAVY else RiskLevel.CRITICAL,
                    timestamp=current_time,
                    location=(0, 0),  # 整个区域
                    description=f"交通密度过高：{traffic_metrics.congestion_level.value}",
                    recommendations=[
                        "建议启动交通疏导措施",
                        "调整信号灯配时",
                        "引导车辆绕行"
                    ],
                    affected_tracks=[],
                    evidence={
                        "vehicle_density": traffic_metrics.vehicle_density,
                        "congestion_level": traffic_metrics.congestion_level.value,
                        "vehicle_count": traffic_metrics.vehicle_count
                    }
                )
                alerts.append(alert)
                self.next_alert_id += 1
        
        return alerts
    
    def _check_accident_alerts(self, recent_accidents: List[AccidentEvent], 
                             current_time: float) -> List[SafetyAlert]:
        """检查事故相关警报"""
        alerts = []
        
        for accident in recent_accidents:
            if current_time - accident.timestamp <= 60:  # 1分钟内的事故
                alert = SafetyAlert(
                    alert_id=f"ALERT_{self.next_alert_id:06d}",
                    alert_type="accident_detected",
                    risk_level=self._accident_severity_to_risk(accident.severity),
                    timestamp=current_time,
                    location=accident.location,
                    description=f"检测到{accident.accident_type.value}事故",
                    recommendations=[
                        "立即派遣救援人员",
                        "疏导周边交通",
                        "设置安全警示"
                    ],
                    affected_tracks=accident.involved_tracks,
                    evidence={
                        "accident_id": accident.accident_id,
                        "accident_type": accident.accident_type.value,
                        "severity": accident.severity.value,
                        "confidence": accident.confidence
                    }
                )
                alerts.append(alert)
                self.next_alert_id += 1
        
        return alerts
    
    def _check_violation_alerts(self, recent_violations: List[ViolationEvent], 
                              current_time: float) -> List[SafetyAlert]:
        """检查违法相关警报"""
        alerts = []
        
        # 统计严重违法行为
        serious_violations = [v for v in recent_violations 
                            if v.severity == ViolationSeverity.SERIOUS and 
                            current_time - v.timestamp <= 300]  # 5分钟内
        
        if len(serious_violations) >= 3:  # 5分钟内3起严重违法
            if self._should_generate_alert("high_violation_rate", current_time):
                alert = SafetyAlert(
                    alert_id=f"ALERT_{self.next_alert_id:06d}",
                    alert_type="high_violation_rate",
                    risk_level=RiskLevel.HIGH,
                    timestamp=current_time,
                    location=(0, 0),
                    description=f"严重违法行为频发：{len(serious_violations)}起",
                    recommendations=[
                        "加强执法力度",
                        "增设警示标志",
                        "实施重点监控"
                    ],
                    affected_tracks=[v.track_id for v in serious_violations],
                    evidence={
                        "violation_count": len(serious_violations),
                        "time_window": 300,
                        "violation_types": [v.violation_type.value for v in serious_violations]
                    }
                )
                alerts.append(alert)
                self.next_alert_id += 1
        
        return alerts
    
    def _check_trajectory_alerts(self, tracks: List[Track], 
                               current_time: float) -> List[SafetyAlert]:
        """检查轨迹相关警报"""
        alerts = []
        
        # 检查高风险轨迹交汇
        risky_intersections = self._find_risky_intersections(tracks)
        
        for intersection in risky_intersections:
            if self._should_generate_alert("trajectory_intersection", current_time):
                alert = SafetyAlert(
                    alert_id=f"ALERT_{self.next_alert_id:06d}",
                    alert_type="trajectory_intersection",
                    risk_level=RiskLevel.MODERATE,
                    timestamp=current_time,
                    location=intersection["location"],
                    description="检测到高风险轨迹交汇",
                    recommendations=[
                        "密切监控该区域",
                        "准备应急响应",
                        "考虑交通引导"
                    ],
                    affected_tracks=intersection["track_ids"],
                    evidence={
                        "intersection_point": intersection["location"],
                        "relative_speed": intersection["relative_speed"],
                        "collision_risk": intersection["risk_score"]
                    }
                )
                alerts.append(alert)
                self.next_alert_id += 1
        
        return alerts
    
    def _find_risky_intersections(self, tracks: List[Track]) -> List[Dict[str, Any]]:
        """查找高风险轨迹交汇点"""
        risky_intersections = []
        
        for i, track1 in enumerate(tracks):
            for j, track2 in enumerate(tracks[i+1:], i+1):
                if not (track1.last_detection and track2.last_detection):
                    continue
                
                # 计算轨迹交汇风险
                distance = track1.last_detection.distance_to(track2.last_detection)
                relative_velocity = np.linalg.norm(
                    np.array(track1.velocity) - np.array(track2.velocity)
                )
                
                # 简化的风险评估
                if distance < 100 and relative_velocity > 30:
                    risk_score = (100 - distance) / 100 * relative_velocity / 100
                    
                    if risk_score > 0.5:
                        risky_intersections.append({
                            "location": track1.last_detection.center,
                            "track_ids": [track1.track_id, track2.track_id],
                            "relative_speed": relative_velocity,
                            "distance": distance,
                            "risk_score": risk_score
                        })
        
        return risky_intersections
    
    def _accident_severity_to_risk(self, severity: AccidentSeverity) -> RiskLevel:
        """将事故严重程度转换为风险等级"""
        mapping = {
            AccidentSeverity.MINOR: RiskLevel.MODERATE,
            AccidentSeverity.MODERATE: RiskLevel.HIGH,
            AccidentSeverity.SERIOUS: RiskLevel.CRITICAL,
            AccidentSeverity.FATAL: RiskLevel.CRITICAL
        }
        return mapping.get(severity, RiskLevel.MODERATE)
    
    def _should_generate_alert(self, alert_type: str, current_time: float) -> bool:
        """检查是否应该生成警报（避免重复）"""
        last_time = self.last_alert_time.get(alert_type, 0)
        if current_time - last_time >= self.alert_cooldown:
            self.last_alert_time[alert_type] = current_time
            return True
        return False
    
    def _filter_duplicate_alerts(self, alerts: List[SafetyAlert]) -> List[SafetyAlert]:
        """过滤重复警报"""
        filtered = []
        
        for alert in alerts:
            is_duplicate = False
            
            for recent_alert in list(self.recent_alerts)[-10:]:
                if (recent_alert.alert_type == alert.alert_type and
                    abs(recent_alert.timestamp - alert.timestamp) < self.alert_cooldown):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                filtered.append(alert)
        
        return filtered
    
    def get_current_risk_level(self) -> RiskLevel:
        """获取当前风险等级"""
        if self.risk_history:
            return self.risk_history[-1]["risk_level"]
        return RiskLevel.LOW
    
    def get_recent_alerts(self, time_window: float = 300.0) -> List[SafetyAlert]:
        """获取最近的安全警报"""
        current_time = time.time()
        recent = []
        
        for alert in reversed(self.safety_alerts):
            if current_time - alert.timestamp <= time_window:
                recent.append(alert)
            else:
                break
        
        return list(reversed(recent))
    
    def get_safety_statistics(self) -> Dict[str, Any]:
        """获取安全监控统计信息"""
        current_risk = self.get_current_risk_level()
        
        return {
            "current_risk_level": current_risk.value,
            "total_alerts": self.total_alerts,
            "alert_type_counts": dict(self.alert_type_counts),
            "risk_level_counts": dict(self.risk_level_counts),
            "recent_alerts_count": len(self.recent_alerts),
            "risk_history_size": len(self.risk_history),
            "config": self.config
        }
    
    def reset(self):
        """重置监控器"""
        self.safety_alerts.clear()
        self.recent_alerts.clear()
        self.risk_history.clear()
        self.last_alert_time.clear()
        self.total_alerts = 0
        self.alert_type_counts.clear()
        self.risk_level_counts.clear()
        self.next_alert_id = 1
        self.logger.info("Safety monitor reset")
