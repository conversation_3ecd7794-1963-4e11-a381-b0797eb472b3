#!/usr/bin/env python3
"""
无人机交通警察系统主启动文件
Air Traffic Police System Main Entry Point
"""
import asyncio
import logging
import signal
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core.system import SystemManager
from api.app import create_app, run_server


def setup_logging(level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/system.log', encoding='utf-8')
        ]
    )


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='无人机交通警察系统')
    parser.add_argument('--mode', choices=['api', 'console', 'demo'], default='api',
                       help='运行模式: api(API服务), console(控制台), demo(演示模式)')
    parser.add_argument('--host', default='0.0.0.0', help='API服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='API服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging(log_level)
    
    logger = logging.getLogger(__name__)
    logger.info("启动无人机交通警察系统...")
    
    try:
        if args.mode == 'api':
            # API服务模式
            logger.info(f"启动API服务器 - {args.host}:{args.port}")
            run_server(
                host=args.host,
                port=args.port,
                reload=args.debug,
                log_level="debug" if args.debug else "info"
            )
            
        elif args.mode == 'console':
            # 控制台模式
            await run_console_mode()
            
        elif args.mode == 'demo':
            # 演示模式
            await run_demo_mode()
            
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统运行错误: {e}", exc_info=True)
        sys.exit(1)


async def run_console_mode():
    """控制台模式"""
    logger = logging.getLogger(__name__)
    
    # 创建系统管理器
    system_manager = SystemManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到信号，正在关闭系统...")
        asyncio.create_task(system_manager.shutdown())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化系统
        logger.info("初始化系统...")
        success = await system_manager.initialize()
        
        if not success:
            logger.error("系统初始化失败")
            return
        
        # 启动系统
        logger.info("启动系统...")
        success = await system_manager.start()
        
        if not success:
            logger.error("系统启动失败")
            return
        
        logger.info("系统运行中... 按Ctrl+C退出")
        
        # 主循环
        while True:
            # 获取系统状态
            status = await system_manager.get_system_status()
            logger.info(f"系统状态: {status.get('status', 'unknown')}")
            
            # 等待一段时间
            await asyncio.sleep(10)
            
    except Exception as e:
        logger.error(f"控制台模式运行错误: {e}", exc_info=True)
    finally:
        await system_manager.shutdown()


async def run_demo_mode():
    """演示模式"""
    logger = logging.getLogger(__name__)
    
    logger.info("启动演示模式...")
    
    # 创建系统管理器
    system_manager = SystemManager()
    
    try:
        # 初始化系统
        logger.info("初始化演示系统...")
        success = await system_manager.initialize()
        
        if not success:
            logger.error("演示系统初始化失败")
            return
        
        # 运行演示
        logger.info("运行系统演示...")
        
        # 模拟一些操作
        await demo_system_operations(system_manager)
        
    except Exception as e:
        logger.error(f"演示模式运行错误: {e}", exc_info=True)
    finally:
        await system_manager.shutdown()


async def demo_system_operations(system_manager):
    """演示系统操作"""
    logger = logging.getLogger(__name__)
    
    # 获取系统信息
    logger.info("获取系统信息...")
    info = await system_manager.get_system_info()
    logger.info(f"系统信息: {info}")
    
    # 获取健康状态
    logger.info("检查系统健康状态...")
    health = await system_manager.get_health_status()
    logger.info(f"健康状态: {health}")
    
    # 获取统计信息
    logger.info("获取系统统计...")
    stats = await system_manager.get_comprehensive_statistics()
    logger.info(f"统计信息: {stats}")
    
    # 模拟一些AI操作
    if hasattr(system_manager, 'ai_engine') and system_manager.ai_engine:
        logger.info("测试AI引擎...")
        
        # 这里可以添加一些AI测试代码
        # 例如：加载测试图像，运行检测等
        
    # 模拟业务分析
    if hasattr(system_manager, 'business_engine') and system_manager.business_engine:
        logger.info("测试业务引擎...")
        
        # 获取当前状态
        status = system_manager.business_engine.get_current_status()
        logger.info(f"业务状态: {status}")
    
    logger.info("演示完成")


def create_directories():
    """创建必要的目录"""
    directories = [
        'logs',
        'data',
        'models',
        'temp',
        'static',
        'uploads'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)


if __name__ == "__main__":
    # 创建必要的目录
    create_directories()
    
    # 运行主函数
    if sys.platform == 'win32':
        # Windows下设置事件循环策略
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
