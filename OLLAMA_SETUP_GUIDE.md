# 🤖 Ollama 本地大模型配置指南

## 📋 **Ollama 简介**

Ollama是一个强大的本地大模型运行平台，支持多种开源模型，具有以下优势：

- 🆓 **完全免费** - 无API费用，无使用限制
- 🔒 **数据私有** - 所有数据在本地处理，保护隐私
- ⚡ **高性能** - 优化的推理引擎，支持GPU加速
- 🎯 **易于使用** - 简单的安装和管理命令
- 🌍 **多模型支持** - 支持LLaMA、Qwen、ChatGLM等主流模型

---

## 🚀 **快速安装**

### **Linux / macOS**
```bash
# 一键安装
curl -fsSL https://ollama.ai/install.sh | sh

# 或者使用包管理器
# macOS
brew install ollama

# Ubuntu/Debian
sudo apt install ollama
```

### **Windows**
1. 访问 [Ollama官网](https://ollama.ai/download)
2. 下载Windows安装包
3. 运行安装程序

### **Docker部署**
```bash
# 拉取镜像
docker pull ollama/ollama

# 运行容器
docker run -d \
  --name ollama \
  -p 11434:11434 \
  -v ollama:/root/.ollama \
  ollama/ollama

# GPU支持 (NVIDIA)
docker run -d \
  --name ollama \
  --gpus all \
  -p 11434:11434 \
  -v ollama:/root/.ollama \
  ollama/ollama
```

---

## 📦 **模型下载和管理**

### **推荐模型列表**

#### **中文优化模型**
```bash
# 通义千问 7B (推荐)
ollama pull qwen:7b-chat

# 通义千问 14B (更强性能)
ollama pull qwen:14b-chat

# ChatGLM3 6B
ollama pull chatglm3:6b

# 百川2 7B
ollama pull baichuan2:7b-chat
```

#### **通用英文模型**
```bash
# LLaMA 2 7B
ollama pull llama2:7b-chat

# LLaMA 2 13B
ollama pull llama2:13b-chat

# Mistral 7B
ollama pull mistral:7b-instruct

# Mixtral 8x7B (高性能)
ollama pull mixtral:8x7b-instruct
```

#### **专业模型**
```bash
# 代码生成
ollama pull codellama:7b-instruct
ollama pull deepseek-coder:6.7b-instruct

# 轻量级模型
ollama pull phi:2.7b
ollama pull yi:6b-chat
```

### **模型管理命令**
```bash
# 查看已安装模型
ollama list

# 删除模型
ollama rm model_name

# 查看模型信息
ollama show model_name

# 更新模型
ollama pull model_name
```

---

## ⚙️ **系统配置**

### **1. 启动Ollama服务**
```bash
# 启动服务
ollama serve

# 后台运行
nohup ollama serve > ollama.log 2>&1 &

# 系统服务 (Linux)
sudo systemctl enable ollama
sudo systemctl start ollama
```

### **2. 验证安装**
```bash
# 检查服务状态
curl http://localhost:11434/api/version

# 测试模型
ollama run qwen:7b-chat "你好，请介绍一下自己"
```

### **3. 性能优化**

#### **GPU配置**
```bash
# 检查GPU支持
nvidia-smi

# 设置GPU内存限制
export OLLAMA_GPU_MEMORY_FRACTION=0.8

# 指定GPU设备
export CUDA_VISIBLE_DEVICES=0
```

#### **内存配置**
```bash
# 设置模型上下文长度
export OLLAMA_NUM_CTX=4096

# 设置并行处理数
export OLLAMA_NUM_PARALLEL=4

# 设置GPU层数
export OLLAMA_NUM_GPU=35
```

---

## 🔧 **项目集成配置**

### **1. 环境变量配置**
```bash
# 编辑 .env 文件
vim .env

# 添加以下配置
LLM_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen:7b-chat
OLLAMA_TIMEOUT=60
```

### **2. 测试连接**
```bash
# 运行测试脚本
python -c "
from config.llm_config import OLLAMA_CONFIG
from core.llm.ollama_client import OllamaClientSync

client = OllamaClientSync(OLLAMA_CONFIG)
response = client.generate_response('qwen', '你好，请生成一份交通报告')
print(response)
"
```

### **3. 启动系统**
```bash
# 确保Ollama服务运行
ollama serve &

# 启动交通警察系统
python start.py

# 选择演示模式测试LLM功能
```

---

## 🎯 **模型选择建议**

### **根据硬件配置选择**

| 硬件配置 | 推荐模型 | 内存需求 | 性能 |
|---------|---------|---------|------|
| **8GB RAM** | phi:2.7b | ~3GB | 基础 |
| **16GB RAM** | qwen:7b-chat | ~8GB | 良好 |
| **32GB RAM** | qwen:14b-chat | ~16GB | 优秀 |
| **64GB RAM** | mixtral:8x7b-instruct | ~32GB | 极佳 |

### **根据使用场景选择**

| 使用场景 | 推荐模型 | 特点 |
|---------|---------|------|
| **中文对话** | qwen:7b-chat | 中文优化，响应快 |
| **代码生成** | codellama:7b-instruct | 专业代码能力 |
| **通用任务** | llama2:7b-chat | 平衡性能 |
| **高质量输出** | mixtral:8x7b-instruct | 最佳效果 |
| **资源受限** | phi:2.7b | 轻量级 |

---

## 🔍 **故障排除**

### **常见问题**

#### **1. 服务无法启动**
```bash
# 检查端口占用
sudo netstat -tulpn | grep 11434

# 杀死占用进程
sudo kill -9 <PID>

# 重新启动
ollama serve
```

#### **2. 模型下载失败**
```bash
# 使用代理
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port

# 手动下载
wget https://ollama.ai/library/qwen:7b-chat
ollama create qwen:7b-chat -f ./qwen-7b-chat
```

#### **3. 内存不足**
```bash
# 使用量化模型
ollama pull qwen:7b-chat-q4_0

# 减少上下文长度
export OLLAMA_NUM_CTX=2048

# 关闭其他应用
```

#### **4. GPU不被识别**
```bash
# 检查CUDA安装
nvidia-smi

# 重新安装CUDA驱动
sudo apt install nvidia-driver-470

# 重启Ollama
sudo systemctl restart ollama
```

### **性能调优**

#### **提升推理速度**
```bash
# 启用Flash Attention
export OLLAMA_FLASH_ATTENTION=1

# 使用更多GPU层
export OLLAMA_NUM_GPU=40

# 增加并行数
export OLLAMA_NUM_PARALLEL=8
```

#### **降低内存使用**
```bash
# 使用量化模型
ollama pull qwen:7b-chat-q4_0

# 减少批处理大小
export OLLAMA_BATCH_SIZE=1

# 限制上下文长度
export OLLAMA_NUM_CTX=2048
```

---

## 📊 **性能基准测试**

### **测试脚本**
```python
import time
from core.llm.ollama_client import OllamaClientSync
from config.llm_config import OLLAMA_CONFIG

def benchmark_model(model_name, prompt, iterations=5):
    client = OllamaClientSync(OLLAMA_CONFIG)
    
    times = []
    for i in range(iterations):
        start_time = time.time()
        response = client.generate_response(model_name, prompt)
        end_time = time.time()
        
        times.append(end_time - start_time)
        print(f"第{i+1}次: {end_time - start_time:.2f}秒")
    
    avg_time = sum(times) / len(times)
    print(f"平均响应时间: {avg_time:.2f}秒")
    print(f"响应长度: {len(response)}字符")

# 测试不同模型
models = ["qwen:7b-chat", "llama2:7b-chat", "phi:2.7b"]
prompt = "请生成一份100字的交通状况报告"

for model in models:
    print(f"\n测试模型: {model}")
    benchmark_model(model, prompt)
```

---

## 🎉 **集成验证**

### **完整测试流程**
```bash
# 1. 启动Ollama服务
ollama serve &

# 2. 下载推荐模型
ollama pull qwen:7b-chat

# 3. 配置环境变量
export LLM_PROVIDER=ollama
export OLLAMA_MODEL=qwen:7b-chat

# 4. 测试系统集成
python demo_interactive.py

# 5. 验证报告生成功能
# 在交互式演示中选择"生成智能报告"
```

### **成功标志**
- ✅ Ollama服务正常运行 (端口11434)
- ✅ 模型下载完成且可用
- ✅ 系统能够调用Ollama生成报告
- ✅ 响应时间在可接受范围内
- ✅ 生成的报告质量良好

---

## 🏆 **最佳实践**

### **生产环境建议**
1. **模型选择**: 使用qwen:7b-chat平衡性能和质量
2. **资源配置**: 至少16GB内存，推荐32GB
3. **GPU加速**: 使用NVIDIA GPU显著提升性能
4. **服务管理**: 配置systemd服务自动启动
5. **监控告警**: 监控内存使用和响应时间

### **开发环境建议**
1. **快速测试**: 使用phi:2.7b轻量级模型
2. **功能验证**: 使用qwen:7b-chat测试中文能力
3. **性能测试**: 使用mixtral:8x7b-instruct测试极限性能

---

**🎊 恭喜！您已成功配置Ollama本地大模型服务！**

**立即体验**: `python start.py` → 选择演示模式 → 测试智能报告生成

**享受免费、私有、高性能的本地AI服务！** ✨🤖
