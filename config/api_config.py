"""
API配置模块
管理第三方API和服务的配置
"""
from typing import Optional, Dict, List
try:
    # Pydantic v2
    from pydantic import BaseModel, Field
    from pydantic_settings import BaseSettings
except ImportError:
    # Pydantic v1
    from pydantic import BaseSettings, Field
from enum import Enum


class LLMProvider(str, Enum):
    """大语言模型提供商枚举"""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    BAIDU = "baidu"
    ALIBABA = "alibaba"
    LOCAL = "local"


class TTSProvider(str, Enum):
    """语音合成提供商枚举"""
    AZURE_SPEECH = "azure_speech"
    GOOGLE_TTS = "google_tts"
    BAIDU_TTS = "baidu_tts"
    XUNFEI = "xunfei"
    LOCAL = "local"


class MapProvider(str, Enum):
    """地图服务提供商枚举"""
    BAIDU_MAP = "baidu_map"
    AMAP = "amap"
    GOOGLE_MAPS = "google_maps"
    OPENSTREETMAP = "openstreetmap"


class APIConfig(BaseSettings):
    """API配置类"""
    
    # OpenAI配置
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_BASE_URL: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    OPENAI_MODEL: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    OPENAI_MAX_TOKENS: int = Field(default=1000, env="OPENAI_MAX_TOKENS")
    OPENAI_TEMPERATURE: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    OPENAI_TIMEOUT: int = Field(default=30, env="OPENAI_TIMEOUT")
    
    # Azure OpenAI配置
    AZURE_OPENAI_API_KEY: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_ENDPOINT: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_DEPLOYMENT: str = Field(default="gpt-35-turbo", env="AZURE_OPENAI_DEPLOYMENT")
    AZURE_OPENAI_API_VERSION: str = Field(default="2023-05-15", env="AZURE_OPENAI_API_VERSION")
    
    # 百度文心一言配置
    BAIDU_API_KEY: Optional[str] = Field(default=None, env="BAIDU_API_KEY")
    BAIDU_SECRET_KEY: Optional[str] = Field(default=None, env="BAIDU_SECRET_KEY")
    BAIDU_MODEL: str = Field(default="ERNIE-Bot", env="BAIDU_MODEL")
    
    # 阿里云通义千问配置
    ALIBABA_API_KEY: Optional[str] = Field(default=None, env="ALIBABA_API_KEY")
    ALIBABA_MODEL: str = Field(default="qwen-turbo", env="ALIBABA_MODEL")
    
    # LLM通用配置
    LLM_PROVIDER: LLMProvider = Field(default=LLMProvider.OPENAI, env="LLM_PROVIDER")
    LLM_ENABLED: bool = Field(default=True, env="LLM_ENABLED")
    LLM_RETRY_TIMES: int = Field(default=3, env="LLM_RETRY_TIMES")
    LLM_CACHE_ENABLED: bool = Field(default=True, env="LLM_CACHE_ENABLED")
    
    # Azure语音服务配置
    AZURE_SPEECH_KEY: Optional[str] = Field(default=None, env="AZURE_SPEECH_KEY")
    AZURE_SPEECH_REGION: str = Field(default="eastus", env="AZURE_SPEECH_REGION")
    AZURE_SPEECH_VOICE: str = Field(default="zh-CN-XiaoxiaoNeural", env="AZURE_SPEECH_VOICE")
    
    # 百度语音配置
    BAIDU_TTS_API_KEY: Optional[str] = Field(default=None, env="BAIDU_TTS_API_KEY")
    BAIDU_TTS_SECRET_KEY: Optional[str] = Field(default=None, env="BAIDU_TTS_SECRET_KEY")
    BAIDU_TTS_VOICE: str = Field(default="5118", env="BAIDU_TTS_VOICE")  # 度小宇
    
    # 讯飞语音配置
    XUNFEI_APP_ID: Optional[str] = Field(default=None, env="XUNFEI_APP_ID")
    XUNFEI_API_KEY: Optional[str] = Field(default=None, env="XUNFEI_API_KEY")
    XUNFEI_API_SECRET: Optional[str] = Field(default=None, env="XUNFEI_API_SECRET")
    XUNFEI_VOICE: str = Field(default="xiaoyan", env="XUNFEI_VOICE")
    
    # TTS通用配置
    TTS_PROVIDER: TTSProvider = Field(default=TTSProvider.AZURE_SPEECH, env="TTS_PROVIDER")
    TTS_ENABLED: bool = Field(default=True, env="TTS_ENABLED")
    TTS_SPEED: float = Field(default=1.0, env="TTS_SPEED")
    TTS_PITCH: float = Field(default=0.0, env="TTS_PITCH")
    TTS_VOLUME: float = Field(default=1.0, env="TTS_VOLUME")
    TTS_CACHE_ENABLED: bool = Field(default=True, env="TTS_CACHE_ENABLED")
    
    # 百度地图配置
    BAIDU_MAP_API_KEY: Optional[str] = Field(default=None, env="BAIDU_MAP_API_KEY")
    
    # 高德地图配置
    AMAP_API_KEY: Optional[str] = Field(default=None, env="AMAP_API_KEY")
    
    # 谷歌地图配置
    GOOGLE_MAPS_API_KEY: Optional[str] = Field(default=None, env="GOOGLE_MAPS_API_KEY")
    
    # 地图服务配置
    MAP_PROVIDER: MapProvider = Field(default=MapProvider.BAIDU_MAP, env="MAP_PROVIDER")
    MAP_ENABLED: bool = Field(default=True, env="MAP_ENABLED")
    
    # 短信服务配置
    SMS_ENABLED: bool = Field(default=False, env="SMS_ENABLED")
    SMS_API_KEY: Optional[str] = Field(default=None, env="SMS_API_KEY")
    SMS_TEMPLATE_ID: Optional[str] = Field(default=None, env="SMS_TEMPLATE_ID")
    
    # 邮件服务配置
    EMAIL_ENABLED: bool = Field(default=False, env="EMAIL_ENABLED")
    SMTP_HOST: str = Field(default="smtp.gmail.com", env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_USE_TLS: bool = Field(default=True, env="SMTP_USE_TLS")
    
    # 微信推送配置
    WECHAT_ENABLED: bool = Field(default=False, env="WECHAT_ENABLED")
    WECHAT_APP_ID: Optional[str] = Field(default=None, env="WECHAT_APP_ID")
    WECHAT_APP_SECRET: Optional[str] = Field(default=None, env="WECHAT_APP_SECRET")
    
    # 钉钉推送配置
    DINGTALK_ENABLED: bool = Field(default=False, env="DINGTALK_ENABLED")
    DINGTALK_WEBHOOK: Optional[str] = Field(default=None, env="DINGTALK_WEBHOOK")
    DINGTALK_SECRET: Optional[str] = Field(default=None, env="DINGTALK_SECRET")
    
    # API限流配置
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # 秒
    
    # 重试配置
    DEFAULT_RETRY_TIMES: int = Field(default=3, env="DEFAULT_RETRY_TIMES")
    DEFAULT_RETRY_DELAY: float = Field(default=1.0, env="DEFAULT_RETRY_DELAY")
    DEFAULT_TIMEOUT: int = Field(default=30, env="DEFAULT_TIMEOUT")
    
    # Pydantic v2 配置
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"
    }
    
    def get_llm_config(self) -> Dict:
        """获取大语言模型配置"""
        config = {
            "provider": self.LLM_PROVIDER,
            "enabled": self.LLM_ENABLED,
            "retry_times": self.LLM_RETRY_TIMES,
            "cache_enabled": self.LLM_CACHE_ENABLED
        }
        
        if self.LLM_PROVIDER == LLMProvider.OPENAI:
            config.update({
                "api_key": self.OPENAI_API_KEY,
                "base_url": self.OPENAI_BASE_URL,
                "model": self.OPENAI_MODEL,
                "max_tokens": self.OPENAI_MAX_TOKENS,
                "temperature": self.OPENAI_TEMPERATURE,
                "timeout": self.OPENAI_TIMEOUT
            })
        elif self.LLM_PROVIDER == LLMProvider.AZURE_OPENAI:
            config.update({
                "api_key": self.AZURE_OPENAI_API_KEY,
                "endpoint": self.AZURE_OPENAI_ENDPOINT,
                "deployment": self.AZURE_OPENAI_DEPLOYMENT,
                "api_version": self.AZURE_OPENAI_API_VERSION
            })
        elif self.LLM_PROVIDER == LLMProvider.BAIDU:
            config.update({
                "api_key": self.BAIDU_API_KEY,
                "secret_key": self.BAIDU_SECRET_KEY,
                "model": self.BAIDU_MODEL
            })
        
        return config
    
    def get_tts_config(self) -> Dict:
        """获取语音合成配置"""
        config = {
            "provider": self.TTS_PROVIDER,
            "enabled": self.TTS_ENABLED,
            "speed": self.TTS_SPEED,
            "pitch": self.TTS_PITCH,
            "volume": self.TTS_VOLUME,
            "cache_enabled": self.TTS_CACHE_ENABLED
        }
        
        if self.TTS_PROVIDER == TTSProvider.AZURE_SPEECH:
            config.update({
                "api_key": self.AZURE_SPEECH_KEY,
                "region": self.AZURE_SPEECH_REGION,
                "voice": self.AZURE_SPEECH_VOICE
            })
        elif self.TTS_PROVIDER == TTSProvider.BAIDU_TTS:
            config.update({
                "api_key": self.BAIDU_TTS_API_KEY,
                "secret_key": self.BAIDU_TTS_SECRET_KEY,
                "voice": self.BAIDU_TTS_VOICE
            })
        elif self.TTS_PROVIDER == TTSProvider.XUNFEI:
            config.update({
                "app_id": self.XUNFEI_APP_ID,
                "api_key": self.XUNFEI_API_KEY,
                "api_secret": self.XUNFEI_API_SECRET,
                "voice": self.XUNFEI_VOICE
            })
        
        return config
    
    def get_map_config(self) -> Dict:
        """获取地图服务配置"""
        config = {
            "provider": self.MAP_PROVIDER,
            "enabled": self.MAP_ENABLED
        }
        
        if self.MAP_PROVIDER == MapProvider.BAIDU_MAP:
            config["api_key"] = self.BAIDU_MAP_API_KEY
        elif self.MAP_PROVIDER == MapProvider.AMAP:
            config["api_key"] = self.AMAP_API_KEY
        elif self.MAP_PROVIDER == MapProvider.GOOGLE_MAPS:
            config["api_key"] = self.GOOGLE_MAPS_API_KEY
        
        return config
    
    def get_notification_config(self) -> Dict:
        """获取通知服务配置"""
        return {
            "sms": {
                "enabled": self.SMS_ENABLED,
                "api_key": self.SMS_API_KEY,
                "template_id": self.SMS_TEMPLATE_ID
            },
            "email": {
                "enabled": self.EMAIL_ENABLED,
                "smtp_host": self.SMTP_HOST,
                "smtp_port": self.SMTP_PORT,
                "username": self.SMTP_USERNAME,
                "password": self.SMTP_PASSWORD,
                "use_tls": self.SMTP_USE_TLS
            },
            "wechat": {
                "enabled": self.WECHAT_ENABLED,
                "app_id": self.WECHAT_APP_ID,
                "app_secret": self.WECHAT_APP_SECRET
            },
            "dingtalk": {
                "enabled": self.DINGTALK_ENABLED,
                "webhook": self.DINGTALK_WEBHOOK,
                "secret": self.DINGTALK_SECRET
            }
        }
    
    def validate_api_config(self) -> bool:
        """验证API配置的有效性"""
        try:
            # 验证LLM配置
            if self.LLM_ENABLED:
                if self.LLM_PROVIDER == LLMProvider.OPENAI and not self.OPENAI_API_KEY:
                    raise ValueError("OpenAI API key is required")
                elif self.LLM_PROVIDER == LLMProvider.AZURE_OPENAI and not self.AZURE_OPENAI_API_KEY:
                    raise ValueError("Azure OpenAI API key is required")
            
            # 验证TTS配置
            if self.TTS_ENABLED:
                if self.TTS_PROVIDER == TTSProvider.AZURE_SPEECH and not self.AZURE_SPEECH_KEY:
                    raise ValueError("Azure Speech API key is required")
            
            # 验证邮件配置
            if self.EMAIL_ENABLED:
                if not (1 <= self.SMTP_PORT <= 65535):
                    raise ValueError("Invalid SMTP port")
            
            return True
            
        except Exception as e:
            print(f"API configuration validation failed: {e}")
            return False
    
    def get_api_summary(self) -> Dict:
        """获取API配置摘要"""
        return {
            "llm": {
                "provider": self.LLM_PROVIDER,
                "enabled": self.LLM_ENABLED
            },
            "tts": {
                "provider": self.TTS_PROVIDER,
                "enabled": self.TTS_ENABLED
            },
            "map": {
                "provider": self.MAP_PROVIDER,
                "enabled": self.MAP_ENABLED
            },
            "notifications": {
                "sms": self.SMS_ENABLED,
                "email": self.EMAIL_ENABLED,
                "wechat": self.WECHAT_ENABLED,
                "dingtalk": self.DINGTALK_ENABLED
            }
        }
