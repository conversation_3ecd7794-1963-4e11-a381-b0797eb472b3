"""
主配置文件
管理系统的核心配置参数
"""
import os
from typing import List, Optional
from pydantic import BaseSettings, Field
from pathlib import Path


class Settings(BaseSettings):
    """主配置类"""
    
    # 项目基础信息
    PROJECT_NAME: str = "空中交警智能交通管理系统"
    PROJECT_VERSION: str = "1.0.0"
    PROJECT_DESCRIPTION: str = "基于无人机的智能交通管理系统"
    
    # 环境配置
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 服务配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=1, env="WORKERS")
    
    # 安全配置
    SECRET_KEY: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # 文件路径配置
    BASE_DIR: Path = Path(__file__).parent.parent
    DATA_DIR: Path = BASE_DIR / "data"
    LOGS_DIR: Path = DATA_DIR / "logs"
    VIDEOS_DIR: Path = DATA_DIR / "videos"
    IMAGES_DIR: Path = DATA_DIR / "images"
    MODELS_DIR: Path = DATA_DIR / "models"
    EXPORTS_DIR: Path = DATA_DIR / "exports"
    
    # 视频处理配置
    DEFAULT_VIDEO_PATH: str = Field(default="action1.mp4", env="DEFAULT_VIDEO_PATH")
    VIDEO_FRAME_RATE: int = Field(default=30, env="VIDEO_FRAME_RATE")
    VIDEO_RESOLUTION: tuple = Field(default=(1920, 1080), env="VIDEO_RESOLUTION")
    FRAME_SKIP: int = Field(default=1, env="FRAME_SKIP")
    
    # 性能配置
    MAX_CONCURRENT_STREAMS: int = Field(default=4, env="MAX_CONCURRENT_STREAMS")
    PROCESSING_TIMEOUT: int = Field(default=30, env="PROCESSING_TIMEOUT")
    MAX_MEMORY_USAGE_MB: int = Field(default=4096, env="MAX_MEMORY_USAGE_MB")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")
    HEALTH_CHECK_INTERVAL: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    # 通知配置
    ENABLE_NOTIFICATIONS: bool = Field(default=True, env="ENABLE_NOTIFICATIONS")
    NOTIFICATION_CHANNELS: List[str] = Field(default=["websocket", "email"], env="NOTIFICATION_CHANNELS")
    
    # 语音配置
    ENABLE_VOICE: bool = Field(default=True, env="ENABLE_VOICE")
    VOICE_LANGUAGE: str = Field(default="zh-CN", env="VOICE_LANGUAGE")
    VOICE_SPEED: float = Field(default=1.0, env="VOICE_SPEED")
    
    # 存储配置
    ENABLE_RECORDING: bool = Field(default=False, env="ENABLE_RECORDING")
    MAX_RECORDING_HOURS: int = Field(default=24, env="MAX_RECORDING_HOURS")
    AUTO_CLEANUP_DAYS: int = Field(default=7, env="AUTO_CLEANUP_DAYS")
    
    # 第三方服务配置
    ENABLE_CLOUD_BACKUP: bool = Field(default=False, env="ENABLE_CLOUD_BACKUP")
    CLOUD_STORAGE_PROVIDER: str = Field(default="local", env="CLOUD_STORAGE_PROVIDER")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.DATA_DIR,
            self.LOGS_DIR,
            self.VIDEOS_DIR,
            self.IMAGES_DIR,
            self.MODELS_DIR,
            self.EXPORTS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.ENVIRONMENT.lower() == "development"
    
    def get_log_config(self) -> dict:
        """获取日志配置"""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                },
                "detailed": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": self.LOG_LEVEL,
                    "formatter": "default",
                    "stream": "ext://sys.stdout",
                },
                "file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "level": self.LOG_LEVEL,
                    "formatter": "detailed",
                    "filename": str(self.LOGS_DIR / "app.log"),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 5,
                },
            },
            "loggers": {
                "": {
                    "level": self.LOG_LEVEL,
                    "handlers": ["console", "file"],
                    "propagate": False,
                },
            },
        }
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查必要的目录
            if not self.BASE_DIR.exists():
                raise ValueError(f"Base directory does not exist: {self.BASE_DIR}")
            
            # 检查端口范围
            if not (1024 <= self.PORT <= 65535):
                raise ValueError(f"Invalid port number: {self.PORT}")
            
            # 检查日志级别
            valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            if self.LOG_LEVEL not in valid_log_levels:
                raise ValueError(f"Invalid log level: {self.LOG_LEVEL}")
            
            # 检查环境类型
            valid_environments = ["development", "testing", "production"]
            if self.ENVIRONMENT not in valid_environments:
                raise ValueError(f"Invalid environment: {self.ENVIRONMENT}")
            
            return True
            
        except Exception as e:
            print(f"Configuration validation failed: {e}")
            return False
    
    def get_summary(self) -> dict:
        """获取配置摘要"""
        return {
            "project": {
                "name": self.PROJECT_NAME,
                "version": self.PROJECT_VERSION,
                "environment": self.ENVIRONMENT,
                "debug": self.DEBUG
            },
            "service": {
                "host": self.HOST,
                "port": self.PORT,
                "workers": self.WORKERS
            },
            "features": {
                "voice_enabled": self.ENABLE_VOICE,
                "recording_enabled": self.ENABLE_RECORDING,
                "notifications_enabled": self.ENABLE_NOTIFICATIONS,
                "metrics_enabled": self.ENABLE_METRICS
            },
            "performance": {
                "max_streams": self.MAX_CONCURRENT_STREAMS,
                "timeout": self.PROCESSING_TIMEOUT,
                "max_memory_mb": self.MAX_MEMORY_USAGE_MB
            }
        }
