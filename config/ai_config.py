"""
AI模型配置模块
管理所有AI相关的配置参数
"""
from typing import Dict, List, Optional, Tuple
try:
    # Pydantic v2
    from pydantic import BaseModel, Field
    from pydantic_settings import BaseSettings
except ImportError:
    # Pydantic v1
    from pydantic import BaseSettings, Field
from enum import Enum


class ModelType(str, Enum):
    """模型类型枚举"""
    YOLOV8N = "yolov8n"
    YOLOV8S = "yolov8s"
    YOLOV8M = "yolov8m"
    YOLOV8L = "yolov8l"
    YOLOV8X = "yolov8x"
    CUSTOM = "custom"


class DeviceType(str, Enum):
    """设备类型枚举"""
    CPU = "cpu"
    CUDA = "cuda"
    MPS = "mps"
    AUTO = "auto"


class TrackerType(str, Enum):
    """跟踪器类型枚举"""
    DEEPSORT = "deepsort"
    BYTETRACK = "bytetrack"
    STRONGSORT = "strongsort"


class AIConfig(BaseSettings):
    """AI配置类"""
    
    # 模型基础配置
    MODEL_TYPE: ModelType = Field(default=ModelType.YOLOV8S, env="MODEL_TYPE")
    MODEL_PATH: str = Field(default="yolov8s.pt", env="MODEL_PATH")
    CUSTOM_MODEL_PATH: Optional[str] = Field(default=None, env="CUSTOM_MODEL_PATH")
    
    # 设备配置
    DEVICE: DeviceType = Field(default=DeviceType.AUTO, env="DEVICE")
    DEVICE_ID: int = Field(default=0, env="DEVICE_ID")
    HALF_PRECISION: bool = Field(default=False, env="HALF_PRECISION")
    
    # 检测参数
    CONFIDENCE_THRESHOLD: float = Field(default=0.25, env="CONFIDENCE_THRESHOLD")
    IOU_THRESHOLD: float = Field(default=0.45, env="IOU_THRESHOLD")
    MAX_DETECTIONS: int = Field(default=1000, env="MAX_DETECTIONS")
    
    # 输入参数
    INPUT_SIZE: Tuple[int, int] = Field(default=(640, 640))
    NORMALIZE: bool = Field(default=True, env="NORMALIZE")
    AUGMENT: bool = Field(default=False, env="AUGMENT")
    
    # 目标类别配置
    VEHICLE_CLASSES: List[str] = Field(default_factory=lambda: [
        "car", "truck", "bus", "motorcycle", "bicycle"
    ])
    PERSON_CLASSES: List[str] = Field(default_factory=lambda: ["person"])
    TRAFFIC_SIGN_CLASSES: List[str] = Field(default_factory=lambda: [
        "traffic light", "stop sign", "speed limit"
    ])
    
    # 跟踪配置
    TRACKING_ENABLED: bool = Field(default=True, env="TRACKING_ENABLED")
    TRACKER_TYPE: TrackerType = Field(default=TrackerType.DEEPSORT, env="TRACKER_TYPE")
    MAX_TRACKING_AGE: int = Field(default=30, env="MAX_TRACKING_AGE")
    MIN_TRACKING_HITS: int = Field(default=3, env="MIN_TRACKING_HITS")
    
    # DeepSORT配置
    DEEPSORT_MODEL_PATH: str = Field(default="deep_sort/deep/checkpoint/ckpt.t7", env="DEEPSORT_MODEL_PATH")
    DEEPSORT_MAX_DIST: float = Field(default=0.2, env="DEEPSORT_MAX_DIST")
    DEEPSORT_MIN_CONFIDENCE: float = Field(default=0.3, env="DEEPSORT_MIN_CONFIDENCE")
    DEEPSORT_NMS_MAX_OVERLAP: float = Field(default=1.0, env="DEEPSORT_NMS_MAX_OVERLAP")
    DEEPSORT_MAX_IOU_DISTANCE: float = Field(default=0.7, env="DEEPSORT_MAX_IOU_DISTANCE")
    DEEPSORT_MAX_AGE: int = Field(default=70, env="DEEPSORT_MAX_AGE")
    DEEPSORT_N_INIT: int = Field(default=3, env="DEEPSORT_N_INIT")
    
    # 行为分析配置
    BEHAVIOR_ANALYSIS_ENABLED: bool = Field(default=True, env="BEHAVIOR_ANALYSIS_ENABLED")
    SPEED_CALCULATION_ENABLED: bool = Field(default=True, env="SPEED_CALCULATION_ENABLED")
    TRAJECTORY_LENGTH: int = Field(default=30, env="TRAJECTORY_LENGTH")
    
    # 事故检测配置
    ACCIDENT_DETECTION_ENABLED: bool = Field(default=True, env="ACCIDENT_DETECTION_ENABLED")
    COLLISION_IOU_THRESHOLD: float = Field(default=0.3, env="COLLISION_IOU_THRESHOLD")
    STATIONARY_TIME_THRESHOLD: int = Field(default=60, env="STATIONARY_TIME_THRESHOLD")  # 秒
    SPEED_ANOMALY_THRESHOLD: float = Field(default=0.5, env="SPEED_ANOMALY_THRESHOLD")
    
    # 违法检测配置
    VIOLATION_DETECTION_ENABLED: bool = Field(default=True, env="VIOLATION_DETECTION_ENABLED")
    LANE_DETECTION_ENABLED: bool = Field(default=True, env="LANE_DETECTION_ENABLED")
    SPEED_LIMIT_DETECTION: bool = Field(default=True, env="SPEED_LIMIT_DETECTION")
    RED_LIGHT_DETECTION: bool = Field(default=True, env="RED_LIGHT_DETECTION")
    
    # 安全装备检测配置
    HELMET_DETECTION_ENABLED: bool = Field(default=True, env="HELMET_DETECTION_ENABLED")
    SEATBELT_DETECTION_ENABLED: bool = Field(default=True, env="SEATBELT_DETECTION_ENABLED")
    HELMET_CONFIDENCE_THRESHOLD: float = Field(default=0.5, env="HELMET_CONFIDENCE_THRESHOLD")
    
    # 性能优化配置
    BATCH_SIZE: int = Field(default=1, env="BATCH_SIZE")
    NUM_WORKERS: int = Field(default=4, env="NUM_WORKERS")
    PREFETCH_FACTOR: int = Field(default=2, env="PREFETCH_FACTOR")
    PIN_MEMORY: bool = Field(default=True, env="PIN_MEMORY")
    
    # TensorRT优化
    TENSORRT_ENABLED: bool = Field(default=False, env="TENSORRT_ENABLED")
    TENSORRT_WORKSPACE_SIZE: int = Field(default=1, env="TENSORRT_WORKSPACE_SIZE")  # GB
    TENSORRT_MAX_BATCH_SIZE: int = Field(default=8, env="TENSORRT_MAX_BATCH_SIZE")
    
    # 模型缓存配置
    MODEL_CACHE_ENABLED: bool = Field(default=True, env="MODEL_CACHE_ENABLED")
    MODEL_CACHE_SIZE: int = Field(default=3, env="MODEL_CACHE_SIZE")
    PRELOAD_MODELS: bool = Field(default=True, env="PRELOAD_MODELS")
    
    # 数据增强配置
    DATA_AUGMENTATION: bool = Field(default=False, env="DATA_AUGMENTATION")
    BRIGHTNESS_RANGE: Tuple[float, float] = Field(default=(0.8, 1.2))
    CONTRAST_RANGE: Tuple[float, float] = Field(default=(0.8, 1.2))
    SATURATION_RANGE: Tuple[float, float] = Field(default=(0.8, 1.2))
    
    # 后处理配置
    POST_PROCESSING_ENABLED: bool = Field(default=True, env="POST_PROCESSING_ENABLED")
    SMOOTHING_ENABLED: bool = Field(default=True, env="SMOOTHING_ENABLED")
    SMOOTHING_FACTOR: float = Field(default=0.3, env="SMOOTHING_FACTOR")
    
    # 质量控制配置
    MIN_OBJECT_SIZE: int = Field(default=32, env="MIN_OBJECT_SIZE")  # 像素
    MAX_OBJECT_SIZE: int = Field(default=1000, env="MAX_OBJECT_SIZE")  # 像素
    ASPECT_RATIO_FILTER: bool = Field(default=True, env="ASPECT_RATIO_FILTER")
    MIN_ASPECT_RATIO: float = Field(default=0.2, env="MIN_ASPECT_RATIO")
    MAX_ASPECT_RATIO: float = Field(default=5.0, env="MAX_ASPECT_RATIO")
    
    # Pydantic v2 配置
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"
    }
    
    def get_model_config(self) -> Dict:
        """获取模型配置"""
        return {
            "type": self.MODEL_TYPE,
            "path": self.CUSTOM_MODEL_PATH if self.MODEL_TYPE == ModelType.CUSTOM else self.MODEL_PATH,
            "device": self.DEVICE,
            "device_id": self.DEVICE_ID,
            "half_precision": self.HALF_PRECISION,
            "input_size": self.INPUT_SIZE,
            "confidence_threshold": self.CONFIDENCE_THRESHOLD,
            "iou_threshold": self.IOU_THRESHOLD,
            "max_detections": self.MAX_DETECTIONS
        }
    
    def get_tracking_config(self) -> Dict:
        """获取跟踪配置"""
        config = {
            "enabled": self.TRACKING_ENABLED,
            "type": self.TRACKER_TYPE,
            "max_age": self.MAX_TRACKING_AGE,
            "min_hits": self.MIN_TRACKING_HITS
        }
        
        if self.TRACKER_TYPE == TrackerType.DEEPSORT:
            config.update({
                "model_path": self.DEEPSORT_MODEL_PATH,
                "max_dist": self.DEEPSORT_MAX_DIST,
                "min_confidence": self.DEEPSORT_MIN_CONFIDENCE,
                "nms_max_overlap": self.DEEPSORT_NMS_MAX_OVERLAP,
                "max_iou_distance": self.DEEPSORT_MAX_IOU_DISTANCE,
                "max_age": self.DEEPSORT_MAX_AGE,
                "n_init": self.DEEPSORT_N_INIT
            })
        
        return config
    
    def get_detection_classes(self) -> Dict[str, List[str]]:
        """获取检测类别配置"""
        return {
            "vehicles": self.VEHICLE_CLASSES,
            "persons": self.PERSON_CLASSES,
            "traffic_signs": self.TRAFFIC_SIGN_CLASSES
        }
    
    def get_performance_config(self) -> Dict:
        """获取性能配置"""
        return {
            "batch_size": self.BATCH_SIZE,
            "num_workers": self.NUM_WORKERS,
            "prefetch_factor": self.PREFETCH_FACTOR,
            "pin_memory": self.PIN_MEMORY,
            "tensorrt": {
                "enabled": self.TENSORRT_ENABLED,
                "workspace_size": self.TENSORRT_WORKSPACE_SIZE,
                "max_batch_size": self.TENSORRT_MAX_BATCH_SIZE
            },
            "cache": {
                "enabled": self.MODEL_CACHE_ENABLED,
                "size": self.MODEL_CACHE_SIZE,
                "preload": self.PRELOAD_MODELS
            }
        }
    
    def validate_ai_config(self) -> bool:
        """验证AI配置的有效性"""
        try:
            # 验证阈值范围
            if not (0.0 <= self.CONFIDENCE_THRESHOLD <= 1.0):
                raise ValueError("Invalid confidence threshold")
            if not (0.0 <= self.IOU_THRESHOLD <= 1.0):
                raise ValueError("Invalid IoU threshold")
            
            # 验证输入尺寸
            if self.INPUT_SIZE[0] <= 0 or self.INPUT_SIZE[1] <= 0:
                raise ValueError("Invalid input size")
            
            # 验证批次大小
            if self.BATCH_SIZE <= 0:
                raise ValueError("Invalid batch size")
            
            # 验证跟踪参数
            if self.MAX_TRACKING_AGE <= 0:
                raise ValueError("Invalid max tracking age")
            
            return True
            
        except Exception as e:
            print(f"AI configuration validation failed: {e}")
            return False
    
    def get_ai_summary(self) -> Dict:
        """获取AI配置摘要"""
        return {
            "model": {
                "type": self.MODEL_TYPE,
                "device": self.DEVICE,
                "input_size": self.INPUT_SIZE,
                "confidence": self.CONFIDENCE_THRESHOLD
            },
            "features": {
                "tracking": self.TRACKING_ENABLED,
                "behavior_analysis": self.BEHAVIOR_ANALYSIS_ENABLED,
                "accident_detection": self.ACCIDENT_DETECTION_ENABLED,
                "violation_detection": self.VIOLATION_DETECTION_ENABLED,
                "helmet_detection": self.HELMET_DETECTION_ENABLED
            },
            "performance": {
                "batch_size": self.BATCH_SIZE,
                "tensorrt": self.TENSORRT_ENABLED,
                "half_precision": self.HALF_PRECISION
            }
        }
