#!/usr/bin/env python3
"""
无人机配置文件
支持多种无人机型号和连接方式
"""

import os
from typing import Dict, Any

# DJI 无人机配置
DJI_CONFIG = {
    # API 密钥 (从 DJI 开发者平台获取)
    "app_key": os.getenv("DJI_APP_KEY", "your_dji_app_key_here"),
    "app_secret": os.getenv("DJI_APP_SECRET", "your_dji_app_secret_here"),
    
    # 连接配置
    "connection": {
        "type": "wifi",  # wifi, usb, lightbridge, ocusync
        "timeout": 30,   # 连接超时时间(秒)
        "retry_count": 3,
        "heartbeat_interval": 5  # 心跳间隔(秒)
    },
    
    # 视频流配置
    "video_stream": {
        "resolution": "1920x1080",  # 4K: 3840x2160, FHD: 1920x1080, HD: 1280x720
        "fps": 30,
        "bitrate": 8000,  # kbps
        "codec": "h264",  # h264, h265
        "format": "yuv420p"
    },
    
    # 飞行参数
    "flight_params": {
        "max_altitude": 120,        # 最大飞行高度(米)
        "max_distance": 500,        # 最大飞行距离(米)
        "return_home_altitude": 30, # 返航高度(米)
        "max_speed": 15,            # 最大飞行速度(m/s)
        "max_angular_velocity": 100 # 最大角速度(度/秒)
    },
    
    # 相机配置
    "camera": {
        "gimbal_pitch_range": [-90, 30],  # 云台俯仰角度范围
        "gimbal_yaw_range": [-180, 180],  # 云台偏航角度范围
        "auto_exposure": True,
        "iso_range": [100, 3200],
        "shutter_speed_range": [1, 8000]  # 快门速度范围
    }
}

# Tello 系列配置 (适用于教学和轻量级应用)
TELLO_CONFIG = {
    "connection": {
        "ip": "************",
        "port": 8889,
        "timeout": 10
    },
    "video_stream": {
        "resolution": "960x720",
        "fps": 30,
        "port": 11111
    },
    "flight_params": {
        "max_altitude": 30,
        "max_distance": 100,
        "max_speed": 8
    }
}

# Autel 无人机配置
AUTEL_CONFIG = {
    "connection": {
        "type": "wifi",
        "timeout": 30
    },
    "video_stream": {
        "resolution": "3840x2160",  # 6K
        "fps": 30,
        "bitrate": 12000
    },
    "flight_params": {
        "max_altitude": 120,
        "max_distance": 1000,
        "max_speed": 20
    }
}

# 支持的无人机型号
SUPPORTED_DRONES = {
    "dji_mavic_3": {
        "brand": "DJI",
        "model": "Mavic 3",
        "config": DJI_CONFIG,
        "features": ["4K_video", "obstacle_avoidance", "long_range"],
        "max_flight_time": 46  # 分钟
    },
    "dji_air_2s": {
        "brand": "DJI", 
        "model": "Air 2S",
        "config": DJI_CONFIG,
        "features": ["1_inch_sensor", "4K_video", "obstacle_avoidance"],
        "max_flight_time": 31
    },
    "dji_mini_3_pro": {
        "brand": "DJI",
        "model": "Mini 3 Pro", 
        "config": DJI_CONFIG,
        "features": ["lightweight", "4K_video", "vertical_shooting"],
        "max_flight_time": 34
    },
    "dji_phantom_4_pro": {
        "brand": "DJI",
        "model": "Phantom 4 Pro",
        "config": DJI_CONFIG,
        "features": ["professional", "mechanical_shutter", "obstacle_avoidance"],
        "max_flight_time": 30
    },
    "dji_tello": {
        "brand": "DJI",
        "model": "Tello",
        "config": TELLO_CONFIG,
        "features": ["educational", "lightweight", "programmable"],
        "max_flight_time": 13
    },
    "autel_evo_ii": {
        "brand": "Autel",
        "model": "EVO II",
        "config": AUTEL_CONFIG,
        "features": ["6K_video", "long_range", "obstacle_avoidance"],
        "max_flight_time": 40
    }
}

# 巡航路线配置
PATROL_ROUTES = {
    "city_center": {
        "name": "市中心巡航",
        "waypoints": [
            {"lat": 39.9042, "lng": 116.4074, "alt": 50},  # 天安门
            {"lat": 39.9163, "lng": 116.3972, "alt": 50},  # 西单
            {"lat": 39.9289, "lng": 116.3883, "alt": 50},  # 西直门
            {"lat": 39.9042, "lng": 116.4074, "alt": 50}   # 返回起点
        ],
        "hover_duration": 30,  # 每个点悬停时间(秒)
        "flight_speed": 5      # 飞行速度(m/s)
    },
    "highway_patrol": {
        "name": "高速公路巡航",
        "waypoints": [
            {"lat": 39.8000, "lng": 116.3000, "alt": 80},
            {"lat": 39.8100, "lng": 116.3100, "alt": 80},
            {"lat": 39.8200, "lng": 116.3200, "alt": 80},
            {"lat": 39.8300, "lng": 116.3300, "alt": 80}
        ],
        "hover_duration": 60,
        "flight_speed": 8
    },
    "intersection_monitor": {
        "name": "路口监控",
        "waypoints": [
            {"lat": 39.9000, "lng": 116.4000, "alt": 40}
        ],
        "hover_duration": 300,  # 在路口悬停5分钟
        "flight_speed": 3
    }
}

# 安全配置
SAFETY_CONFIG = {
    "geofence": {
        "enabled": True,
        "max_altitude": 120,    # 最大飞行高度
        "max_distance": 500,    # 最大飞行距离
        "no_fly_zones": [       # 禁飞区域
            {
                "name": "机场",
                "center": {"lat": 40.0801, "lng": 116.5846},
                "radius": 5000  # 米
            }
        ]
    },
    "emergency": {
        "low_battery_threshold": 20,    # 低电量阈值(%)
        "signal_loss_timeout": 10,      # 信号丢失超时(秒)
        "auto_return_home": True,       # 自动返航
        "emergency_landing": True       # 紧急降落
    },
    "weather": {
        "max_wind_speed": 10,          # 最大风速(m/s)
        "min_visibility": 1000,        # 最小能见度(米)
        "max_precipitation": 0.1       # 最大降水量(mm/h)
    }
}

# 数据传输配置
DATA_TRANSMISSION = {
    "video_stream": {
        "protocol": "rtmp",  # rtmp, udp, tcp
        "server_url": "rtmp://localhost:1935/live",
        "stream_key": "drone_stream",
        "buffer_size": 1024 * 1024,  # 1MB
        "compression": "h264"
    },
    "telemetry": {
        "protocol": "mqtt",  # mqtt, websocket, tcp
        "broker_host": "localhost",
        "broker_port": 1883,
        "topic_prefix": "drone/telemetry",
        "qos": 1,
        "retain": False
    },
    "commands": {
        "protocol": "tcp",
        "host": "localhost", 
        "port": 8888,
        "timeout": 5
    }
}

# 获取无人机配置
def get_drone_config(drone_type: str) -> Dict[str, Any]:
    """
    获取指定无人机型号的配置
    
    Args:
        drone_type: 无人机型号标识
        
    Returns:
        无人机配置字典
    """
    if drone_type not in SUPPORTED_DRONES:
        raise ValueError(f"不支持的无人机型号: {drone_type}")
    
    return SUPPORTED_DRONES[drone_type]

# 验证配置
def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证无人机配置是否有效
    
    Args:
        config: 无人机配置
        
    Returns:
        配置是否有效
    """
    required_keys = ["connection", "video_stream", "flight_params"]
    
    for key in required_keys:
        if key not in config:
            print(f"缺少必需的配置项: {key}")
            return False
    
    # 验证飞行参数
    flight_params = config["flight_params"]
    if flight_params["max_altitude"] > 120:
        print("警告: 最大飞行高度超过法规限制(120米)")
    
    if flight_params["max_distance"] > 1000:
        print("警告: 最大飞行距离可能超出视距范围")
    
    return True

# 默认配置
DEFAULT_DRONE_TYPE = "dji_mavic_3"
DEFAULT_CONFIG = get_drone_config(DEFAULT_DRONE_TYPE)

if __name__ == "__main__":
    # 测试配置
    print("支持的无人机型号:")
    for drone_type, info in SUPPORTED_DRONES.items():
        print(f"  - {drone_type}: {info['brand']} {info['model']}")
    
    print(f"\n默认配置: {DEFAULT_DRONE_TYPE}")
    print(f"配置验证: {validate_config(DEFAULT_CONFIG['config'])}")
