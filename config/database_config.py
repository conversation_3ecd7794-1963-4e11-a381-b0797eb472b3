"""
数据库配置模块
管理数据库连接和相关配置
"""
from typing import Optional
from pydantic import BaseSettings, Field
from enum import Enum


class DatabaseType(str, Enum):
    """数据库类型枚举"""
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    SQLITE = "sqlite"


class CacheType(str, Enum):
    """缓存类型枚举"""
    REDIS = "redis"
    MEMCACHED = "memcached"
    MEMORY = "memory"


class DatabaseConfig(BaseSettings):
    """数据库配置类"""
    
    # 主数据库配置
    DATABASE_TYPE: DatabaseType = Field(default=DatabaseType.SQLITE, env="DATABASE_TYPE")
    DATABASE_URL: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # PostgreSQL配置
    POSTGRES_HOST: str = Field(default="localhost", env="POSTGRES_HOST")
    POSTGRES_PORT: int = Field(default=5432, env="POSTGRES_PORT")
    POSTGRES_USER: str = Field(default="postgres", env="POSTGRES_USER")
    POSTGRES_PASSWORD: str = Field(default="password", env="POSTGRES_PASSWORD")
    POSTGRES_DB: str = Field(default="air_traffic_police", env="POSTGRES_DB")
    
    # MySQL配置
    MYSQL_HOST: str = Field(default="localhost", env="MYSQL_HOST")
    MYSQL_PORT: int = Field(default=3306, env="MYSQL_PORT")
    MYSQL_USER: str = Field(default="root", env="MYSQL_USER")
    MYSQL_PASSWORD: str = Field(default="password", env="MYSQL_PASSWORD")
    MYSQL_DB: str = Field(default="air_traffic_police", env="MYSQL_DB")
    
    # SQLite配置
    SQLITE_PATH: str = Field(default="data/air_traffic_police.db", env="SQLITE_PATH")
    
    # 连接池配置
    POOL_SIZE: int = Field(default=10, env="DB_POOL_SIZE")
    MAX_OVERFLOW: int = Field(default=20, env="DB_MAX_OVERFLOW")
    POOL_TIMEOUT: int = Field(default=30, env="DB_POOL_TIMEOUT")
    POOL_RECYCLE: int = Field(default=3600, env="DB_POOL_RECYCLE")
    
    # 缓存配置
    CACHE_TYPE: CacheType = Field(default=CacheType.REDIS, env="CACHE_TYPE")
    CACHE_URL: Optional[str] = Field(default=None, env="CACHE_URL")
    
    # Redis配置
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_TIMEOUT: int = Field(default=5, env="REDIS_TIMEOUT")
    
    # 缓存策略配置
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")  # 秒
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    CACHE_ENABLED: bool = Field(default=True, env="CACHE_ENABLED")
    
    # 数据保留配置
    DATA_RETENTION_DAYS: int = Field(default=30, env="DATA_RETENTION_DAYS")
    LOG_RETENTION_DAYS: int = Field(default=7, env="LOG_RETENTION_DAYS")
    VIDEO_RETENTION_DAYS: int = Field(default=3, env="VIDEO_RETENTION_DAYS")
    
    # 备份配置
    BACKUP_ENABLED: bool = Field(default=True, env="BACKUP_ENABLED")
    BACKUP_INTERVAL_HOURS: int = Field(default=24, env="BACKUP_INTERVAL_HOURS")
    BACKUP_RETENTION_DAYS: int = Field(default=7, env="BACKUP_RETENTION_DAYS")
    BACKUP_PATH: str = Field(default="data/backups", env="BACKUP_PATH")
    
    # 性能配置
    QUERY_TIMEOUT: int = Field(default=30, env="QUERY_TIMEOUT")
    BULK_INSERT_SIZE: int = Field(default=1000, env="BULK_INSERT_SIZE")
    ENABLE_QUERY_LOGGING: bool = Field(default=False, env="ENABLE_QUERY_LOGGING")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        if self.DATABASE_URL:
            return self.DATABASE_URL
        
        if self.DATABASE_TYPE == DatabaseType.POSTGRESQL:
            return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
        elif self.DATABASE_TYPE == DatabaseType.MYSQL:
            return f"mysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DB}"
        elif self.DATABASE_TYPE == DatabaseType.SQLITE:
            return f"sqlite:///{self.SQLITE_PATH}"
        else:
            raise ValueError(f"Unsupported database type: {self.DATABASE_TYPE}")
    
    def get_cache_url(self) -> str:
        """获取缓存连接URL"""
        if self.CACHE_URL:
            return self.CACHE_URL
        
        if self.CACHE_TYPE == CacheType.REDIS:
            if self.REDIS_PASSWORD:
                return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
            else:
                return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        elif self.CACHE_TYPE == CacheType.MEMORY:
            return "memory://"
        else:
            raise ValueError(f"Unsupported cache type: {self.CACHE_TYPE}")
    
    def get_database_config(self) -> dict:
        """获取数据库配置字典"""
        return {
            "url": self.get_database_url(),
            "pool_size": self.POOL_SIZE,
            "max_overflow": self.MAX_OVERFLOW,
            "pool_timeout": self.POOL_TIMEOUT,
            "pool_recycle": self.POOL_RECYCLE,
            "query_timeout": self.QUERY_TIMEOUT,
            "enable_logging": self.ENABLE_QUERY_LOGGING
        }
    
    def get_cache_config(self) -> dict:
        """获取缓存配置字典"""
        return {
            "url": self.get_cache_url(),
            "ttl": self.CACHE_TTL,
            "max_size": self.CACHE_MAX_SIZE,
            "enabled": self.CACHE_ENABLED,
            "timeout": self.REDIS_TIMEOUT if self.CACHE_TYPE == CacheType.REDIS else 5
        }
    
    def validate_database_config(self) -> bool:
        """验证数据库配置的有效性"""
        try:
            # 验证端口范围
            if self.DATABASE_TYPE == DatabaseType.POSTGRESQL:
                if not (1024 <= self.POSTGRES_PORT <= 65535):
                    raise ValueError("Invalid PostgreSQL port")
            elif self.DATABASE_TYPE == DatabaseType.MYSQL:
                if not (1024 <= self.MYSQL_PORT <= 65535):
                    raise ValueError("Invalid MySQL port")
            
            if self.CACHE_TYPE == CacheType.REDIS:
                if not (1024 <= self.REDIS_PORT <= 65535):
                    raise ValueError("Invalid Redis port")
            
            # 验证池配置
            if self.POOL_SIZE <= 0:
                raise ValueError("Pool size must be positive")
            if self.MAX_OVERFLOW < 0:
                raise ValueError("Max overflow cannot be negative")
            
            # 验证保留期配置
            if self.DATA_RETENTION_DAYS <= 0:
                raise ValueError("Data retention days must be positive")
            
            return True
            
        except Exception as e:
            print(f"Database configuration validation failed: {e}")
            return False
    
    def get_database_summary(self) -> dict:
        """获取数据库配置摘要"""
        return {
            "database": {
                "type": self.DATABASE_TYPE,
                "pool_size": self.POOL_SIZE,
                "max_overflow": self.MAX_OVERFLOW
            },
            "cache": {
                "type": self.CACHE_TYPE,
                "enabled": self.CACHE_ENABLED,
                "ttl": self.CACHE_TTL
            },
            "retention": {
                "data_days": self.DATA_RETENTION_DAYS,
                "log_days": self.LOG_RETENTION_DAYS,
                "video_days": self.VIDEO_RETENTION_DAYS
            },
            "backup": {
                "enabled": self.BACKUP_ENABLED,
                "interval_hours": self.BACKUP_INTERVAL_HOURS,
                "retention_days": self.BACKUP_RETENTION_DAYS
            }
        }
