"""
硬件配置模块
管理无人机、摄像头等硬件设备的配置
"""
from typing import Dict, List, Optional, Union
from enum import Enum

try:
    # Pydantic v2
    from pydantic import BaseModel, Field
    from pydantic_settings import BaseSettings
except ImportError:
    # Pydantic v1
    from pydantic import BaseSettings, Field


class DroneType(str, Enum):
    """无人机类型枚举"""
    DJI_MAVIC_3 = "dji_mavic_3"
    DJI_AIR_2S = "dji_air_2s"
    DJI_MINI_3 = "dji_mini_3"
    CUSTOM = "custom"
    SIMULATOR = "simulator"


class CameraType(str, Enum):
    """摄像头类型枚举"""
    USB_CAMERA = "usb_camera"
    IP_CAMERA = "ip_camera"
    DRONE_CAMERA = "drone_camera"
    FILE_SOURCE = "file_source"


class CommunicationType(str, Enum):
    """通信类型枚举"""
    WIFI = "wifi"
    CELLULAR_4G = "4g"
    CELLULAR_5G = "5g"
    RADIO = "radio"


class HardwareConfig(BaseSettings):
    """硬件配置类"""
    
    # 无人机配置
    DRONE_ENABLED: bool = Field(default=False, env="DRONE_ENABLED")
    DRONE_TYPE: DroneType = Field(default=DroneType.SIMULATOR, env="DRONE_TYPE")
    DRONE_COUNT: int = Field(default=1, env="DRONE_COUNT")
    
    # 无人机连接配置
    DRONE_CONNECTION_TYPE: str = Field(default="wifi", env="DRONE_CONNECTION_TYPE")
    DRONE_IP_ADDRESS: str = Field(default="*************", env="DRONE_IP_ADDRESS")
    DRONE_PORT: int = Field(default=8080, env="DRONE_PORT")
    DRONE_USERNAME: str = Field(default="admin", env="DRONE_USERNAME")
    DRONE_PASSWORD: str = Field(default="password", env="DRONE_PASSWORD")
    
    # 无人机飞行参数
    DRONE_MAX_ALTITUDE: float = Field(default=120.0, env="DRONE_MAX_ALTITUDE")  # 米
    DRONE_MAX_SPEED: float = Field(default=15.0, env="DRONE_MAX_SPEED")  # m/s
    DRONE_MAX_DISTANCE: float = Field(default=500.0, env="DRONE_MAX_DISTANCE")  # 米
    DRONE_BATTERY_WARNING: float = Field(default=20.0, env="DRONE_BATTERY_WARNING")  # 百分比
    DRONE_AUTO_RETURN_BATTERY: float = Field(default=15.0, env="DRONE_AUTO_RETURN_BATTERY")  # 百分比
    
    # 摄像头配置
    CAMERA_ENABLED: bool = Field(default=True, env="CAMERA_ENABLED")
    CAMERA_TYPE: CameraType = Field(default=CameraType.FILE_SOURCE, env="CAMERA_TYPE")
    CAMERA_COUNT: int = Field(default=1, env="CAMERA_COUNT")
    
    # 摄像头参数
    CAMERA_RESOLUTION_WIDTH: int = Field(default=1920, env="CAMERA_RESOLUTION_WIDTH")
    CAMERA_RESOLUTION_HEIGHT: int = Field(default=1080, env="CAMERA_RESOLUTION_HEIGHT")
    CAMERA_FPS: int = Field(default=30, env="CAMERA_FPS")
    CAMERA_CODEC: str = Field(default="h264", env="CAMERA_CODEC")
    
    # IP摄像头配置
    IP_CAMERA_URLS: List[str] = Field(default_factory=lambda: ["rtsp://admin:password@*************:554/stream1"])
    IP_CAMERA_TIMEOUT: int = Field(default=10, env="IP_CAMERA_TIMEOUT")
    
    # USB摄像头配置
    USB_CAMERA_DEVICES: List[int] = Field(default_factory=lambda: [0])
    USB_CAMERA_BUFFER_SIZE: int = Field(default=1, env="USB_CAMERA_BUFFER_SIZE")
    
    # 文件源配置
    VIDEO_FILE_PATH: str = Field(default="data/videos/action1.mp4", env="VIDEO_FILE_PATH")
    VIDEO_LOOP: bool = Field(default=True, env="VIDEO_LOOP")
    
    # 通信配置
    COMMUNICATION_TYPE: CommunicationType = Field(default=CommunicationType.WIFI, env="COMMUNICATION_TYPE")
    WIFI_SSID: str = Field(default="DroneNetwork", env="WIFI_SSID")
    WIFI_PASSWORD: str = Field(default="password123", env="WIFI_PASSWORD")
    
    # 4G/5G配置
    CELLULAR_APN: str = Field(default="internet", env="CELLULAR_APN")
    CELLULAR_USERNAME: str = Field(default="", env="CELLULAR_USERNAME")
    CELLULAR_PASSWORD: str = Field(default="", env="CELLULAR_PASSWORD")
    
    # 传感器配置
    GPS_ENABLED: bool = Field(default=True, env="GPS_ENABLED")
    IMU_ENABLED: bool = Field(default=True, env="IMU_ENABLED")
    BAROMETER_ENABLED: bool = Field(default=True, env="BAROMETER_ENABLED")
    
    # 云台配置
    GIMBAL_ENABLED: bool = Field(default=True, env="GIMBAL_ENABLED")
    GIMBAL_PITCH_RANGE: tuple = Field(default=(-90, 30))
    GIMBAL_YAW_RANGE: tuple = Field(default=(-180, 180))
    GIMBAL_STABILIZATION: bool = Field(default=True, env="GIMBAL_STABILIZATION")
    
    # 存储配置
    ONBOARD_STORAGE_ENABLED: bool = Field(default=True, env="ONBOARD_STORAGE_ENABLED")
    ONBOARD_STORAGE_CAPACITY_GB: int = Field(default=64, env="ONBOARD_STORAGE_CAPACITY_GB")
    AUTO_DOWNLOAD_MEDIA: bool = Field(default=True, env="AUTO_DOWNLOAD_MEDIA")
    
    # 安全配置
    GEOFENCE_ENABLED: bool = Field(default=True, env="GEOFENCE_ENABLED")
    GEOFENCE_RADIUS: float = Field(default=1000.0, env="GEOFENCE_RADIUS")  # 米
    OBSTACLE_AVOIDANCE: bool = Field(default=True, env="OBSTACLE_AVOIDANCE")
    EMERGENCY_LANDING: bool = Field(default=True, env="EMERGENCY_LANDING")
    
    # 维护配置
    AUTO_CALIBRATION: bool = Field(default=True, env="AUTO_CALIBRATION")
    MAINTENANCE_INTERVAL_HOURS: int = Field(default=100, env="MAINTENANCE_INTERVAL_HOURS")
    FIRMWARE_AUTO_UPDATE: bool = Field(default=False, env="FIRMWARE_AUTO_UPDATE")
    
    # Pydantic v2 配置
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"
    }
    
    def get_drone_config(self, drone_id: int = 0) -> Dict:
        """获取指定无人机的配置"""
        return {
            "id": drone_id,
            "type": self.DRONE_TYPE,
            "connection": {
                "type": self.DRONE_CONNECTION_TYPE,
                "ip": self.DRONE_IP_ADDRESS,
                "port": self.DRONE_PORT,
                "username": self.DRONE_USERNAME,
                "password": self.DRONE_PASSWORD
            },
            "flight_params": {
                "max_altitude": self.DRONE_MAX_ALTITUDE,
                "max_speed": self.DRONE_MAX_SPEED,
                "max_distance": self.DRONE_MAX_DISTANCE,
                "battery_warning": self.DRONE_BATTERY_WARNING,
                "auto_return_battery": self.DRONE_AUTO_RETURN_BATTERY
            },
            "safety": {
                "geofence_enabled": self.GEOFENCE_ENABLED,
                "geofence_radius": self.GEOFENCE_RADIUS,
                "obstacle_avoidance": self.OBSTACLE_AVOIDANCE,
                "emergency_landing": self.EMERGENCY_LANDING
            }
        }
    
    def get_camera_config(self, camera_id: int = 0) -> Dict:
        """获取指定摄像头的配置"""
        config = {
            "id": camera_id,
            "type": self.CAMERA_TYPE,
            "resolution": {
                "width": self.CAMERA_RESOLUTION_WIDTH,
                "height": self.CAMERA_RESOLUTION_HEIGHT
            },
            "fps": self.CAMERA_FPS,
            "codec": self.CAMERA_CODEC
        }
        
        if self.CAMERA_TYPE == CameraType.IP_CAMERA:
            config["url"] = self.IP_CAMERA_URLS[camera_id] if camera_id < len(self.IP_CAMERA_URLS) else self.IP_CAMERA_URLS[0]
            config["timeout"] = self.IP_CAMERA_TIMEOUT
        elif self.CAMERA_TYPE == CameraType.USB_CAMERA:
            config["device"] = self.USB_CAMERA_DEVICES[camera_id] if camera_id < len(self.USB_CAMERA_DEVICES) else 0
            config["buffer_size"] = self.USB_CAMERA_BUFFER_SIZE
        elif self.CAMERA_TYPE == CameraType.FILE_SOURCE:
            config["file_path"] = self.VIDEO_FILE_PATH
            config["loop"] = self.VIDEO_LOOP
        
        return config
    
    def get_communication_config(self) -> Dict:
        """获取通信配置"""
        config = {
            "type": self.COMMUNICATION_TYPE
        }
        
        if self.COMMUNICATION_TYPE == CommunicationType.WIFI:
            config.update({
                "ssid": self.WIFI_SSID,
                "password": self.WIFI_PASSWORD
            })
        elif self.COMMUNICATION_TYPE in [CommunicationType.CELLULAR_4G, CommunicationType.CELLULAR_5G]:
            config.update({
                "apn": self.CELLULAR_APN,
                "username": self.CELLULAR_USERNAME,
                "password": self.CELLULAR_PASSWORD
            })
        
        return config
    
    def validate_hardware_config(self) -> bool:
        """验证硬件配置的有效性"""
        try:
            # 验证无人机配置
            if self.DRONE_ENABLED:
                if self.DRONE_COUNT <= 0:
                    raise ValueError("Drone count must be positive")
                if not (0 < self.DRONE_MAX_ALTITUDE <= 500):
                    raise ValueError("Invalid drone max altitude")
                if not (0 < self.DRONE_MAX_SPEED <= 30):
                    raise ValueError("Invalid drone max speed")
            
            # 验证摄像头配置
            if self.CAMERA_ENABLED:
                if self.CAMERA_COUNT <= 0:
                    raise ValueError("Camera count must be positive")
                if self.CAMERA_FPS <= 0 or self.CAMERA_FPS > 60:
                    raise ValueError("Invalid camera FPS")
            
            # 验证分辨率
            if self.CAMERA_RESOLUTION_WIDTH <= 0 or self.CAMERA_RESOLUTION_HEIGHT <= 0:
                raise ValueError("Invalid camera resolution")
            
            return True
            
        except Exception as e:
            print(f"Hardware configuration validation failed: {e}")
            return False
    
    def get_hardware_summary(self) -> Dict:
        """获取硬件配置摘要"""
        return {
            "drones": {
                "enabled": self.DRONE_ENABLED,
                "count": self.DRONE_COUNT,
                "type": self.DRONE_TYPE
            },
            "cameras": {
                "enabled": self.CAMERA_ENABLED,
                "count": self.CAMERA_COUNT,
                "type": self.CAMERA_TYPE,
                "resolution": f"{self.CAMERA_RESOLUTION_WIDTH}x{self.CAMERA_RESOLUTION_HEIGHT}",
                "fps": self.CAMERA_FPS
            },
            "communication": {
                "type": self.COMMUNICATION_TYPE
            },
            "safety": {
                "geofence": self.GEOFENCE_ENABLED,
                "obstacle_avoidance": self.OBSTACLE_AVOIDANCE,
                "emergency_landing": self.EMERGENCY_LANDING
            }
        }
