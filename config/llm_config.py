#!/usr/bin/env python3
"""
大语言模型配置文件
支持多种LLM提供商和本地模型
"""

import os
from typing import Dict, Any, List

# OpenAI GPT 配置
OPENAI_CONFIG = {
    "api_key": os.getenv("OPENAI_API_KEY", "sk-your-openai-api-key-here"),
    "organization": os.getenv("OPENAI_ORG_ID", ""),
    "base_url": "https://api.openai.com/v1",
    
    # 模型配置
    "models": {
        "gpt-4-turbo": {
            "model_name": "gpt-4-turbo-preview",
            "max_tokens": 4096,
            "temperature": 0.7,
            "top_p": 0.9,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "cost_per_1k_tokens": {"input": 0.01, "output": 0.03}
        },
        "gpt-4": {
            "model_name": "gpt-4",
            "max_tokens": 8192,
            "temperature": 0.7,
            "top_p": 0.9,
            "cost_per_1k_tokens": {"input": 0.03, "output": 0.06}
        },
        "gpt-3.5-turbo": {
            "model_name": "gpt-3.5-turbo",
            "max_tokens": 4096,
            "temperature": 0.7,
            "top_p": 0.9,
            "cost_per_1k_tokens": {"input": 0.0015, "output": 0.002}
        }
    },
    
    # 请求配置
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 1,
    "rate_limit": {
        "requests_per_minute": 60,
        "tokens_per_minute": 90000
    }
}

# Azure OpenAI 配置
AZURE_OPENAI_CONFIG = {
    "api_key": os.getenv("AZURE_OPENAI_API_KEY", "your-azure-api-key"),
    "api_base": os.getenv("AZURE_OPENAI_ENDPOINT", "https://your-resource.openai.azure.com/"),
    "api_version": "2024-02-15-preview",
    
    # 部署配置
    "deployments": {
        "gpt-4": {
            "deployment_name": "gpt-4-deployment",
            "model_version": "0613",
            "max_tokens": 8192,
            "temperature": 0.7
        },
        "gpt-35-turbo": {
            "deployment_name": "gpt-35-turbo-deployment", 
            "model_version": "0613",
            "max_tokens": 4096,
            "temperature": 0.7
        }
    },
    
    "timeout": 30,
    "max_retries": 3
}

# 百度文心一言配置
BAIDU_ERNIE_CONFIG = {
    "api_key": os.getenv("BAIDU_API_KEY", "your-baidu-api-key"),
    "secret_key": os.getenv("BAIDU_SECRET_KEY", "your-baidu-secret-key"),
    "base_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat",
    
    "models": {
        "ernie-bot-4": {
            "model": "completions_pro",
            "max_tokens": 2048,
            "temperature": 0.7,
            "top_p": 0.8
        },
        "ernie-bot": {
            "model": "completions",
            "max_tokens": 2048,
            "temperature": 0.7,
            "top_p": 0.8
        }
    }
}

# 阿里通义千问配置
ALIBABA_QWEN_CONFIG = {
    "api_key": os.getenv("DASHSCOPE_API_KEY", "your-dashscope-api-key"),
    "base_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
    
    "models": {
        "qwen-turbo": {
            "model": "qwen-turbo",
            "max_tokens": 2048,
            "temperature": 0.7,
            "top_p": 0.8
        },
        "qwen-plus": {
            "model": "qwen-plus",
            "max_tokens": 8192,
            "temperature": 0.7,
            "top_p": 0.8
        }
    }
}

# 本地模型配置
LOCAL_MODELS_CONFIG = {
    "llama2": {
        "model_path": "models/llama/llama-2-7b-chat.q4_0.bin",
        "model_type": "llama",
        "context_length": 2048,
        "gpu_layers": 35,
        "threads": 8,
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "repeat_penalty": 1.1
    },
    "chatglm3": {
        "model_path": "models/chatglm/chatglm3-6b",
        "model_type": "chatglm",
        "device": "cuda",
        "precision": "fp16",
        "max_length": 2048,
        "temperature": 0.7,
        "top_p": 0.8
    },
    "baichuan2": {
        "model_path": "models/baichuan/Baichuan2-7B-Chat",
        "model_type": "baichuan",
        "device": "cuda",
        "precision": "fp16",
        "max_length": 2048,
        "temperature": 0.7,
        "top_p": 0.8
    },
    "qwen": {
        "model_path": "models/qwen/Qwen-7B-Chat",
        "model_type": "qwen",
        "device": "cuda", 
        "precision": "fp16",
        "max_length": 2048,
        "temperature": 0.7,
        "top_p": 0.8
    }
}

# 提示词模板
PROMPT_TEMPLATES = {
    "traffic_report": {
        "system": """你是一个专业的交通分析师，负责分析无人机采集的交通数据并生成报告。
请基于检测数据提供准确、专业的分析。""",
        
        "user": """基于以下交通检测数据，生成一份专业的交通状况报告：

检测时间: {timestamp}
检测位置: {location}
检测数据:
{detection_data}

请分析以下方面：
1. 交通流量状况
2. 车辆类型分布
3. 潜在安全隐患
4. 违法行为识别
5. 改善建议

要求：
- 语言专业、简洁
- 突出重点问题
- 提供可行建议
- 字数控制在500字以内"""
    },
    
    "violation_analysis": {
        "system": """你是一个交通违法行为分析专家，能够识别和分析各种交通违法行为。""",
        
        "user": """分析以下检测到的交通违法行为：

违法类型: {violation_type}
发生时间: {timestamp}
发生地点: {location}
涉及车辆: {vehicles}
详细描述: {description}

请提供：
1. 违法行为严重程度评估
2. 可能的安全风险
3. 处罚建议
4. 预防措施"""
    },
    
    "emergency_alert": {
        "system": """你是一个紧急情况处理专家，能够快速分析交通事故和紧急情况。""",
        
        "user": """紧急情况报告：

事件类型: {event_type}
发生时间: {timestamp}
发生地点: {location}
严重程度: {severity}
涉及人员/车辆: {involved}
现场描述: {description}

请立即提供：
1. 紧急响应建议
2. 救援资源需求
3. 交通管制措施
4. 后续处理步骤"""
    },
    
    "traffic_optimization": {
        "system": """你是一个交通优化专家，能够基于数据分析提供交通改善方案。""",
        
        "user": """交通优化分析：

路段信息: {road_info}
时间段: {time_period}
交通数据: {traffic_data}
问题描述: {issues}

请提供：
1. 交通流量分析
2. 瓶颈识别
3. 优化方案
4. 预期效果
5. 实施建议"""
    }
}

# 语音合成配置
TTS_CONFIG = {
    "azure_speech": {
        "subscription_key": os.getenv("AZURE_SPEECH_KEY", "your-azure-speech-key"),
        "region": os.getenv("AZURE_SPEECH_REGION", "eastus"),
        "voice_name": "zh-CN-XiaoxiaoNeural",
        "speech_rate": "0%",
        "speech_pitch": "0%",
        "output_format": "audio-16khz-128kbitrate-mono-mp3"
    },
    
    "baidu_tts": {
        "app_id": os.getenv("BAIDU_TTS_APP_ID", "your-app-id"),
        "api_key": os.getenv("BAIDU_TTS_API_KEY", "your-api-key"),
        "secret_key": os.getenv("BAIDU_TTS_SECRET_KEY", "your-secret-key"),
        "voice": "5118",  # 度小宇
        "speed": "5",
        "pitch": "5",
        "volume": "5"
    },
    
    "local_tts": {
        "engine": "pyttsx3",
        "voice_id": 0,  # 0: 男声, 1: 女声
        "rate": 200,    # 语速
        "volume": 0.8   # 音量
    }
}

# LLM 提供商优先级
LLM_PROVIDERS = [
    {
        "name": "openai",
        "config": OPENAI_CONFIG,
        "priority": 1,
        "enabled": True
    },
    {
        "name": "azure_openai", 
        "config": AZURE_OPENAI_CONFIG,
        "priority": 2,
        "enabled": False
    },
    {
        "name": "baidu_ernie",
        "config": BAIDU_ERNIE_CONFIG,
        "priority": 3,
        "enabled": False
    },
    {
        "name": "alibaba_qwen",
        "config": ALIBABA_QWEN_CONFIG,
        "priority": 4,
        "enabled": False
    },
    {
        "name": "local_llama2",
        "config": LOCAL_MODELS_CONFIG["llama2"],
        "priority": 5,
        "enabled": True
    }
]

# 内容过滤配置
CONTENT_FILTER = {
    "enabled": True,
    "filter_level": "medium",  # low, medium, high
    "blocked_categories": [
        "hate_speech",
        "violence", 
        "sexual_content",
        "self_harm"
    ],
    "custom_filters": [
        # 自定义过滤规则
    ]
}

# 缓存配置
CACHE_CONFIG = {
    "enabled": True,
    "backend": "redis",  # redis, memory, file
    "ttl": 3600,        # 缓存时间(秒)
    "max_size": 1000,   # 最大缓存条目数
    "key_prefix": "llm_cache:"
}

def get_llm_config(provider_name: str) -> Dict[str, Any]:
    """
    获取指定LLM提供商的配置
    
    Args:
        provider_name: 提供商名称
        
    Returns:
        LLM配置字典
    """
    provider_configs = {
        "openai": OPENAI_CONFIG,
        "azure_openai": AZURE_OPENAI_CONFIG,
        "baidu_ernie": BAIDU_ERNIE_CONFIG,
        "alibaba_qwen": ALIBABA_QWEN_CONFIG,
        "local_models": LOCAL_MODELS_CONFIG
    }
    
    if provider_name not in provider_configs:
        raise ValueError(f"不支持的LLM提供商: {provider_name}")
    
    return provider_configs[provider_name]

def get_available_providers() -> List[str]:
    """
    获取可用的LLM提供商列表
    
    Returns:
        可用提供商列表
    """
    available = []
    
    for provider in LLM_PROVIDERS:
        if provider["enabled"]:
            # 检查API密钥是否配置
            config = provider["config"]
            if "api_key" in config and config["api_key"] != "your-api-key-here":
                available.append(provider["name"])
            elif "model_path" in config:  # 本地模型
                import os
                if os.path.exists(config["model_path"]):
                    available.append(provider["name"])
    
    return available

def validate_llm_config(provider_name: str) -> bool:
    """
    验证LLM配置是否有效
    
    Args:
        provider_name: 提供商名称
        
    Returns:
        配置是否有效
    """
    try:
        config = get_llm_config(provider_name)
        
        if provider_name in ["openai", "azure_openai", "baidu_ernie", "alibaba_qwen"]:
            # 检查API密钥
            if not config.get("api_key") or "your-" in config["api_key"]:
                return False
        
        elif provider_name == "local_models":
            # 检查模型文件
            for model_name, model_config in config.items():
                if not os.path.exists(model_config["model_path"]):
                    return False
        
        return True
        
    except Exception:
        return False

# 默认配置
DEFAULT_LLM_PROVIDER = "openai"
DEFAULT_MODEL = "gpt-3.5-turbo"
DEFAULT_TTS_PROVIDER = "azure_speech"

if __name__ == "__main__":
    # 测试配置
    print("可用的LLM提供商:")
    for provider in get_available_providers():
        print(f"  - {provider}")
    
    print(f"\n默认LLM提供商: {DEFAULT_LLM_PROVIDER}")
    print(f"配置验证: {validate_llm_config(DEFAULT_LLM_PROVIDER)}")
    
    print(f"\n默认TTS提供商: {DEFAULT_TTS_PROVIDER}")
    print(f"提示词模板数量: {len(PROMPT_TEMPLATES)}")
