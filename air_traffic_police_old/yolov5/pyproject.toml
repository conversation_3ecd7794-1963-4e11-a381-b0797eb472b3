# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Overview:
# This pyproject.toml file manages the build, packaging, and distribution of the Ultralytics library.
# It defines essential project metadata, dependencies, and settings used to develop and deploy the library.

# Key Sections:
# - [build-system]: Specifies the build requirements and backend (e.g., setuptools, wheel).
# - [project]: Includes details like name, version, description, authors, dependencies and more.
# - [project.optional-dependencies]: Provides additional, optional packages for extended features.
# - [tool.*]: Configures settings for various tools (pytest, yapf, etc.) used in the project.

# Installation:
# The Ultralytics library can be installed using the command: 'pip install ultralytics'
# For development purposes, you can install the package in editable mode with: 'pip install -e .'
# This approach allows for real-time code modifications without the need for re-installation.

# Documentation:
# For comprehensive documentation and usage instructions, visit: https://docs.ultralytics.com

[build-system]
requires = ["setuptools>=43.0.0", "wheel"]
build-backend = "setuptools.build_meta"

# Project settings -----------------------------------------------------------------------------------------------------
[project]
version = "7.0.0"
name = "YOLOv5"
description = "Ultralytics YOLOv5 for SOTA object detection, instance segmentation and image classification."
readme = "README.md"
requires-python = ">=3.8"
license = { "text" = "AGPL-3.0" }
keywords = ["machine-learning", "deep-learning", "computer-vision", "ML", "DL", "AI", "YOLO", "YOLOv3", "YOLOv5", "YOLOv8", "HUB", "Ultralytics"]
authors = [
    { name = "Glenn Jocher" },
    { name = "Ayush Chaurasia" },
    { name = "Jing Qiu" }
]
maintainers = [
    { name = "Glenn Jocher" },
    { name = "Ayush Chaurasia" },
    { name = "Jing Qiu" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Recognition",
    "Operating System :: POSIX :: Linux",
    "Operating System :: MacOS",
    "Operating System :: Microsoft :: Windows",
]

# Required dependencies ------------------------------------------------------------------------------------------------
dependencies = [
    "matplotlib>=3.3.0",
    "numpy>=1.22.2",
    "opencv-python>=4.6.0",
    "pillow>=7.1.2",
    "pyyaml>=5.3.1",
    "requests>=2.23.0",
    "scipy>=1.4.1",
    "torch>=1.8.0",
    "torchvision>=0.9.0",
    "tqdm>=4.64.0", # progress bars
    "psutil", # system utilization
    "py-cpuinfo", # display CPU info
    "thop>=0.1.1", # FLOPs computation
    "pandas>=1.1.4",
    "seaborn>=0.11.0", # plotting
    "ultralytics>=8.1.47"
]

# Optional dependencies ------------------------------------------------------------------------------------------------
[project.optional-dependencies]
dev = [
    "ipython",
    "check-manifest",
    "pytest",
    "pytest-cov",
    "coverage[toml]",
    "mkdocs-material",
    "mkdocstrings[python]",
    "mkdocs-ultralytics-plugin>=0.0.34", # for meta descriptions and images, dates and authors
]
export = [
    "onnx>=1.12.0", # ONNX export
    "coremltools>=7.0; platform_system != 'Windows'", # CoreML only supported on macOS and Linux
    "openvino-dev>=2023.0", # OpenVINO export
    "tensorflow>=2.0.0", # TF bug https://github.com/ultralytics/ultralytics/issues/5161
    "tensorflowjs>=3.9.0", # TF.js export, automatically installs tensorflow
]
# tensorflow>=2.4.1,<=2.13.1  # TF exports (-cpu, -aarch64, -macos)
# tflite-support  # for TFLite model metadata
# scikit-learn==0.19.2  # CoreML quantization
# nvidia-pyindex  # TensorRT export
# nvidia-tensorrt  # TensorRT export
logging = [
    "comet", # https://docs.ultralytics.com/integrations/comet/
    "tensorboard>=2.13.0",
    "dvclive>=2.12.0",
]
extra = [
    "ipython", # interactive notebook
    "albumentations>=1.0.3", # training augmentations
    "pycocotools>=2.0.6", # COCO mAP
]

[project.urls]
"Bug Reports" = "https://github.com/ultralytics/yolov5/issues"
"Funding" = "https://ultralytics.com"
"Source" = "https://github.com/ultralytics/yolov5/"

# Tools settings -------------------------------------------------------------------------------------------------------
[tool.pytest]
norecursedirs = [".git", "dist", "build"]
addopts = "--doctest-modules --durations=30 --color=yes"

[tool.isort]
line_length = 120
multi_line_output = 0

[tool.ruff]
line-length = 120

[tool.docformatter]
wrap-summaries = 120
wrap-descriptions = 120
in-place = true
pre-summary-newline = true
close-quotes-on-newline = true

[tool.codespell]
ignore-words-list = "crate,nd,strack,dota,ane,segway,fo,gool,winn,commend"
skip = '*.csv,*venv*,docs/??/,docs/mkdocs_??.yml'
