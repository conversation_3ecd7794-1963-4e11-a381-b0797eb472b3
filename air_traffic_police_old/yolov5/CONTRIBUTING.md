<a href="https://www.ultralytics.com/"><img src="https://raw.githubusercontent.com/ultralytics/assets/main/logo/Ultralytics_Logotype_Original.svg" width="320" alt="Ultralytics logo"></a>

# Contributing to YOLO 🚀

We value your input and are committed to making contributing to YOLO as easy and transparent as possible. Whether you're:

- Reporting a bug
- Discussing the current state of the codebase
- Submitting a fix
- Proposing a new feature
- Interested in becoming a maintainer

Ultralytics YOLO thrives thanks to the collective efforts of our community. Every improvement you contribute helps push the boundaries of what's possible in AI! 😃

## 🛠️ Submitting a Pull Request (PR)

Submitting a PR is straightforward! Here’s an example showing how to update `requirements.txt` in four simple steps:

### 1. Select the File to Update

Click on `requirements.txt` in the GitHub repository.

<p align="center"><img width="800" alt="PR_step1" src="https://user-images.githubusercontent.com/26833433/122260847-08be2600-ced4-11eb-828b-8287ace4136c.png"></p>

### 2. Click 'Edit this file'

Find the 'Edit this file' button in the top-right corner.

<p align="center"><img width="800" alt="PR_step2" src="https://user-images.githubusercontent.com/26833433/122260844-06f46280-ced4-11eb-9eec-b8a24be519ca.png"></p>

### 3. Make Your Changes

For example, update the `matplotlib` version from `3.2.2` to `3.3`.

<p align="center"><img width="800" alt="PR_step3" src="https://user-images.githubusercontent.com/26833433/122260853-0a87e980-ced4-11eb-9fd2-3650fb6e0842.png"></p>

### 4. Preview Changes and Submit Your PR

Click the **Preview changes** tab to review your updates. At the bottom, select 'Create a new branch for this commit', give your branch a descriptive name like `fix/matplotlib_version`, and click the green **Propose changes** button. Your PR is now submitted for review! 😃

<p align="center"><img width="800" alt="PR_step4" src="https://user-images.githubusercontent.com/26833433/122260856-0b208000-ced4-11eb-8e8e-77b6151cbcc3.png"></p>

### PR Best Practices

To ensure your work is integrated smoothly, please:

- ✅ Make sure your PR is **up-to-date** with the `ultralytics/yolov5` `master` branch. If your branch is behind, update it using the 'Update branch' button or by running `git pull` and `git merge master` locally.

<p align="center"><img width="751" alt="Screenshot 2022-08-29 at 22 47 15" src="https://user-images.githubusercontent.com/26833433/187295893-50ed9f44-b2c9-4138-a614-de69bd1753d7.png"></p>

- ✅ Ensure all YOLO Continuous Integration (CI) **checks are passing**.

<p align="center"><img width="751" alt="Screenshot 2022-08-29 at 22 47 03" src="https://user-images.githubusercontent.com/26833433/187296922-545c5498-f64a-4d8c-8300-5fa764360da6.png"></p>

- ✅ Limit your changes to the **minimum** required for your bug fix or feature.  
  _"It is not daily increase but daily decrease, hack away the unessential. The closer to the source, the less wastage there is."_ — Bruce Lee

## 🐛 Submitting a Bug Report

If you encounter an issue with YOLO, please submit a bug report!

To help us investigate, we need to be able to reproduce the problem. Follow these guidelines to provide what we need to get started:

When asking a question or reporting a bug, you'll get better help if you provide **code** that others can easily understand and use to **reproduce** the issue. This is known as a [minimum reproducible example](https://docs.ultralytics.com/help/minimum-reproducible-example/). Your code should be:

- ✅ **Minimal** – Use as little code as possible that still produces the issue
- ✅ **Complete** – Include all parts needed for someone else to reproduce the problem
- ✅ **Reproducible** – Test your code to ensure it actually reproduces the issue

Additionally, for [Ultralytics](https://www.ultralytics.com/) to assist you, your code should be:

- ✅ **Current** – Ensure your code is up-to-date with the latest [master branch](https://github.com/ultralytics/yolov5/tree/master). Use `git pull` or `git clone` to get the latest version and confirm your issue hasn't already been fixed.
- ✅ **Unmodified** – The problem must be reproducible without any custom modifications to the repository. [Ultralytics](https://www.ultralytics.com/) does not provide support for custom code ⚠️.

If your issue meets these criteria, please close your current issue and open a new one using the 🐛 **Bug Report** [template](https://github.com/ultralytics/yolov5/issues/new/choose), including your [minimum reproducible example](https://docs.ultralytics.com/help/minimum-reproducible-example/) to help us diagnose your problem.

## 📄 License

By contributing, you agree that your contributions will be licensed under the [AGPL-3.0 license](https://choosealicense.com/licenses/agpl-3.0/).

---

For more details on contributing, check out the [Ultralytics open-source contributing guide](https://docs.ultralytics.com/help/contributing/), and explore our [Ultralytics blog](https://www.ultralytics.com/blog) for community highlights and best practices.

We welcome your contributions—thank you for helping make Ultralytics YOLO better! 🚀

[![Ultralytics open-source contributors](https://raw.githubusercontent.com/ultralytics/assets/main/im/image-contributors.png)](https://github.com/ultralytics/ultralytics/graphs/contributors)
