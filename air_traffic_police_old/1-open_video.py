import os.path

import cv2


def show_video(src, syn_frame=False):
    """
    显示视频
    :param src: 视频路径(如 data/videos/action1.mp4)，可以是视频文件或摄像头设备（如/dev/video0）
    :param syn_frame: 是否同步帧率
    :return:
    """
    cap = cv2.VideoCapture(src)  # 替换为你的视频路径
    delay = 1
    if syn_frame:
        # 获取视频的帧率
        fps = cap.get(cv2.CAP_PROP_FPS)
        delay = int(1000 / fps) if fps > 0 else 33  # fallback 为 30FPS 时33ms
        print(f"帧率={fps},延时={delay}ms")
    # 设置可缩放的窗口
    window_title = "Traffic Flow"
    cv2.namedWindow(window_title, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_title, 640, 480)
    video_opened = cap.isOpened()
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        cv2.imshow(window_title, frame)
        if cv2.waitKey(delay) & 0xFF == ord('q'):
            break

    if not video_opened:
        print("未能打开视频，请检查视频路径:", os.path.abspath(src))
    try:
        cap.release()
        cv2.destroyAllWindows()
    except Exception as e:
        print("读取摄像头异常:", e)


if __name__ == '__main__':
    # show_video("../data/video/action.mp4", syn_frame=True)
    show_video("/dev/video0", syn_frame=True)
