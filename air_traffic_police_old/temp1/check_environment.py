#!/usr/bin/env python3
"""
环境检查脚本
检查conda环境、依赖包和项目文件的完整性
"""
import sys
import os
import subprocess
import importlib.util


def check_conda_env():
    """检查conda环境"""
    print("📦 Conda环境检查:")
    try:
        # 检查当前是否在conda环境中
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env:
            print(f"  ✅ 当前conda环境: {conda_env}")
            if conda_env == 'yolov5':
                print("  ✅ 正在使用推荐的 'yolov5' 环境")
            else:
                print("  ⚠️  建议使用 'yolov5' 环境")
        else:
            print("  ❌ 未检测到conda环境")
            
        # 检查conda命令
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ Conda版本: {result.stdout.strip()}")
        else:
            print("  ❌ conda命令不可用")
            
    except Exception as e:
        print(f"  ❌ Conda检查失败: {e}")


def check_python_version():
    """检查Python版本"""
    print(f"\n🐍 Python环境:")
    print(f"  ✅ Python版本: {sys.version}")
    print(f"  ✅ Python路径: {sys.executable}")
    
    # 检查Python版本是否合适
    version_info = sys.version_info
    if version_info.major == 3 and version_info.minor >= 7:
        print("  ✅ Python版本兼容")
    else:
        print("  ⚠️  建议使用Python 3.7+")


def check_packages():
    """检查必要的包"""
    print(f"\n📚 依赖包检查:")
    
    packages = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'cv2': 'OpenCV',
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib',
        'PIL': 'Pillow',
        'psutil': 'PSUtil (可选)'
    }
    
    installed_packages = []
    missing_packages = []
    
    for pkg, name in packages.items():
        try:
            spec = importlib.util.find_spec(pkg)
            if spec is not None:
                module = importlib.import_module(pkg)
                version = getattr(module, '__version__', 'unknown')
                print(f"  ✅ {name}: {version}")
                installed_packages.append(pkg)
            else:
                print(f"  ❌ {name}: 未安装")
                missing_packages.append(pkg)
        except ImportError:
            print(f"  ❌ {name}: 导入失败")
            missing_packages.append(pkg)
        except Exception as e:
            print(f"  ⚠️  {name}: 检查异常 ({e})")
    
    return installed_packages, missing_packages


def check_project_files():
    """检查项目文件"""
    print(f"\n📁 项目文件检查:")
    
    core_files = [
        'config.py',
        'traffic_utils.py', 
        'detector.py',
        'data_manager.py',
        'visualization.py',
        'accident_detector.py',
        'traffic_monitor.py',
        'optimized_traffic_monitor.py'
    ]
    
    example_files = [
        'simple_example.py',
        'example_usage.py',
        'test_refactored_code.py'
    ]
    
    doc_files = [
        'README.md',
        'REFACTORING_REPORT.md',
        'CONDA_SETUP_GUIDE.md',
        'QUICK_FIX_GUIDE.md'
    ]
    
    def check_file_group(files, group_name):
        missing = []
        for file in files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"  ✅ {file} ({size} bytes)")
            else:
                print(f"  ❌ {file} 缺失")
                missing.append(file)
        return missing
    
    print("  核心模块:")
    missing_core = check_file_group(core_files, "核心模块")
    
    print("  示例文件:")
    missing_examples = check_file_group(example_files, "示例文件")
    
    print("  文档文件:")
    missing_docs = check_file_group(doc_files, "文档文件")
    
    return missing_core, missing_examples, missing_docs


def check_yolov5_integration():
    """检查YOLOv5集成"""
    print(f"\n🎯 YOLOv5集成检查:")
    
    yolov5_path = "yolov5"
    if os.path.exists(yolov5_path):
        print(f"  ✅ YOLOv5目录存在")
        
        # 检查关键文件
        key_files = [
            "yolov5/models/experimental.py",
            "yolov5/utils/general.py",
            "yolov5/requirements.txt"
        ]
        
        for file in key_files:
            if os.path.exists(file):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} 缺失")
    else:
        print(f"  ❌ YOLOv5目录不存在")
    
    # 检查模型文件
    model_files = ["yolov5s.pt", "yolov5m.pt", "yolov5l.pt"]
    found_models = []
    for model in model_files:
        if os.path.exists(model):
            size = os.path.getsize(model) / (1024*1024)  # MB
            print(f"  ✅ 模型文件: {model} ({size:.1f} MB)")
            found_models.append(model)
    
    if not found_models:
        print("  ⚠️  未找到预训练模型文件")


def test_core_functionality():
    """测试核心功能"""
    print(f"\n🧪 核心功能测试:")
    
    try:
        # 测试配置模块
        from config import Config, default_config
        print("  ✅ 配置模块导入成功")
        
        # 测试工具函数
        from traffic_utils import compute_iou, FrameBuffer
        print("  ✅ 工具函数模块导入成功")
        
        # 测试数据类
        from detector import Detection
        print("  ✅ 检测数据类导入成功")
        
        # 简单功能测试
        iou = compute_iou((0, 0, 10, 10), (5, 5, 15, 15))
        print(f"  ✅ IoU计算测试: {iou:.3f}")
        
        buffer = FrameBuffer(3)
        print(f"  ✅ 帧缓冲区测试: 大小={buffer.size()}")
        
        config = Config()
        print(f"  ✅ 配置创建测试: 阈值={config.model.conf_threshold}")
        
    except Exception as e:
        print(f"  ❌ 核心功能测试失败: {e}")


def generate_installation_commands(missing_packages):
    """生成安装命令"""
    if not missing_packages:
        return
    
    print(f"\n🔧 安装建议:")
    print("  使用conda安装（推荐）:")
    
    conda_packages = {
        'torch': 'pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia',
        'cv2': 'opencv -c conda-forge',
        'numpy': 'numpy -c conda-forge',
        'matplotlib': 'matplotlib -c conda-forge',
        'PIL': 'pillow -c conda-forge',
        'psutil': 'psutil -c conda-forge'
    }
    
    for pkg in missing_packages:
        if pkg in conda_packages:
            print(f"    conda install {conda_packages[pkg]}")
    
    print("\n  或使用pip安装:")
    pip_packages = {
        'torch': 'torch torchvision',
        'cv2': 'opencv-python',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib', 
        'PIL': 'pillow',
        'psutil': 'psutil'
    }
    
    for pkg in missing_packages:
        if pkg in pip_packages:
            print(f"    pip install {pip_packages[pkg]}")


def main():
    """主函数"""
    print("🔍 交通监控系统环境检查")
    print("=" * 50)
    
    # 检查conda环境
    check_conda_env()
    
    # 检查Python版本
    check_python_version()
    
    # 检查依赖包
    installed, missing = check_packages()
    
    # 检查项目文件
    missing_core, missing_examples, missing_docs = check_project_files()
    
    # 检查YOLOv5集成
    check_yolov5_integration()
    
    # 测试核心功能
    test_core_functionality()
    
    # 生成安装建议
    if missing:
        generate_installation_commands(missing)
    
    # 总结报告
    print(f"\n📊 检查总结:")
    print(f"  ✅ 已安装包: {len(installed)}")
    print(f"  ❌ 缺失包: {len(missing)}")
    print(f"  ❌ 缺失核心文件: {len(missing_core)}")
    
    if not missing and not missing_core:
        print(f"\n🎉 环境检查完成！系统已准备就绪。")
        print(f"💡 运行命令: python simple_example.py")
    else:
        print(f"\n⚠️  请根据上述建议安装缺失的依赖。")
    
    return len(missing) == 0 and len(missing_core) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
