"""
交通监控系统主应用类
整合所有功能模块，提供统一的接口
"""
import cv2
import time
import sys
from typing import Optional, Dict, Any
import logging

from config import Config, default_config
from detector import TrafficDetector
from data_manager import DataManager
from visualization import Visualizer
from accident_detector import AccidentDetector
from traffic_utils import setup_logging


class TrafficMonitor:
    """交通监控主类，整合所有功能模块"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化交通监控系统
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or default_config
        self.logger = setup_logging(self.config.system.log_level)
        
        # 验证配置
        if not self.config.validate():
            self.logger.error("Configuration validation failed")
            sys.exit(1)
        
        # 初始化各个模块
        self.detector = None
        self.data_manager = None
        self.visualizer = None
        self.accident_detector = None
        self.video_capture = None
        
        # 运行状态
        self.is_running = False
        self.frame_count = 0
        self.start_time = None
        
        self._initialize_modules()
    
    def _initialize_modules(self):
        """初始化所有模块"""
        try:
            self.logger.info("Initializing traffic monitoring system...")
            
            # 初始化检测器
            self.logger.info("Loading detection model...")
            self.detector = TrafficDetector(self.config)
            
            # 初始化数据管理器
            self.data_manager = DataManager(self.config)
            
            # 初始化可视化器
            self.visualizer = Visualizer(self.config)
            
            # 初始化事故检测器
            self.accident_detector = AccidentDetector(self.config)
            
            self.logger.info("All modules initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize modules: {e}")
            raise
    
    def _setup_video_capture(self) -> bool:
        """设置视频捕获"""
        try:
            self.video_capture = cv2.VideoCapture(self.config.video.video_path)
            
            if not self.video_capture.isOpened():
                self.logger.error(f"Failed to open video: {self.config.video.video_path}")
                return False
            
            # 获取视频信息
            fps = self.video_capture.get(cv2.CAP_PROP_FPS)
            frame_count = int(self.video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(self.video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            self.logger.info(f"Video info - FPS: {fps}, Frames: {frame_count}, "
                           f"Resolution: {width}x{height}")
            
            # 设置显示窗口
            self.visualizer.setup_windows(width, height)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup video capture: {e}")
            return False
    
    def run(self) -> bool:
        """
        运行交通监控系统
        
        Returns:
            是否成功运行
        """
        if not self._setup_video_capture():
            return False
        
        self.is_running = True
        self.start_time = time.time()
        self.frame_count = 0
        
        self.logger.info("Starting traffic monitoring...")
        
        try:
            while self.is_running:
                success = self._process_frame()
                if not success:
                    break
                
                # 检查退出条件
                if self._should_exit():
                    break
            
            self.logger.info("Traffic monitoring stopped")
            return True
            
        except KeyboardInterrupt:
            self.logger.info("Interrupted by user")
            return True
        except Exception as e:
            self.logger.error(f"Error during processing: {e}")
            return False
        finally:
            self._cleanup()
    
    def _process_frame(self) -> bool:
        """
        处理单帧
        
        Returns:
            是否成功处理
        """
        frame_start_time = time.time()
        
        # 读取帧
        ret, frame = self.video_capture.read()
        if not ret:
            self.logger.info("End of video or failed to read frame")
            return False
        
        # 跳帧处理
        if self.frame_count % self.config.video.frame_skip != 0:
            self.frame_count += 1
            return True
        
        try:
            # 目标检测
            vehicles, persons = self.detector.detect(frame)
            
            # 事故检测
            accident_info = self.accident_detector.update(vehicles, persons, self.frame_count)
            
            # 数据管理
            processing_time = time.time() - frame_start_time
            frame_data = self.data_manager.add_frame_data(
                self.frame_count, vehicles, persons, processing_time
            )
            
            # 获取统计信息
            statistics = self.data_manager.get_current_statistics()
            
            # 可视化
            self._visualize_results(frame, frame_data, vehicles, persons, 
                                  accident_info, statistics)
            
            # 日志输出
            if self.frame_count % 30 == 0:  # 每30帧输出一次
                self._log_progress(frame_data, statistics, accident_info)
            
            self.frame_count += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing frame {self.frame_count}: {e}")
            return False
    
    def _visualize_results(self, 
                          frame, 
                          frame_data, 
                          vehicles, 
                          persons, 
                          accident_info, 
                          statistics):
        """可视化结果"""
        # 绘制检测结果
        detection_frame = self.visualizer.draw_detections(
            frame, vehicles, persons, 
            accident_info.get('accident_bboxes', [])
        )
        
        # 绘制统计信息
        detection_frame = self.visualizer.draw_statistics(
            detection_frame, frame_data, statistics, accident_info
        )
        
        # 绘制密度图
        vehicle_densities, person_densities = self.data_manager.get_density_history()
        plot_frame = self.visualizer.draw_density_plot(vehicle_densities, person_densities)
        
        # 显示
        self.visualizer.show_frames(detection_frame, plot_frame)
    
    def _log_progress(self, frame_data, statistics, accident_info):
        """记录进度日志"""
        runtime = time.time() - self.start_time
        fps = statistics.get('processing_fps', 0)
        
        log_msg = (f"Frame {frame_data.frame_id}: "
                  f"V={frame_data.vehicle_count}, P={frame_data.person_count}, "
                  f"FPS={fps:.1f}, Runtime={runtime:.1f}s")
        
        if accident_info.get('detected', False):
            log_msg += f", ACCIDENT: {accident_info.get('type', 'Unknown')}"
        
        self.logger.info(log_msg)
    
    def _should_exit(self) -> bool:
        """检查是否应该退出"""
        # 检查按键
        key = self.visualizer.wait_key(1)
        if key == ord('q'):
            self.logger.info("Exit requested by user")
            return True
        
        # 检查最大帧数限制
        if (self.config.video.max_frames > 0 and 
            self.frame_count >= self.config.video.max_frames):
            self.logger.info("Reached maximum frame limit")
            return True
        
        return False
    
    def _cleanup(self):
        """清理资源"""
        self.is_running = False
        
        if self.video_capture:
            self.video_capture.release()
        
        if self.visualizer:
            self.visualizer.cleanup()
        
        self.logger.info("Resources cleaned up")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        info = {
            'config': {
                'model_path': self.config.model.model_path,
                'video_path': self.config.video.video_path,
                'input_size': self.config.model.input_size,
                'device': self.detector.device if self.detector else None
            },
            'runtime': {
                'is_running': self.is_running,
                'frame_count': self.frame_count,
                'runtime_seconds': time.time() - self.start_time if self.start_time else 0
            }
        }
        
        if self.data_manager:
            info['statistics'] = self.data_manager.get_current_statistics()
        
        if self.accident_detector:
            info['tracking'] = self.accident_detector.get_tracking_info()
        
        if self.detector:
            info['model'] = self.detector.get_model_info()
        
        return info
    
    def stop(self):
        """停止监控系统"""
        self.is_running = False
        self.logger.info("Stop requested")


def main():
    """主函数"""
    # 创建配置（可以从命令行参数或配置文件加载）
    config = default_config
    
    # 创建并运行交通监控系统
    monitor = TrafficMonitor(config)
    success = monitor.run()
    
    if success:
        print("Traffic monitoring completed successfully")
    else:
        print("Traffic monitoring failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
