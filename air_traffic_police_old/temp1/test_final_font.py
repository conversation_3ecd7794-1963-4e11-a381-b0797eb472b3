
import matplotlib.pyplot as plt
import numpy as np

# 导入字体配置
exec(open('font_config.py').read())

# 创建测试图表
fig, ax = plt.subplots(figsize=(8, 6))
x = range(10)
y1 = [2, 3, 5, 4, 6, 7, 5, 8, 6, 4]
y2 = [1, 2, 1, 3, 2, 3, 4, 2, 3, 2]

ax.plot(x, y1, 'r-', label='车辆密度', linewidth=2)
ax.plot(x, y2, 'b-', label='行人密度', linewidth=2)

ax.set_xlabel('时间')
ax.set_ylabel('密度 (个/m²)')
ax.set_title('交通密度监控图表')
ax.legend()
ax.grid(True, alpha=0.3)

plt.savefig('final_chinese_test.png', dpi=150, bbox_inches='tight')
plt.show()

print("✅ 中文字体测试完成！")
