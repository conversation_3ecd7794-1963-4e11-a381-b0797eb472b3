# 🐍 Conda环境设置指南

## 📋 环境信息
- **Conda环境**: `yolov5`
- **激活命令**: `conda activate yolov5`
- **项目路径**: `/home/<USER>/workspace/MyNeuralNetwork-master/air_traffic_police`

## 🔧 依赖安装

### 1. 激活conda环境
```bash
conda activate yolov5
```

### 2. 检查当前已安装的包
```bash
conda list | grep -E "(torch|opencv|numpy)"
```

### 3. 安装缺失的依赖

#### 方法一：使用conda安装（推荐）
```bash
# 安装PyTorch相关包
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# 安装OpenCV
conda install opencv -c conda-forge

# 安装其他依赖
conda install numpy matplotlib pillow -c conda-forge
```

#### 方法二：使用pip安装（在conda环境中）
```bash
# 确保在yolov5环境中
conda activate yolov5

# 安装依赖
pip install torchvision opencv-python matplotlib pillow

# 可选：性能监控依赖
pip install psutil
```

### 4. 验证安装
```bash
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import torchvision; print(f'TorchVision: {torchvision.__version__}')"
python -c "import cv2; print(f'OpenCV: {cv2.__version__}')"
python -c "import numpy; print(f'NumPy: {numpy.__version__}')"
```

## 🚀 运行重构后的系统

### 1. 测试核心架构（无需额外依赖）
```bash
conda activate yolov5
cd /home/<USER>/workspace/MyNeuralNetwork-master/air_traffic_police
python simple_example.py
```

### 2. 测试完整模块（需要依赖）
```bash
conda activate yolov5
python test_refactored_code.py
```

### 3. 运行示例程序
```bash
conda activate yolov5
python example_usage.py
```

### 4. 运行主程序（需要视频文件）
```bash
conda activate yolov5
# 基础版本
python traffic_monitor.py

# 性能优化版本
python optimized_traffic_monitor.py
```

## 📊 环境状态检查

### 创建环境检查脚本
```python
# 保存为 check_environment.py
import sys
import subprocess

def check_conda_env():
    """检查conda环境"""
    try:
        result = subprocess.run(['conda', 'info', '--envs'], 
                              capture_output=True, text=True)
        if 'yolov5' in result.stdout:
            print("✅ conda环境 'yolov5' 存在")
        else:
            print("❌ conda环境 'yolov5' 不存在")
    except:
        print("❌ conda命令不可用")

def check_packages():
    """检查必要的包"""
    packages = {
        'torch': 'PyTorch',
        'torchvision': 'TorchVision', 
        'cv2': 'OpenCV',
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib'
    }
    
    for pkg, name in packages.items():
        try:
            __import__(pkg)
            print(f"✅ {name} 已安装")
        except ImportError:
            print(f"❌ {name} 未安装")

def check_project_files():
    """检查项目文件"""
    import os
    files = [
        'config.py',
        'traffic_utils.py', 
        'detector.py',
        'data_manager.py',
        'visualization.py',
        'accident_detector.py',
        'traffic_monitor.py',
        'simple_example.py'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 缺失")

if __name__ == "__main__":
    print("🔍 环境检查报告")
    print("=" * 40)
    
    print("\n📦 Conda环境:")
    check_conda_env()
    
    print(f"\n🐍 Python版本: {sys.version}")
    
    print("\n📚 依赖包:")
    check_packages()
    
    print("\n📁 项目文件:")
    check_project_files()
```

### 运行环境检查
```bash
conda activate yolov5
python check_environment.py
```

## 🛠️ 常见问题解决

### 问题1: conda环境不存在
```bash
# 创建新的conda环境
conda create -n yolov5 python=3.8
conda activate yolov5
```

### 问题2: PyTorch安装问题
```bash
# 卸载现有版本
conda remove pytorch torchvision torchaudio

# 重新安装（根据您的CUDA版本选择）
# CPU版本
conda install pytorch torchvision torchaudio cpuonly -c pytorch

# GPU版本（CUDA 11.8）
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

### 问题3: OpenCV安装问题
```bash
# 卸载可能冲突的版本
pip uninstall opencv-python opencv-contrib-python

# 使用conda安装
conda install opencv -c conda-forge
```

### 问题4: 权限问题
```bash
# 确保有写权限
chmod +x *.py

# 或者使用sudo（如果需要）
sudo python simple_example.py
```

## 📈 性能优化建议

### 1. 针对conda环境的优化
```bash
# 设置conda通道优先级
conda config --add channels conda-forge
conda config --add channels pytorch
conda config --add channels nvidia

# 清理conda缓存
conda clean --all
```

### 2. 环境变量设置
```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export CONDA_ENV_NAME=yolov5
export PROJECT_PATH="/home/<USER>/workspace/MyNeuralNetwork-master/air_traffic_police"

# 创建快捷命令
alias activate_traffic="conda activate yolov5 && cd $PROJECT_PATH"
```

### 3. 自动激活脚本
```bash
# 创建 run_traffic.sh
#!/bin/bash
conda activate yolov5
cd /home/<USER>/workspace/MyNeuralNetwork-master/air_traffic_police
python simple_example.py
```

## 🎯 快速启动命令

```bash
# 一键启动
conda activate yolov5 && cd /home/<USER>/workspace/MyNeuralNetwork-master/air_traffic_police && python simple_example.py
```

## 📝 注意事项

1. **环境隔离**: 始终在 `yolov5` 环境中运行项目
2. **依赖管理**: 优先使用conda安装，避免版本冲突
3. **路径问题**: 确保在正确的项目目录中运行
4. **权限检查**: 确保有文件读写权限
5. **版本兼容**: 注意PyTorch和CUDA版本的兼容性

## 🚀 下一步

1. 运行环境检查脚本
2. 安装缺失的依赖
3. 测试重构后的系统
4. 根据需要调整配置

您的conda环境配置完成后，重构后的交通监控系统将完美运行！🎉
