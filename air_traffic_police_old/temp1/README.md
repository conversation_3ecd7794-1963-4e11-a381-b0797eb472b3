# 交通监控系统 (Traffic Monitoring System)

一个基于YOLOv5的智能交通监控系统，具有目标检测、密度分析、事故检测等功能。

## 🚀 主要特性

- **目标检测**: 基于YOLOv5的车辆和行人检测
- **密度分析**: 实时计算交通密度并可视化
- **事故检测**: 智能检测碰撞和异常情况
- **性能优化**: 批处理、内存管理、帧跳过等优化
- **模块化设计**: 清晰的代码结构，易于扩展和维护

## 📁 项目结构

```
├── config.py                    # 配置管理模块
├── utils.py                     # 工具函数模块
├── detector.py                  # 核心检测类
├── data_manager.py             # 数据管理类
├── visualization.py            # 可视化模块
├── accident_detector.py        # 事故检测模块
├── traffic_monitor.py          # 主应用类
├── optimized_traffic_monitor.py # 优化版主应用
├── example_usage.py            # 使用示例
├── README.md                   # 项目文档
└── yolov5/                     # YOLOv5模型目录
```

## 🛠️ 安装依赖

```bash
# 安装基础依赖
pip install torch torchvision opencv-python numpy

# 安装YOLOv5依赖
pip install -r yolov5/requirements.txt

# 可选：安装性能监控依赖
pip install psutil
```

## 🎯 快速开始

### 基础使用

```python
from traffic_monitor import TrafficMonitor

# 使用默认配置
monitor = TrafficMonitor()
monitor.run()
```

### 自定义配置

```python
from config import Config
from traffic_monitor import TrafficMonitor

# 创建自定义配置
config = Config()
config.model.conf_threshold = 0.3
config.video.frame_skip = 2
config.video.video_path = "your_video.mp4"

# 运行监控
monitor = TrafficMonitor(config)
monitor.run()
```

### 优化版使用

```python
from optimized_traffic_monitor import OptimizedTrafficMonitor

# 使用优化版（更好的性能）
monitor = OptimizedTrafficMonitor()
monitor.run()
```

## ⚙️ 配置选项

### 模型配置 (ModelConfig)
- `model_path`: 模型文件路径
- `input_size`: 输入尺寸
- `conf_threshold`: 置信度阈值
- `iou_threshold`: IoU阈值
- `device`: 计算设备 ("auto", "cpu", "cuda")

### 视频配置 (VideoConfig)
- `video_path`: 视频文件路径
- `frame_skip`: 跳帧数量
- `max_frames`: 最大处理帧数

### 检测配置 (DetectionConfig)
- `vehicle_classes`: 车辆类别列表
- `person_classes`: 行人类别列表

### 密度配置 (DensityConfig)
- `vehicle_physical_area`: 车辆物理面积
- `calibration_frames`: 校准帧数
- `default_area_m2`: 默认监控区域面积

### 事故检测配置 (AccidentConfig)
- `iou_threshold`: IoU阈值
- `speed_threshold`: 速度阈值
- `stationary_frames`: 静止帧数阈值

## 🔧 模块说明

### 1. 配置管理 (config.py)
统一管理所有配置参数，支持验证和扩展。

### 2. 工具函数 (utils.py)
包含图像预处理、坐标转换、性能监控等工具函数。

### 3. 检测器 (detector.py)
封装YOLOv5模型，提供目标检测功能。

### 4. 数据管理 (data_manager.py)
管理检测数据、密度计算和历史记录。

### 5. 可视化 (visualization.py)
处理所有绘图和显示功能。

### 6. 事故检测 (accident_detector.py)
实现智能的事故检测算法。

### 7. 主应用 (traffic_monitor.py)
整合所有模块的主应用类。

## 🚀 性能优化

### 优化策略
1. **批处理**: 同时处理多帧以提高GPU利用率
2. **帧跳过**: 跳过部分帧以提高处理速度
3. **内存管理**: 定期清理内存，使用固定大小缓冲区
4. **图像优化**: 压缩和缩放图像以减少内存使用

### 使用优化版
```python
from optimized_traffic_monitor import OptimizedTrafficMonitor

config = Config()
config.video.frame_skip = 3  # 跳帧
config.system.max_memory_frames = 30  # 减少内存使用

monitor = OptimizedTrafficMonitor(config)
monitor.run()
```

## 📊 输出信息

### 实时显示
- 检测结果可视化
- 密度曲线图
- 统计信息显示
- 事故警报

### 日志输出
```
INFO - Frame 120: V=5, P=2, FPS=15.3, Memory=245.2MB, Runtime=8.1s
INFO - Area calibrated: 856.34 m² (based on 15 vehicle samples)
WARNING - ACCIDENT: collision Detected!
```

### 性能统计
```
=== Performance Summary ===
Average FPS: 18.45
Average processing time: 54.23 ms
Max processing time: 89.12 ms
Frames processed: 1200
Average memory usage: 234.5 MB
```

## 🎮 控制操作

- **Q键**: 退出程序
- **窗口**: 可调节大小的显示窗口

## 🔍 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   确保yolov5s.pt文件在项目根目录
   ```

2. **视频文件无法打开**
   ```
   检查视频文件路径和格式
   ```

3. **CUDA内存不足**
   ```python
   config.model.device = "cpu"  # 强制使用CPU
   config.video.frame_skip = 3  # 增加跳帧
   ```

4. **处理速度慢**
   ```python
   # 使用优化版
   from optimized_traffic_monitor import OptimizedTrafficMonitor
   
   # 或调整配置
   config.model.input_size = (416, 416)  # 减小输入尺寸
   config.video.frame_skip = 2  # 跳帧处理
   ```

## 📈 扩展功能

### 添加新的检测类别
```python
config.detection.vehicle_classes.append("motorcycle")
```

### 自定义事故检测规则
继承`AccidentDetector`类并重写检测方法。

### 数据导出
```python
# 在data_manager.py中实现
data_manager.export_data("results.json")
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

- [YOLOv5](https://github.com/ultralytics/yolov5) - 目标检测模型
- [OpenCV](https://opencv.org/) - 计算机视觉库
- [PyTorch](https://pytorch.org/) - 深度学习框架
