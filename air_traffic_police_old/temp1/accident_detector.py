"""
交通监控系统事故检测模块
实现智能的事故检测算法
"""
import numpy as np
from collections import defaultdict, deque
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import time

from config import Config
from detector import Detection
from traffic_utils import compute_iou, compute_distance, get_bbox_center


@dataclass
class TrackedObject:
    """跟踪对象数据类"""
    object_id: int
    class_name: str
    positions: deque  # 历史位置
    velocities: deque  # 历史速度
    stationary_frames: int = 0
    last_seen_frame: int = 0
    is_stationary: bool = False


@dataclass
class AccidentEvent:
    """事故事件数据类"""
    event_type: str  # "collision", "stationary", "abnormal_speed"
    confidence: float
    involved_objects: List[int]  # 涉及的对象ID
    start_frame: int
    duration_frames: int
    location: Tuple[float, float]
    description: str


class AccidentDetector:
    """事故检测器类，实现智能的事故检测算法"""
    
    def __init__(self, config: Config):
        """
        初始化事故检测器
        
        Args:
            config: 配置对象
        """
        self.config = config
        
        # 跟踪相关
        self.tracked_objects = {}  # {object_id: TrackedObject}
        self.next_object_id = 1
        self.max_tracking_distance = 100  # 最大跟踪距离
        self.max_missing_frames = 30  # 最大丢失帧数
        
        # 事故检测参数
        self.iou_threshold = config.accident.iou_threshold
        self.speed_threshold = config.accident.speed_threshold
        self.stationary_threshold = config.accident.stationary_frames
        self.collision_threshold = config.accident.collision_frames
        
        # 事故历史
        self.accident_history = deque(maxlen=100)
        self.collision_candidates = defaultdict(int)  # 碰撞候选计数
        
        # 当前帧信息
        self.current_frame = 0
    
    def update(self, 
               vehicles: List[Detection], 
               persons: List[Detection], 
               frame_id: int) -> Dict:
        """
        更新事故检测状态
        
        Args:
            vehicles: 车辆检测结果
            persons: 行人检测结果
            frame_id: 当前帧ID
        
        Returns:
            事故检测结果字典
        """
        self.current_frame = frame_id
        all_detections = vehicles + persons
        
        # 更新对象跟踪
        self._update_tracking(all_detections)
        
        # 检测各种类型的事故
        collision_events = self._detect_collisions(all_detections)
        stationary_events = self._detect_stationary_objects()
        speed_events = self._detect_abnormal_speeds()
        
        # 合并所有事故事件
        all_events = collision_events + stationary_events + speed_events
        
        # 更新事故历史
        for event in all_events:
            self.accident_history.append(event)
        
        # 生成检测结果
        result = self._generate_detection_result(all_events, all_detections)
        
        return result
    
    def _update_tracking(self, detections: List[Detection]):
        """更新对象跟踪"""
        # 匹配检测结果到已跟踪对象
        matched_objects = set()
        unmatched_detections = []
        
        for detection in detections:
            best_match_id = None
            best_distance = float('inf')
            
            detection_center = get_bbox_center(detection.bbox)
            
            # 寻找最佳匹配
            for obj_id, tracked_obj in self.tracked_objects.items():
                if tracked_obj.class_name != detection.class_name:
                    continue
                
                if len(tracked_obj.positions) == 0:
                    continue
                
                last_position = tracked_obj.positions[-1]
                distance = compute_distance(detection_center, last_position)
                
                if distance < self.max_tracking_distance and distance < best_distance:
                    best_distance = distance
                    best_match_id = obj_id
            
            # 更新匹配的对象
            if best_match_id is not None:
                self._update_tracked_object(best_match_id, detection, detection_center)
                matched_objects.add(best_match_id)
            else:
                unmatched_detections.append((detection, detection_center))
        
        # 创建新的跟踪对象
        for detection, center in unmatched_detections:
            self._create_new_tracked_object(detection, center)
        
        # 清理丢失的对象
        self._cleanup_lost_objects(matched_objects)
    
    def _update_tracked_object(self, obj_id: int, detection: Detection, center: Tuple[float, float]):
        """更新已跟踪对象"""
        tracked_obj = self.tracked_objects[obj_id]
        
        # 更新位置
        tracked_obj.positions.append(center)
        tracked_obj.last_seen_frame = self.current_frame
        
        # 计算速度
        if len(tracked_obj.positions) >= 2:
            prev_pos = tracked_obj.positions[-2]
            velocity = compute_distance(center, prev_pos)
            tracked_obj.velocities.append(velocity)
            
            # 检查是否静止
            if velocity < self.speed_threshold:
                tracked_obj.stationary_frames += 1
            else:
                tracked_obj.stationary_frames = 0
            
            tracked_obj.is_stationary = tracked_obj.stationary_frames >= self.stationary_threshold
    
    def _create_new_tracked_object(self, detection: Detection, center: Tuple[float, float]):
        """创建新的跟踪对象"""
        new_obj = TrackedObject(
            object_id=self.next_object_id,
            class_name=detection.class_name,
            positions=deque(maxlen=30),
            velocities=deque(maxlen=30),
            last_seen_frame=self.current_frame
        )
        new_obj.positions.append(center)
        
        self.tracked_objects[self.next_object_id] = new_obj
        self.next_object_id += 1
    
    def _cleanup_lost_objects(self, matched_objects: set):
        """清理丢失的对象"""
        to_remove = []
        for obj_id, tracked_obj in self.tracked_objects.items():
            if obj_id not in matched_objects:
                frames_missing = self.current_frame - tracked_obj.last_seen_frame
                if frames_missing > self.max_missing_frames:
                    to_remove.append(obj_id)
        
        for obj_id in to_remove:
            del self.tracked_objects[obj_id]
    
    def _detect_collisions(self, detections: List[Detection]) -> List[AccidentEvent]:
        """检测碰撞事故"""
        collision_events = []
        
        # 检查所有检测结果对之间的IoU
        for i, det1 in enumerate(detections):
            for j, det2 in enumerate(detections[i+1:], i+1):
                iou = compute_iou(det1.bbox, det2.bbox)
                
                if iou > self.iou_threshold:
                    # 创建碰撞候选键
                    collision_key = tuple(sorted([det1.class_name, det2.class_name]))
                    self.collision_candidates[collision_key] += 1
                    
                    # 如果碰撞持续足够长时间，认为是真实碰撞
                    if self.collision_candidates[collision_key] >= self.collision_threshold:
                        center1 = get_bbox_center(det1.bbox)
                        center2 = get_bbox_center(det2.bbox)
                        collision_center = ((center1[0] + center2[0]) / 2, 
                                          (center1[1] + center2[1]) / 2)
                        
                        event = AccidentEvent(
                            event_type="collision",
                            confidence=min(det1.confidence, det2.confidence),
                            involved_objects=[],  # 简化版本，不跟踪具体对象ID
                            start_frame=self.current_frame - self.collision_threshold,
                            duration_frames=self.collision_threshold,
                            location=collision_center,
                            description=f"Collision between {det1.class_name} and {det2.class_name}"
                        )
                        collision_events.append(event)
                        
                        # 重置计数器
                        self.collision_candidates[collision_key] = 0
        
        return collision_events
    
    def _detect_stationary_objects(self) -> List[AccidentEvent]:
        """检测静止对象（可能的事故）"""
        stationary_events = []
        
        for obj_id, tracked_obj in self.tracked_objects.items():
            if (tracked_obj.is_stationary and 
                tracked_obj.class_name in self.config.detection.vehicle_classes):
                
                # 车辆长时间静止可能表示事故
                if len(tracked_obj.positions) > 0:
                    location = tracked_obj.positions[-1]
                    
                    event = AccidentEvent(
                        event_type="stationary",
                        confidence=0.7,  # 中等置信度
                        involved_objects=[obj_id],
                        start_frame=self.current_frame - tracked_obj.stationary_frames,
                        duration_frames=tracked_obj.stationary_frames,
                        location=location,
                        description=f"Stationary {tracked_obj.class_name} detected"
                    )
                    stationary_events.append(event)
        
        return stationary_events
    
    def _detect_abnormal_speeds(self) -> List[AccidentEvent]:
        """检测异常速度（预留功能）"""
        # TODO: 实现异常速度检测
        # 可以检测突然停止、异常加速等情况
        return []
    
    def _generate_detection_result(self, 
                                 events: List[AccidentEvent], 
                                 detections: List[Detection]) -> Dict:
        """生成检测结果"""
        has_accident = len(events) > 0
        
        # 获取涉及事故的边界框
        accident_bboxes = []
        accident_types = []
        
        if has_accident:
            # 对于碰撞事故，找到相关的检测框
            for event in events:
                if event.event_type == "collision":
                    # 找到碰撞位置附近的检测框
                    for detection in detections:
                        det_center = get_bbox_center(detection.bbox)
                        distance = compute_distance(det_center, event.location)
                        if distance < 50:  # 50像素范围内
                            accident_bboxes.append(detection.bbox)
                
                elif event.event_type == "stationary":
                    # 找到静止对象的检测框
                    for obj_id in event.involved_objects:
                        if obj_id in self.tracked_objects:
                            tracked_obj = self.tracked_objects[obj_id]
                            # 找到最接近的检测框
                            if len(tracked_obj.positions) > 0:
                                obj_pos = tracked_obj.positions[-1]
                                for detection in detections:
                                    det_center = get_bbox_center(detection.bbox)
                                    distance = compute_distance(det_center, obj_pos)
                                    if distance < 30:
                                        accident_bboxes.append(detection.bbox)
                
                accident_types.append(event.event_type)
        
        # 计算总体置信度
        confidence = max([event.confidence for event in events]) if events else 0.0
        
        return {
            'detected': has_accident,
            'confidence': confidence,
            'types': list(set(accident_types)),
            'type': accident_types[0] if accident_types else None,
            'events': events,
            'accident_bboxes': accident_bboxes,
            'num_tracked_objects': len(self.tracked_objects),
            'description': events[0].description if events else ""
        }
    
    def get_tracking_info(self) -> Dict:
        """获取跟踪信息"""
        return {
            'num_tracked_objects': len(self.tracked_objects),
            'tracked_vehicles': sum(1 for obj in self.tracked_objects.values() 
                                  if obj.class_name in self.config.detection.vehicle_classes),
            'tracked_persons': sum(1 for obj in self.tracked_objects.values() 
                                 if obj.class_name in self.config.detection.person_classes),
            'stationary_objects': sum(1 for obj in self.tracked_objects.values() 
                                    if obj.is_stationary),
            'total_accidents_detected': len(self.accident_history)
        }
    
    def reset(self):
        """重置检测器状态"""
        self.tracked_objects.clear()
        self.accident_history.clear()
        self.collision_candidates.clear()
        self.next_object_id = 1
        self.current_frame = 0
