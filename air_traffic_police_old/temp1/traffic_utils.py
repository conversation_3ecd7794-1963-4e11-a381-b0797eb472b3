"""
交通监控系统工具函数模块
包含所有通用的工具函数，避免代码重复
"""
import cv2
import torch
import numpy as np
import time
from collections import deque
from typing import Tuple, List, Union, Dict
import logging


def setup_logging(level: str = "INFO") -> logging.Logger:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def letterbox(im: np.ndarray, 
              new_shape: Tuple[int, int] = (640, 640), 
              color: Tuple[int, int, int] = (114, 114, 114)) -> Tuple[np.ndarray, float, Tuple[float, float]]:
    """
    Letterbox resize（自动等比例缩放+填充）
    
    Args:
        im: 输入图像
        new_shape: 目标尺寸 (height, width)
        color: 填充颜色 (B, G, R)
    
    Returns:
        resized_img: 调整后的图像
        ratio: 缩放比例
        pad: 填充大小 (dw, dh)
    """
    shape = im.shape[:2]  # current shape [height, width]
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    
    # 计算新的未填充尺寸
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]
    dw /= 2  # divide padding into 2 sides
    dh /= 2

    # 调整图像大小
    if shape[::-1] != new_unpad:  # resize
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    
    # 添加边框
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
    
    return im, r, (dw, dh)


def scale_coords_back(xyxy: Union[torch.Tensor, List], 
                     gain: float, 
                     pad: Tuple[float, float]) -> Tuple[int, int, int, int]:
    """
    坐标从缩放图映射回原图
    
    Args:
        xyxy: 边界框坐标 [x1, y1, x2, y2]
        gain: 缩放比例
        pad: 填充大小 (dw, dh)
    
    Returns:
        原图坐标 (x1, y1, x2, y2)
    """
    x1, y1, x2, y2 = xyxy
    x1 = (x1 - pad[0]) / gain
    y1 = (y1 - pad[1]) / gain
    x2 = (x2 - pad[0]) / gain
    y2 = (y2 - pad[1]) / gain
    return tuple(map(int, (x1, y1, x2, y2)))


def compute_iou(box1: Tuple[int, int, int, int], 
                box2: Tuple[int, int, int, int]) -> float:
    """
    计算两个边界框的IoU
    
    Args:
        box1: 边界框1 (x1, y1, x2, y2)
        box2: 边界框2 (x1, y1, x2, y2)
    
    Returns:
        IoU值
    """
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # 计算交集
    xi1 = max(x1_1, x1_2)
    yi1 = max(y1_1, y1_2)
    xi2 = min(x2_1, x2_2)
    yi2 = min(y2_1, y2_2)
    
    inter_area = max(0, xi2 - xi1) * max(0, yi2 - yi1)
    
    # 计算并集
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - inter_area
    
    return inter_area / union_area if union_area > 0 else 0


def compute_distance(point1: Tuple[float, float], 
                    point2: Tuple[float, float]) -> float:
    """
    计算两点之间的欧几里得距离
    
    Args:
        point1: 点1 (x, y)
        point2: 点2 (x, y)
    
    Returns:
        距离
    """
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)


def get_bbox_center(bbox: Tuple[int, int, int, int]) -> Tuple[float, float]:
    """
    获取边界框中心点
    
    Args:
        bbox: 边界框 (x1, y1, x2, y2)
    
    Returns:
        中心点 (cx, cy)
    """
    x1, y1, x2, y2 = bbox
    return ((x1 + x2) / 2, (y1 + y2) / 2)


def get_bbox_area(bbox: Tuple[int, int, int, int]) -> float:
    """
    计算边界框面积
    
    Args:
        bbox: 边界框 (x1, y1, x2, y2)
    
    Returns:
        面积
    """
    x1, y1, x2, y2 = bbox
    return (x2 - x1) * (y2 - y1)


def setup_device(device_preference: str = "auto") -> torch.device:
    """
    设置计算设备
    
    Args:
        device_preference: 设备偏好 ("auto", "cpu", "cuda")
    
    Returns:
        torch设备对象
    """
    if device_preference == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(device_preference)
    
    return device


def preprocess_frame(frame: np.ndarray, 
                    input_size: Tuple[int, int] = (640, 640),
                    device: torch.device = None) -> Tuple[torch.Tensor, float, Tuple[float, float]]:
    """
    预处理视频帧用于模型推理
    
    Args:
        frame: 输入帧
        input_size: 模型输入尺寸
        device: 计算设备
    
    Returns:
        processed_tensor: 预处理后的张量
        gain: 缩放比例
        pad: 填充大小
    """
    if device is None:
        device = torch.device("cpu")
    
    # Letterbox resize
    img, gain, pad = letterbox(frame, new_shape=input_size)
    
    # 颜色空间转换和归一化
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = torch.from_numpy(img).permute(2, 0, 1).float().unsqueeze(0) / 255.0
    img = img.to(device)
    
    return img, gain, pad


def filter_detections_by_class(detections: List, 
                              model_names: List[str], 
                              target_classes: List[str]) -> List:
    """
    根据类别过滤检测结果
    
    Args:
        detections: 检测结果列表
        model_names: 模型类别名称列表
        target_classes: 目标类别列表
    
    Returns:
        过滤后的检测结果
    """
    filtered = []
    for det in detections:
        if det is not None and len(det):
            for *xyxy, conf, cls in det:
                if model_names[int(cls)] in target_classes:
                    filtered.append((*xyxy, conf, cls))
    return filtered


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 默认值
    
    Returns:
        除法结果或默认值
    """
    return numerator / denominator if denominator != 0 else default


def clamp(value: float, min_val: float, max_val: float) -> float:
    """
    将值限制在指定范围内

    Args:
        value: 输入值
        min_val: 最小值
        max_val: 最大值

    Returns:
        限制后的值
    """
    return max(min_val, min(value, max_val))


class FrameBuffer:
    """帧缓冲区类，用于批处理优化"""

    def __init__(self, buffer_size: int = 4):
        """
        初始化帧缓冲区

        Args:
            buffer_size: 缓冲区大小
        """
        self.buffer_size = buffer_size
        self.frames = []
        self.frame_info = []

    def add_frame(self, frame: np.ndarray, frame_id: int) -> bool:
        """
        添加帧到缓冲区

        Args:
            frame: 图像帧
            frame_id: 帧ID

        Returns:
            缓冲区是否已满
        """
        self.frames.append(frame.copy())
        self.frame_info.append(frame_id)

        return len(self.frames) >= self.buffer_size

    def get_batch(self) -> Tuple[List[np.ndarray], List[int]]:
        """
        获取批次数据

        Returns:
            (frames, frame_ids)
        """
        batch_frames = self.frames.copy()
        batch_info = self.frame_info.copy()
        self.clear()
        return batch_frames, batch_info

    def clear(self):
        """清空缓冲区"""
        self.frames.clear()
        self.frame_info.clear()

    def is_empty(self) -> bool:
        """检查缓冲区是否为空"""
        return len(self.frames) == 0

    def size(self) -> int:
        """获取当前缓冲区大小"""
        return len(self.frames)


class PerformanceMonitor:
    """性能监控类"""

    def __init__(self, window_size: int = 100):
        """
        初始化性能监控器

        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.processing_times = deque(maxlen=window_size)
        self.memory_usage = deque(maxlen=window_size)
        self.frame_counts = deque(maxlen=window_size)
        self.timestamps = deque(maxlen=window_size)

    def record_frame(self, processing_time: float, memory_mb: float = 0.0):
        """
        记录帧处理信息

        Args:
            processing_time: 处理时间（秒）
            memory_mb: 内存使用量（MB）
        """
        self.processing_times.append(processing_time)
        self.memory_usage.append(memory_mb)
        self.timestamps.append(time.time())

    def get_stats(self) -> Dict:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        if not self.processing_times:
            return {}

        avg_time = np.mean(self.processing_times)
        max_time = np.max(self.processing_times)
        min_time = np.min(self.processing_times)
        fps = safe_divide(1.0, avg_time)

        stats = {
            'avg_processing_time_ms': avg_time * 1000,
            'max_processing_time_ms': max_time * 1000,
            'min_processing_time_ms': min_time * 1000,
            'avg_fps': fps,
            'frames_processed': len(self.processing_times)
        }

        if self.memory_usage and any(m > 0 for m in self.memory_usage):
            stats.update({
                'avg_memory_mb': np.mean([m for m in self.memory_usage if m > 0]),
                'max_memory_mb': np.max(self.memory_usage),
                'current_memory_mb': self.memory_usage[-1] if self.memory_usage else 0
            })

        return stats


def get_memory_usage() -> float:
    """
    获取当前内存使用量（MB）

    Returns:
        内存使用量（MB）
    """
    try:
        import psutil
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except ImportError:
        return 0.0


def optimize_frame_for_processing(frame: np.ndarray,
                                target_size: Tuple[int, int] = None,
                                quality: float = 0.8) -> np.ndarray:
    """
    优化帧用于处理（降低分辨率、压缩等）

    Args:
        frame: 输入帧
        target_size: 目标尺寸 (width, height)
        quality: 质量因子 (0.1-1.0)

    Returns:
        优化后的帧
    """
    if target_size:
        frame = cv2.resize(frame, target_size, interpolation=cv2.INTER_LINEAR)

    if quality < 1.0:
        # 使用JPEG压缩来减少内存使用
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), int(quality * 100)]
        _, encoded = cv2.imencode('.jpg', frame, encode_param)
        frame = cv2.imdecode(encoded, cv2.IMREAD_COLOR)

    return frame


def setup_chinese_font():
    """
    设置中文字体支持
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm

        # 清除matplotlib字体缓存
        try:
            fm._rebuild()
        except:
            pass

        # 设置matplotlib中文字体 - 使用系统中实际存在的字体
        chinese_fonts = [
            'SimHei',           # 黑体
            'Microsoft YaHei',  # 微软雅黑
            'WenQuanYi Zen Hei', # 文泉驿正黑
            'Noto Sans CJK SC', # Noto Sans 中文
            'AR PL UMing CN',   # 文鼎PL明体
            'DejaVu Sans'       # 后备字体
        ]

        plt.rcParams['font.sans-serif'] = chinese_fonts
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        # 强制刷新字体缓存
        plt.rcParams['font.family'] = 'sans-serif'

        return True
    except ImportError:
        return False


def put_chinese_text(img: np.ndarray,
                    text: str,
                    position: Tuple[int, int],
                    font_scale: float = 0.7,
                    color: Tuple[int, int, int] = (0, 255, 255),
                    thickness: int = 2) -> np.ndarray:
    """
    在图像上绘制中文文本（使用PIL作为后备方案）

    Args:
        img: 输入图像
        text: 要绘制的文本
        position: 文本位置 (x, y)
        font_scale: 字体大小
        color: 文本颜色 (B, G, R)
        thickness: 文本粗细

    Returns:
        绘制文本后的图像
    """
    try:
        # 尝试使用PIL绘制中文
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np

        # 转换为PIL图像
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)

        # 尝试加载中文字体 - 使用系统中实际存在的字体
        font_paths = [
            "/usr/share/fonts/program_font/simhei.ttf",  # 黑体
            "/usr/share/fonts/MyFonts/simhei.ttf",       # 黑体备份
            "/usr/share/fonts/program_font/msyh.ttf",    # 微软雅黑
            "/usr/share/fonts/MyFonts/msyh.ttf",         # 微软雅黑备份
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",  # 文泉驿正黑
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",  # Noto Sans CJK
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # 后备字体
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"  # 后备字体
        ]

        font = None
        font_size = int(font_scale * 20)

        for font_path in font_paths:
            try:
                import os
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                    break
            except:
                continue

        if font is None:
            font = ImageFont.load_default()

        # 绘制文本
        draw.text(position, text, font=font, fill=(color[2], color[1], color[0]))

        # 转换回OpenCV格式
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        return img_cv

    except ImportError:
        # 如果PIL不可用，使用OpenCV的基础文本绘制
        cv2.putText(img, text, position, cv2.FONT_HERSHEY_SIMPLEX,
                   font_scale, color, thickness)
        return img
