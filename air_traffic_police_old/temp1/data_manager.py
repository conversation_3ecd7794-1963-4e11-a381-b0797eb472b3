"""
交通监控系统数据管理模块
管理密度数据、历史记录和统计信息
"""
import numpy as np
from collections import deque
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, field
import time

from config import Config
from detector import Detection
from traffic_utils import safe_divide


@dataclass
class FrameData:
    """单帧数据类"""
    frame_id: int
    timestamp: float
    vehicle_count: int
    person_count: int
    vehicle_density: float
    person_density: float
    vehicles: List[Detection] = field(default_factory=list)
    persons: List[Detection] = field(default_factory=list)


@dataclass
class AreaCalibration:
    """区域校准数据类"""
    vehicle_pixel_areas: List[float] = field(default_factory=list)
    vehicle_widths: List[Tuple[float, float]] = field(default_factory=list)  # (width, y_center)
    is_calibrated: bool = False
    estimated_area_m2: Optional[float] = None


class DataManager:
    """数据管理器类，管理所有历史数据和统计信息"""
    
    def __init__(self, config: Config):
        """
        初始化数据管理器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.max_frames = config.system.max_memory_frames
        
        # 使用deque实现高效的固定大小缓冲区
        self.frame_data_history = deque(maxlen=self.max_frames)
        self.vehicle_densities = deque(maxlen=config.visualization.max_plot_points)
        self.person_densities = deque(maxlen=config.visualization.max_plot_points)
        self.vehicle_counts = deque(maxlen=config.visualization.max_plot_points)
        self.person_counts = deque(maxlen=config.visualization.max_plot_points)
        
        # 区域校准
        self.area_calibration = AreaCalibration()
        self.current_area_m2 = config.density.default_area_m2
        
        # 统计信息
        self.total_frames_processed = 0
        self.start_time = time.time()
        
        # 性能统计
        self.processing_times = deque(maxlen=100)
    
    def add_frame_data(self, 
                      frame_id: int,
                      vehicles: List[Detection], 
                      persons: List[Detection],
                      processing_time: float = 0.0) -> FrameData:
        """
        添加新的帧数据
        
        Args:
            frame_id: 帧ID
            vehicles: 车辆检测结果
            persons: 行人检测结果
            processing_time: 处理时间
        
        Returns:
            创建的帧数据对象
        """
        timestamp = time.time()
        vehicle_count = len(vehicles)
        person_count = len(persons)
        
        # 计算密度
        vehicle_density = safe_divide(vehicle_count, self.current_area_m2)
        person_density = safe_divide(person_count, self.current_area_m2)
        
        # 创建帧数据
        frame_data = FrameData(
            frame_id=frame_id,
            timestamp=timestamp,
            vehicle_count=vehicle_count,
            person_count=person_count,
            vehicle_density=vehicle_density,
            person_density=person_density,
            vehicles=vehicles,
            persons=persons
        )
        
        # 添加到历史记录
        self.frame_data_history.append(frame_data)
        self.vehicle_densities.append(vehicle_density)
        self.person_densities.append(person_density)
        self.vehicle_counts.append(vehicle_count)
        self.person_counts.append(person_count)
        
        # 更新统计信息
        self.total_frames_processed += 1
        if processing_time > 0:
            self.processing_times.append(processing_time)
        
        # 区域校准
        if not self.area_calibration.is_calibrated:
            self._update_area_calibration(vehicles)
        
        return frame_data
    
    def _update_area_calibration(self, vehicles: List[Detection]):
        """
        更新区域校准数据
        
        Args:
            vehicles: 车辆检测结果
        """
        if self.total_frames_processed > self.config.density.calibration_frames:
            return
        
        # 收集车辆像素面积和宽度信息
        for vehicle in vehicles:
            x1, y1, x2, y2 = vehicle.bbox
            pixel_area = vehicle.area
            width_pixels = x2 - x1
            y_center = (y1 + y2) / 2
            
            if pixel_area > 100:  # 过滤太小的检测框
                self.area_calibration.vehicle_pixel_areas.append(pixel_area)
                self.area_calibration.vehicle_widths.append((width_pixels, y_center))
        
        # 在校准帧数达到后进行区域估算
        if (self.total_frames_processed == self.config.density.calibration_frames and 
            self.area_calibration.vehicle_pixel_areas):
            self._calibrate_area()
    
    def _calibrate_area(self):
        """执行区域校准"""
        try:
            # 方法1：基于车辆平均面积估算
            if self.area_calibration.vehicle_pixel_areas:
                avg_vehicle_pixel_area = np.mean(self.area_calibration.vehicle_pixel_areas)
                pixel_to_m2 = self.config.density.vehicle_physical_area / avg_vehicle_pixel_area
                
                # 假设视频分辨率代表监控区域
                # 这里需要根据实际情况调整
                estimated_area = 1920 * 1080 * pixel_to_m2  # 假设1080p视频
                
                self.current_area_m2 = min(estimated_area, 10000)  # 限制最大面积
                self.area_calibration.estimated_area_m2 = self.current_area_m2
                self.area_calibration.is_calibrated = True
                
                print(f"Area calibrated: {self.current_area_m2:.2f} m² "
                      f"(based on {len(self.area_calibration.vehicle_pixel_areas)} vehicle samples)")
            
            # 方法2：基于车辆宽度的梯形估算（如果有足够数据）
            if len(self.area_calibration.vehicle_widths) >= 10:
                self._calibrate_area_by_perspective()
                
        except Exception as e:
            print(f"Area calibration failed: {e}")
            self.area_calibration.is_calibrated = True  # 使用默认值
    
    def _calibrate_area_by_perspective(self):
        """基于透视效果的区域校准"""
        try:
            # 按Y坐标排序
            widths_sorted = sorted(self.area_calibration.vehicle_widths, key=lambda x: x[1])
            
            # 取上方和下方的车辆宽度
            top_widths = [w for w, y in widths_sorted[:5] if w > 10]
            bottom_widths = [w for w, y in widths_sorted[-5:] if w > 10]
            
            if top_widths and bottom_widths:
                top_width_pixels = np.mean(top_widths)
                bottom_width_pixels = np.mean(bottom_widths)
                
                # 计算像素到物理单位的转换
                pixel_to_m_top = self.config.density.vehicle_width_m / top_width_pixels
                pixel_to_m_bottom = self.config.density.vehicle_width_m / bottom_width_pixels
                
                # 梯形面积估算
                top_width_m = top_width_pixels * pixel_to_m_top
                bottom_width_m = bottom_width_pixels * pixel_to_m_bottom
                area_m2 = (top_width_m + bottom_width_m) * self.config.density.assumed_height_m / 2
                
                if 100 <= area_m2 <= 10000:  # 合理范围检查
                    self.current_area_m2 = area_m2
                    self.area_calibration.estimated_area_m2 = area_m2
                    print(f"Perspective-based area calibration: {area_m2:.2f} m²")
                
        except Exception as e:
            print(f"Perspective calibration failed: {e}")
    
    def get_current_statistics(self) -> Dict:
        """
        获取当前统计信息
        
        Returns:
            统计信息字典
        """
        if not self.frame_data_history:
            return {}
        
        latest_frame = self.frame_data_history[-1]
        
        # 计算平均值（最近N帧）
        recent_frames = list(self.frame_data_history)[-min(30, len(self.frame_data_history)):]
        avg_vehicle_count = np.mean([f.vehicle_count for f in recent_frames])
        avg_person_count = np.mean([f.person_count for f in recent_frames])
        avg_vehicle_density = np.mean([f.vehicle_density for f in recent_frames])
        avg_person_density = np.mean([f.person_density for f in recent_frames])
        
        # 处理性能统计
        avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0
        fps = safe_divide(1.0, avg_processing_time) if avg_processing_time > 0 else 0
        
        return {
            'current_frame': latest_frame.frame_id,
            'current_vehicle_count': latest_frame.vehicle_count,
            'current_person_count': latest_frame.person_count,
            'current_vehicle_density': latest_frame.vehicle_density,
            'current_person_density': latest_frame.person_density,
            'avg_vehicle_count': avg_vehicle_count,
            'avg_person_count': avg_person_count,
            'avg_vehicle_density': avg_vehicle_density,
            'avg_person_density': avg_person_density,
            'total_frames_processed': self.total_frames_processed,
            'estimated_area_m2': self.current_area_m2,
            'is_area_calibrated': self.area_calibration.is_calibrated,
            'processing_fps': fps,
            'avg_processing_time_ms': avg_processing_time * 1000,
            'runtime_seconds': time.time() - self.start_time
        }
    
    def get_density_history(self) -> Tuple[List[float], List[float]]:
        """
        获取密度历史数据
        
        Returns:
            (vehicle_densities, person_densities)
        """
        return list(self.vehicle_densities), list(self.person_densities)
    
    def get_count_history(self) -> Tuple[List[int], List[int]]:
        """
        获取计数历史数据
        
        Returns:
            (vehicle_counts, person_counts)
        """
        return list(self.vehicle_counts), list(self.person_counts)
    
    def export_data(self, filepath: str):
        """
        导出数据到文件（预留接口）
        
        Args:
            filepath: 文件路径
        """
        # TODO: 实现数据导出功能
        pass
    
    def clear_history(self):
        """清空历史数据"""
        self.frame_data_history.clear()
        self.vehicle_densities.clear()
        self.person_densities.clear()
        self.vehicle_counts.clear()
        self.person_counts.clear()
        self.processing_times.clear()
        self.total_frames_processed = 0
        self.start_time = time.time()
