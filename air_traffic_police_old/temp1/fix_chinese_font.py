#!/usr/bin/env python3
"""
中文字体修复脚本
彻底解决matplotlib和OpenCV的中文显示问题
"""
import os
import sys
import subprocess


def clear_matplotlib_cache():
    """清除matplotlib字体缓存"""
    print("🔄 清除matplotlib字体缓存...")
    
    try:
        import matplotlib
        import matplotlib.font_manager as fm
        
        # 获取缓存目录
        cache_dir = matplotlib.get_cachedir()
        print(f"  缓存目录: {cache_dir}")
        
        # 删除字体缓存文件
        cache_files = [
            'fontlist-v330.json',
            'fontlist-v320.json', 
            'fontlist-v310.json',
            'fontList.cache'
        ]
        
        for cache_file in cache_files:
            cache_path = os.path.join(cache_dir, cache_file)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                print(f"  ✅ 删除缓存文件: {cache_file}")
        
        # 重建字体缓存
        fm._rebuild()
        print("  ✅ 重建字体缓存完成")
        
        return True
    except Exception as e:
        print(f"  ❌ 清除缓存失败: {e}")
        return False


def test_font_availability():
    """测试字体可用性"""
    print("\n🔍 测试字体可用性...")
    
    try:
        import matplotlib.font_manager as fm
        
        # 获取所有可用字体
        fonts = fm.findSystemFonts()
        chinese_fonts = []
        
        # 查找中文字体
        chinese_font_names = [
            'SimHei', '黑体',
            'Microsoft YaHei', '微软雅黑', 
            'WenQuanYi Zen Hei', '文泉驿正黑',
            'Noto Sans CJK SC',
            'AR PL UMing CN'
        ]
        
        for font_path in fonts:
            try:
                font_prop = fm.FontProperties(fname=font_path)
                font_name = font_prop.get_name()
                
                for chinese_name in chinese_font_names:
                    if chinese_name in font_name:
                        chinese_fonts.append((font_name, font_path))
                        break
            except:
                continue
        
        print(f"  找到 {len(chinese_fonts)} 个中文字体:")
        for font_name, font_path in chinese_fonts[:5]:  # 只显示前5个
            print(f"    ✅ {font_name}: {font_path}")
        
        return len(chinese_fonts) > 0
        
    except Exception as e:
        print(f"  ❌ 字体检测失败: {e}")
        return False


def configure_matplotlib():
    """配置matplotlib中文字体"""
    print("\n⚙️ 配置matplotlib中文字体...")
    
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        
        # 设置中文字体
        chinese_fonts = [
            'SimHei',
            'Microsoft YaHei', 
            'WenQuanYi Zen Hei',
            'Noto Sans CJK SC',
            'AR PL UMing CN',
            'DejaVu Sans'
        ]
        
        plt.rcParams['font.sans-serif'] = chinese_fonts
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['font.family'] = 'sans-serif'
        
        print(f"  ✅ 设置字体列表: {chinese_fonts}")
        
        # 测试中文显示
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.text(0.5, 0.5, '中文测试 Chinese Test', 
                ha='center', va='center', fontsize=16)
        ax.set_title('字体测试')
        
        # 保存测试图片
        plt.savefig('matplotlib_font_test.png', dpi=100, bbox_inches='tight')
        plt.close()
        
        print("  ✅ matplotlib配置完成，已保存测试图片")
        return True
        
    except Exception as e:
        print(f"  ❌ matplotlib配置失败: {e}")
        return False


def test_opencv_chinese():
    """测试OpenCV中文显示"""
    print("\n🖼️ 测试OpenCV中文显示...")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建测试图像
        img = np.ones((300, 500, 3), dtype=np.uint8) * 255
        
        # 使用PIL绘制中文
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 尝试加载中文字体
        font_paths = [
            "/usr/share/fonts/program_font/simhei.ttf",
            "/usr/share/fonts/MyFonts/simhei.ttf",
            "/usr/share/fonts/program_font/msyh.ttf",
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc"
        ]
        
        font = None
        used_font_path = None
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 24)
                    used_font_path = font_path
                    break
                except:
                    continue
        
        if font is None:
            font = ImageFont.load_default()
            used_font_path = "默认字体"
        
        # 绘制中文文本
        test_texts = [
            "交通监控系统",
            "车辆检测: 5辆", 
            "行人检测: 3人",
            "密度: 0.025/m²",
            "⚠️ 检测到事故!"
        ]
        
        for i, text in enumerate(test_texts):
            draw.text((50, 50 + i * 40), text, font=font, fill=(0, 0, 0))
        
        # 转换回OpenCV格式并保存
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        cv2.imwrite('opencv_font_test.png', img_cv)
        
        print(f"  ✅ 使用字体: {used_font_path}")
        print("  ✅ OpenCV中文测试完成，已保存测试图片")
        return True
        
    except Exception as e:
        print(f"  ❌ OpenCV测试失败: {e}")
        return False


def create_font_config():
    """创建字体配置文件"""
    print("\n📝 创建字体配置文件...")
    
    config_content = '''
# matplotlib中文字体配置
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = [
    'SimHei',           # 黑体
    'Microsoft YaHei',  # 微软雅黑
    'WenQuanYi Zen Hei', # 文泉驿正黑
    'Noto Sans CJK SC', # Noto Sans 中文
    'AR PL UMing CN',   # 文鼎PL明体
    'DejaVu Sans'       # 后备字体
]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
plt.rcParams['font.family'] = 'sans-serif'

print("✅ 中文字体配置已加载")
'''
    
    try:
        with open('font_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("  ✅ 已创建 font_config.py")
        return True
    except Exception as e:
        print(f"  ❌ 创建配置文件失败: {e}")
        return False


def update_system_modules():
    """更新系统模块以使用正确的字体配置"""
    print("\n🔧 更新系统模块...")
    
    try:
        # 在可视化模块开头添加字体配置
        visualization_import = '''
# 强制设置中文字体
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 清除缓存并重新配置
try:
    fm._rebuild()
except:
    pass

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'sans-serif'
'''
        
        print("  ✅ 字体配置已准备就绪")
        print("  💡 请在使用前运行: import font_config")
        return True
        
    except Exception as e:
        print(f"  ❌ 更新模块失败: {e}")
        return False


def main():
    """主函数"""
    print("🔤 中文字体修复工具")
    print("=" * 50)
    
    # 执行修复步骤
    steps = [
        ("清除matplotlib缓存", clear_matplotlib_cache),
        ("测试字体可用性", test_font_availability),
        ("配置matplotlib", configure_matplotlib),
        ("测试OpenCV中文", test_opencv_chinese),
        ("创建字体配置", create_font_config),
        ("更新系统模块", update_system_modules)
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n📋 执行: {step_name}")
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            results.append((step_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("🧪 修复结果汇总:")
    
    success_count = 0
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {step_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n📊 总计: {success_count}/{len(results)} 个步骤成功")
    
    if success_count >= len(results) - 1:  # 允许1个步骤失败
        print("\n🎉 中文字体修复基本完成！")
        print("\n📋 下一步操作:")
        print("1. 重启Python解释器")
        print("2. 运行: python test_chinese_font.py")
        print("3. 检查生成的测试图片")
        
        # 生成测试脚本
        test_script = '''
import matplotlib.pyplot as plt
import numpy as np

# 导入字体配置
exec(open('font_config.py').read())

# 创建测试图表
fig, ax = plt.subplots(figsize=(8, 6))
x = range(10)
y1 = [2, 3, 5, 4, 6, 7, 5, 8, 6, 4]
y2 = [1, 2, 1, 3, 2, 3, 4, 2, 3, 2]

ax.plot(x, y1, 'r-', label='车辆密度', linewidth=2)
ax.plot(x, y2, 'b-', label='行人密度', linewidth=2)

ax.set_xlabel('时间')
ax.set_ylabel('密度 (个/m²)')
ax.set_title('交通密度监控图表')
ax.legend()
ax.grid(True, alpha=0.3)

plt.savefig('final_chinese_test.png', dpi=150, bbox_inches='tight')
plt.show()

print("✅ 中文字体测试完成！")
'''
        
        with open('test_final_font.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("4. 运行: python test_final_font.py")
        
    else:
        print("\n⚠️ 部分修复步骤失败，可能需要手动安装中文字体")
        print("\n💡 建议:")
        print("sudo apt-get install fonts-noto-cjk")
        print("sudo apt-get install fonts-wqy-microhei")
    
    return success_count >= len(results) - 1


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
