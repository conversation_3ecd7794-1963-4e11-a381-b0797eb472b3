"""
交通监控系统使用示例
展示如何使用重构后的模块化系统
"""
from config import Config
from traffic_monitor import TrafficMonitor
from optimized_traffic_monitor import OptimizedTrafficMonitor


def basic_usage_example():
    """基础使用示例"""
    print("=== 基础交通监控示例 ===")
    
    # 使用默认配置
    monitor = TrafficMonitor()
    
    # 运行监控系统
    success = monitor.run()
    
    if success:
        print("监控完成")
    else:
        print("监控失败")


def custom_config_example():
    """自定义配置示例"""
    print("=== 自定义配置示例 ===")
    
    # 创建自定义配置
    config = Config()
    
    # 修改模型配置
    config.model.conf_threshold = 0.3  # 降低置信度阈值
    config.model.device = "cpu"  # 强制使用CPU
    
    # 修改视频配置
    config.video.frame_skip = 2  # 跳帧处理
    config.video.max_frames = 1000  # 只处理前1000帧
    
    # 修改可视化配置
    config.visualization.display_scale = 0.5  # 缩小显示窗口
    
    # 修改事故检测配置
    config.accident.iou_threshold = 0.3  # 降低碰撞检测阈值
    config.accident.stationary_frames = 20  # 减少静止帧数阈值
    
    # 创建监控器
    monitor = TrafficMonitor(config)
    
    # 运行
    success = monitor.run()
    
    if success:
        print("自定义配置监控完成")
        
        # 获取系统信息
        info = monitor.get_system_info()
        print(f"处理帧数: {info['runtime']['frame_count']}")
        print(f"运行时间: {info['runtime']['runtime_seconds']:.2f}秒")


def optimized_usage_example():
    """优化版使用示例"""
    print("=== 优化版交通监控示例 ===")
    
    # 创建优化配置
    config = Config()
    config.video.frame_skip = 3  # 更激进的跳帧
    config.system.max_memory_frames = 30  # 减少内存使用
    
    # 使用优化版监控器
    monitor = OptimizedTrafficMonitor(config)
    
    # 运行
    success = monitor.run()
    
    if success:
        print("优化版监控完成")


def detector_only_example():
    """仅使用检测器示例"""
    print("=== 仅检测器使用示例 ===")
    
    import cv2
    from detector import TrafficDetector
    from config import default_config
    
    # 创建检测器
    detector = TrafficDetector(default_config)
    
    # 读取单张图片进行检测
    frame = cv2.imread("action1.mp4")  # 这里应该是图片路径
    if frame is not None:
        vehicles, persons = detector.detect(frame)
        
        print(f"检测到 {len(vehicles)} 辆车, {len(persons)} 个人")
        
        # 获取检测摘要
        summary = detector.get_detection_summary(vehicles, persons)
        print(f"检测摘要: {summary}")


def data_analysis_example():
    """数据分析示例"""
    print("=== 数据分析示例 ===")

    from data_manager import DataManager
    from detector import Detection
    from config import default_config

    # 创建数据管理器
    data_manager = DataManager(default_config)
    
    # 模拟添加一些数据
    for i in range(10):
        # 创建模拟检测结果
        vehicles = [
            Detection(
                bbox=(100, 100, 200, 200),
                confidence=0.8,
                class_id=0,
                class_name="car",
                center=(150, 150),
                area=10000
            )
        ]
        persons = []
        
        # 添加帧数据
        frame_data = data_manager.add_frame_data(i, vehicles, persons)
        print(f"帧 {i}: 车辆密度 = {frame_data.vehicle_density:.4f}/m²")
    
    # 获取统计信息
    stats = data_manager.get_current_statistics()
    print(f"平均车辆数量: {stats['avg_vehicle_count']:.2f}")
    print(f"估算区域面积: {stats['estimated_area_m2']:.2f} m²")


def visualization_example():
    """可视化示例"""
    print("=== 可视化示例 ===")
    
    import numpy as np
    from visualization import Visualizer
    from config import default_config
    
    # 创建可视化器
    visualizer = Visualizer(default_config)
    
    # 创建模拟密度数据
    vehicle_densities = [0.1 * i + 0.05 * np.sin(i * 0.1) for i in range(50)]
    person_densities = [0.05 * i + 0.02 * np.cos(i * 0.15) for i in range(50)]
    
    # 绘制密度图
    plot_img = visualizer.draw_density_plot(vehicle_densities, person_densities)
    
    # 显示图像
    import cv2
    cv2.imshow("Density Plot Example", plot_img)
    cv2.waitKey(3000)  # 显示3秒
    cv2.destroyAllWindows()


def accident_detection_example():
    """事故检测示例"""
    print("=== 事故检测示例 ===")

    from accident_detector import AccidentDetector
    from detector import Detection
    from config import default_config

    # 创建事故检测器
    accident_detector = AccidentDetector(default_config)
    
    # 模拟检测结果（两个重叠的车辆）
    vehicles = [
        Detection(
            bbox=(100, 100, 200, 200),
            confidence=0.9,
            class_id=0,
            class_name="car",
            center=(150, 150),
            area=10000
        ),
        Detection(
            bbox=(150, 150, 250, 250),  # 重叠的边界框
            confidence=0.8,
            class_id=0,
            class_name="car",
            center=(200, 200),
            area=10000
        )
    ]
    
    # 更新事故检测
    for frame_id in range(10):
        accident_info = accident_detector.update(vehicles, [], frame_id)
        
        if accident_info['detected']:
            print(f"帧 {frame_id}: 检测到事故 - {accident_info['type']}")
            print(f"置信度: {accident_info['confidence']:.2f}")
            print(f"描述: {accident_info['description']}")
            break
    
    # 获取跟踪信息
    tracking_info = accident_detector.get_tracking_info()
    print(f"跟踪对象数量: {tracking_info['num_tracked_objects']}")


def main():
    """主函数 - 运行所有示例"""
    print("交通监控系统使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        detector_only_example()
        print()
        
        data_analysis_example()
        print()
        
        visualization_example()
        print()
        
        accident_detection_example()
        print()
        
        # 注意：以下示例需要视频文件，如果没有可以注释掉
        # basic_usage_example()
        # custom_config_example()
        # optimized_usage_example()
        
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        print("请确保已安装所有依赖并且视频文件存在")


if __name__ == "__main__":
    main()
