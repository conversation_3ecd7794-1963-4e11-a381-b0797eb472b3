import torch
import cv2
import numpy as np
import sys
sys.path.append("yolov5")

from yolov5.models.experimental import attempt_load
from yolov5.utils.general import non_max_suppression

# 🚀 Letterbox resize（自动等比例缩放+填充）
def letterbox(im, new_shape=(640, 640), color=(114, 114, 114)):
    shape = im.shape[:2]  # current shape [height, width]
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]
    dw /= 2  # divide padding into 2 sides
    dh /= 2

    im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
    return im, r, (dw, dh)

# ✨ 坐标从缩放图映射回原图
def scale_coords_back(xyxy, gain, pad):
    x1, y1, x2, y2 = xyxy
    x1 = (x1 - pad[0]) / gain
    y1 = (y1 - pad[1]) / gain
    x2 = (x2 - pad[0]) / gain
    y2 = (y2 - pad[1]) / gain
    return map(int, (x1, y1, x2, y2))

# 📦 设备设置
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 🔍 加载模型
model = attempt_load("yolov5s.pt")
model.to(device).eval()

# 🎥 打开视频
cap = cv2.VideoCapture("action1.mp4")

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    original = frame.copy()
    img, gain, pad = letterbox(frame, new_shape=(640, 640))  # 🚀 预处理
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = torch.from_numpy(img).permute(2, 0, 1).float().unsqueeze(0) / 255.0
    img = img.to(device)

    with torch.no_grad():
        pred = model(img)[0]
        pred = non_max_suppression(pred, conf_thres=0.25, iou_thres=0.45)
    vehicle_count = 0
    person_count = 0
    for det in pred:
        if det is not None and len(det):
            for *xyxy, conf, cls in det:
                # 检测车辆
                if model.names[int(cls)] in ["car", "truck", "bus"]:
                    vehicle_count += 1
                # 检测人
                if model.names[int(cls)] == "person":
                    person_count += 1

                x1, y1, x2, y2 = scale_coords_back(xyxy, gain, pad)  # 坐标还原回原图
                label = f"{model.names[int(cls)]} {conf:.2f}"
                cv2.rectangle(original, (x1, y1), (x2, y2), (0, 255, 0), 1)
                cv2.putText(original, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX,
                            0.9, (0, 255, 0), 2)
    #输出车辆和行人数
    print(f"车辆数：{vehicle_count}，行者数：{person_count}")
    cv2.imshow("YOLOv5 Detection", original)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
