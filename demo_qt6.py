#!/usr/bin/env python3
"""
无人机交通警察系统 - Qt6现代化演示界面
提供美观的图形化界面展示系统功能
"""

import sys
import asyncio
import threading
import time
import random
from pathlib import Path
import cv2
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QGridLayout, QPushButton, QLabel, 
                                QTextEdit, QProgressBar, QFrame, QSplitter,
                                QGroupBox, QScrollArea, QTabWidget, QStatusBar)
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
    from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QLinearGradient
    QT6_AVAILABLE = True
except ImportError:
    QT6_AVAILABLE = False
    print("Qt6未安装，请运行: pip install PyQt6")

from core.system import SystemManager

class SystemWorker(QThread):
    """系统操作工作线程"""
    status_updated = pyqtSignal(str)
    system_info_updated = pyqtSignal(dict)
    detection_result = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.system_manager = None
        self.demo_running = False
        
    def initialize_system(self):
        """初始化系统"""
        try:
            self.status_updated.emit("正在初始化系统...")
            
            async def init():
                self.system_manager = SystemManager()
                success = await self.system_manager.initialize()
                if success:
                    start_success = await self.system_manager.start()
                    return start_success
                return False
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success = loop.run_until_complete(init())
            
            if success:
                self.status_updated.emit("系统初始化成功")
                self.update_system_info()
            else:
                self.status_updated.emit("系统初始化失败")
                
            return success
            
        except Exception as e:
            self.status_updated.emit(f"初始化错误: {e}")
            return False
    
    def stop_system(self):
        """停止系统"""
        try:
            self.demo_running = False
            
            if self.system_manager:
                async def stop():
                    await self.system_manager.shutdown()
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(stop())
                
                self.system_manager = None
            
            self.status_updated.emit("系统已停止")
            
        except Exception as e:
            self.status_updated.emit(f"停止错误: {e}")
    
    def start_demo(self):
        """开始演示"""
        if not self.system_manager:
            self.status_updated.emit("请先初始化系统")
            return
        
        self.demo_running = True
        self.status_updated.emit("演示开始")
        
        # 启动演示循环
        self.start()
    
    def stop_demo(self):
        """停止演示"""
        self.demo_running = False
        self.status_updated.emit("演示已停止")
    
    def update_system_info(self):
        """更新系统信息"""
        if not self.system_manager:
            return
        
        try:
            async def get_info():
                info = await self.system_manager.get_system_info()
                status = await self.system_manager.get_system_status()
                return {**info, **status}
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            info = loop.run_until_complete(get_info())
            
            self.system_info_updated.emit(info)
            
        except Exception as e:
            self.status_updated.emit(f"获取系统信息失败: {e}")
    
    def run(self):
        """演示循环"""
        frame_count = 0
        
        while self.demo_running:
            frame_count += 1
            
            # 模拟检测结果
            detections = self.simulate_detection()
            
            # 发送检测结果
            result = {
                'frame': frame_count,
                'detections': detections,
                'timestamp': time.strftime("%H:%M:%S"),
                'fps': 30
            }
            
            self.detection_result.emit(result)
            
            # 模拟处理时间
            time.sleep(1/30)  # 30 FPS
    
    def simulate_detection(self):
        """模拟检测结果"""
        detections = []
        
        if random.random() > 0.3:  # 70%概率有检测
            num_objects = random.randint(1, 4)
            
            for _ in range(num_objects):
                obj_type = random.choice(['car', 'person', 'bicycle', 'motorcycle'])
                confidence = random.uniform(0.6, 0.95)
                
                detections.append({
                    'class': obj_type,
                    'confidence': confidence,
                    'bbox': [
                        random.randint(50, 500),
                        random.randint(50, 300),
                        random.randint(80, 150),
                        random.randint(60, 120)
                    ]
                })
        
        return detections

class ModernButton(QPushButton):
    """现代化按钮样式"""
    
    def __init__(self, text, color="primary"):
        super().__init__(text)
        self.setFixedHeight(45)
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        
        colors = {
            "primary": "#3498db",
            "success": "#27ae60", 
            "danger": "#e74c3c",
            "warning": "#f39c12",
            "info": "#9b59b6"
        }
        
        base_color = colors.get(color, colors["primary"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {base_color}, stop:1 {self.darken_color(base_color)});
                border: none;
                border-radius: 8px;
                color: white;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(base_color)}, stop:1 {base_color});
            }}
            QPushButton:pressed {{
                background: {self.darken_color(base_color)};
            }}
            QPushButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
    
    def lighten_color(self, color):
        """使颜色变亮"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(min(255, int(c * 1.2)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def darken_color(self, color):
        """使颜色变暗"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(max(0, int(c * 0.8)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

class StatusCard(QFrame):
    """状态卡片组件"""
    
    def __init__(self, title, value="--", icon="📊"):
        super().__init__()
        self.setFrameStyle(QFrame.Shape.Box)
        self.setFixedHeight(100)
        
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin: 5px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 图标和标题
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI Emoji", 16))
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 10))
        title_label.setStyleSheet("color: #6c757d;")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # 数值
        self.value_label = QLabel(value)
        self.value_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        self.value_label.setStyleSheet("color: #2c3e50;")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.value_label)
        layout.addStretch()
        
        self.setLayout(layout)
    
    def update_value(self, value):
        """更新数值"""
        self.value_label.setText(str(value))

class TrafficPoliceQt6Demo(QMainWindow):
    """Qt6现代化演示界面"""
    
    def __init__(self):
        super().__init__()
        self.worker = SystemWorker()
        self.setup_ui()
        self.setup_connections()
        
        # 统计数据
        self.total_frames = 0
        self.total_detections = 0
        self.detection_history = []
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🚁 无人机交通警察系统 - 现代化演示界面")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
            }
            QWidget {
                font-family: "Microsoft YaHei";
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 中央显示区域
        self.create_display_area(splitter)
        
        # 右侧信息面板
        self.create_info_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 700, 400])
        
        main_layout.addWidget(splitter)
        
        # 状态栏
        self.create_status_bar()
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        control_widget.setFixedWidth(300)
        control_widget.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(control_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("🎮 系统控制")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 控制按钮
        self.init_btn = ModernButton("🚀 初始化系统", "success")
        self.stop_btn = ModernButton("⏹️ 停止系统", "danger")
        self.demo_btn = ModernButton("🎭 开始演示", "primary")
        self.pause_btn = ModernButton("⏸️ 停止演示", "warning")
        
        self.stop_btn.setEnabled(False)
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        layout.addWidget(self.init_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(self.demo_btn)
        layout.addWidget(self.pause_btn)
        
        # 系统状态卡片
        layout.addWidget(QLabel("📊 系统状态"))
        
        status_layout = QGridLayout()
        self.status_card = StatusCard("系统状态", "未初始化", "🔧")
        self.fps_card = StatusCard("处理速度", "0 FPS", "⚡")
        self.detection_card = StatusCard("检测总数", "0", "🎯")
        self.uptime_card = StatusCard("运行时间", "0s", "⏱️")
        
        status_layout.addWidget(self.status_card, 0, 0)
        status_layout.addWidget(self.fps_card, 0, 1)
        status_layout.addWidget(self.detection_card, 1, 0)
        status_layout.addWidget(self.uptime_card, 1, 1)
        
        layout.addLayout(status_layout)
        layout.addStretch()
        
        parent.addWidget(control_widget)
    
    def create_display_area(self, parent):
        """创建显示区域"""
        display_widget = QWidget()
        display_widget.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(display_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("📹 实时检测画面")
        title.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setMinimumHeight(400)
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                color: white;
                font-size: 18px;
            }
        """)
        self.video_label.setText("等待演示开始...")
        layout.addWidget(self.video_label)
        
        # 检测信息显示
        detection_info = QLabel("🎯 检测信息")
        detection_info.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        detection_info.setStyleSheet("color: #2c3e50; margin-top: 15px;")
        layout.addWidget(detection_info)
        
        self.detection_display = QTextEdit()
        self.detection_display.setMaximumHeight(150)
        self.detection_display.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-family: "Consolas", monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.detection_display)
        
        parent.addWidget(display_widget)
    
    def create_info_panel(self, parent):
        """创建信息面板"""
        info_widget = QWidget()
        info_widget.setFixedWidth(400)
        info_widget.setStyleSheet("""
            QWidget {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 15px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(info_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background: white;
            }
            QTabBar::tab {
                background: #f8f9fa;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 2px solid #3498db;
            }
        """)
        
        # 系统信息标签页
        self.system_info_text = QTextEdit()
        self.system_info_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Consolas", monospace;
                font-size: 11px;
                background: transparent;
            }
        """)
        tab_widget.addTab(self.system_info_text, "📋 系统信息")
        
        # 日志标签页
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Consolas", monospace;
                font-size: 11px;
                background: transparent;
                color: #27ae60;
            }
        """)
        tab_widget.addTab(self.log_text, "📝 系统日志")
        
        # 统计标签页
        self.stats_text = QTextEdit()
        self.stats_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Consolas", monospace;
                font-size: 11px;
                background: transparent;
            }
        """)
        tab_widget.addTab(self.stats_text, "📊 统计信息")
        
        layout.addWidget(tab_widget)
        
        parent.addWidget(info_widget)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background: rgba(255, 255, 255, 0.9);
                border-top: 1px solid #dee2e6;
                padding: 5px;
            }
        """)
        
        self.status_label = QLabel("系统就绪")
        self.status_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        status_bar.addWidget(self.status_label)
        
        status_bar.addPermanentWidget(QLabel("🚁 无人机交通警察系统 v1.0.0"))
        
        self.setStatusBar(status_bar)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.init_btn.clicked.connect(self.initialize_system)
        self.stop_btn.clicked.connect(self.stop_system)
        self.demo_btn.clicked.connect(self.start_demo)
        self.pause_btn.clicked.connect(self.stop_demo)
        
        # 工作线程信号连接
        self.worker.status_updated.connect(self.update_status)
        self.worker.system_info_updated.connect(self.update_system_info)
        self.worker.detection_result.connect(self.update_detection_result)
    
    def initialize_system(self):
        """初始化系统"""
        self.init_btn.setEnabled(False)
        self.log_message("开始初始化系统...")
        
        # 在新线程中初始化
        threading.Thread(target=self._init_system_thread, daemon=True).start()
    
    def _init_system_thread(self):
        """初始化系统线程"""
        success = self.worker.initialize_system()
        
        if success:
            self.init_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.demo_btn.setEnabled(True)
            self.status_card.update_value("运行中")
        else:
            self.init_btn.setEnabled(True)
            self.status_card.update_value("初始化失败")
    
    def stop_system(self):
        """停止系统"""
        self.worker.stop_system()
        
        self.init_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        self.status_card.update_value("已停止")
        self.fps_card.update_value("0 FPS")
    
    def start_demo(self):
        """开始演示"""
        self.worker.start_demo()
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)
        
        self.video_label.setText("🎬 演示进行中...")
        self.video_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border: 2px solid #27ae60;
                border-radius: 10px;
                color: white;
                font-size: 18px;
            }
        """)
    
    def stop_demo(self):
        """停止演示"""
        self.worker.stop_demo()
        self.demo_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)
        
        self.video_label.setText("演示已停止")
        self.video_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                color: white;
                font-size: 18px;
            }
        """)
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
        self.log_message(message)
    
    def update_system_info(self, info):
        """更新系统信息"""
        info_text = f"""
系统名称: {info.get('name', 'N/A')}
版本号: {info.get('version', 'N/A')}
启动时间: {info.get('start_time', '未启动')}
运行状态: {'✅ 正常' if info.get('running') else '❌ 异常'}
初始化: {'✅ 完成' if info.get('initialized') else '❌ 未完成'}

组件状态:
- AI引擎: {'✅' if info.get('components', {}).get('ai_engine') else '❌'}
- 业务引擎: {'✅' if info.get('components', {}).get('business_engine') else '❌'}
- 硬件管理: {'✅' if info.get('components', {}).get('hardware_manager') else '❌'}
- 数据管理: {'✅' if info.get('components', {}).get('data_manager') else '❌'}
- LLM引擎: {'✅' if info.get('components', {}).get('llm_engine') else '❌'}
        """
        
        self.system_info_text.setText(info_text.strip())
        
        # 更新运行时间
        if info.get('uptime_seconds'):
            uptime_minutes = info['uptime_seconds'] / 60
            self.uptime_card.update_value(f"{uptime_minutes:.1f}m")
    
    def update_detection_result(self, result):
        """更新检测结果"""
        self.total_frames += 1
        self.total_detections += len(result['detections'])
        
        # 更新统计卡片
        self.fps_card.update_value(f"{result['fps']} FPS")
        self.detection_card.update_value(str(self.total_detections))
        
        # 更新检测显示
        timestamp = result['timestamp']
        frame = result['frame']
        detections = result['detections']
        
        if detections:
            detection_text = f"[{timestamp}] 帧 {frame:04d}: 检测到 {len(detections)} 个目标\n"
            for det in detections:
                detection_text += f"  - {det['class']}: {det['confidence']:.2f}\n"
        else:
            detection_text = f"[{timestamp}] 帧 {frame:04d}: 无目标检测\n"
        
        self.detection_display.append(detection_text)
        
        # 保持最新的检测结果在视图中
        cursor = self.detection_display.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.detection_display.setTextCursor(cursor)
        
        # 更新统计信息
        self.update_statistics()
    
    def update_statistics(self):
        """更新统计信息"""
        avg_detections = self.total_detections / max(1, self.total_frames)
        
        stats_text = f"""
总处理帧数: {self.total_frames}
总检测数量: {self.total_detections}
平均每帧检测: {avg_detections:.2f}
检测成功率: {(self.total_detections/max(1,self.total_frames)*100):.1f}%

最近检测统计:
- 车辆: {random.randint(0, 10)}
- 行人: {random.randint(0, 8)}
- 自行车: {random.randint(0, 3)}
- 摩托车: {random.randint(0, 2)}
        """
        
        self.stats_text.setText(stats_text.strip())
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.append(log_entry)
        
        # 保持最新日志在视图中
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

def main():
    """主函数"""
    if not QT6_AVAILABLE:
        print("❌ Qt6未安装")
        print("请运行以下命令安装:")
        print("pip install PyQt6")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用图标和样式
    app.setApplicationName("无人机交通警察系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = TrafficPoliceQt6Demo()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
