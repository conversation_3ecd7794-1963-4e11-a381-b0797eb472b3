#!/usr/bin/env python3
"""
无人机交通警察系统 - Qt6现代化演示界面
提供美观的图形化界面展示系统功能
"""

import sys
import asyncio
import threading
import time
import random
from pathlib import Path
# cv2 和 numpy 在演示界面中不需要
# import cv2
# import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                                QHBoxLayout, QGridLayout, QPushButton, QLabel,
                                QTextEdit, QFrame, QSplitter, QTabWidget, QStatusBar)
    from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
    from PyQt6.QtGui import QFont
    QT6_AVAILABLE = True
except ImportError:
    QT6_AVAILABLE = False
    print("Qt6未安装，请运行: pip install PyQt6")

from core.system import SystemManager

class SystemWorker(QThread):
    """系统操作工作线程"""
    status_updated = pyqtSignal(str)
    system_info_updated = pyqtSignal(dict)
    detection_result = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.system_manager = None
        self.demo_running = False
        
    def initialize_system(self):
        """初始化系统"""
        try:
            self.status_updated.emit("正在初始化系统...")
            
            async def init():
                self.system_manager = SystemManager()
                success = await self.system_manager.initialize()
                if success:
                    start_success = await self.system_manager.start()
                    return start_success
                return False
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success = loop.run_until_complete(init())
            
            if success:
                self.status_updated.emit("系统初始化成功")
                self.update_system_info()
            else:
                self.status_updated.emit("系统初始化失败")
                
            return success
            
        except Exception as e:
            self.status_updated.emit(f"初始化错误: {e}")
            return False
    
    def stop_system(self):
        """停止系统"""
        try:
            self.demo_running = False

            if self.system_manager:
                # 使用线程安全的方式停止系统
                import threading

                def stop_in_thread():
                    try:
                        # 尝试获取现有的事件循环
                        try:
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                # 如果循环正在运行，创建任务
                                future = asyncio.run_coroutine_threadsafe(
                                    self.system_manager.shutdown(), loop
                                )
                                future.result(timeout=10.0)  # 10秒超时
                            else:
                                # 如果循环没有运行，直接运行
                                loop.run_until_complete(self.system_manager.shutdown())
                        except RuntimeError:
                            # 没有事件循环，创建新的
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                loop.run_until_complete(self.system_manager.shutdown())
                            finally:
                                loop.close()

                        self.status_updated.emit("系统已停止")

                    except Exception as e:
                        self.status_updated.emit(f"停止错误: {e}")

                # 在单独线程中执行停止操作
                stop_thread = threading.Thread(target=stop_in_thread, daemon=True)
                stop_thread.start()
                stop_thread.join(timeout=15.0)  # 15秒超时

                self.system_manager = None
            else:
                self.status_updated.emit("系统已停止")

        except Exception as e:
            self.status_updated.emit(f"停止错误: {e}")
    
    def start_demo(self):
        """开始演示"""
        if not self.system_manager:
            self.status_updated.emit("请先初始化系统")
            return
        
        self.demo_running = True
        self.status_updated.emit("演示开始")
        
        # 启动演示循环
        self.start()
    
    def stop_demo(self):
        """停止演示"""
        self.demo_running = False
        self.status_updated.emit("演示已停止")
    
    def update_system_info(self):
        """更新系统信息"""
        if not self.system_manager:
            return

        try:
            async def get_info():
                info = await self.system_manager.get_system_info()
                status = await self.system_manager.get_system_status()
                return {**info, **status}

            # 使用线程安全的方式获取信息
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建任务
                    future = asyncio.run_coroutine_threadsafe(get_info(), loop)
                    info = future.result(timeout=5.0)
                else:
                    # 如果循环没有运行，直接运行
                    info = loop.run_until_complete(get_info())
            except RuntimeError:
                # 没有事件循环，创建新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    info = loop.run_until_complete(get_info())
                finally:
                    loop.close()

            self.system_info_updated.emit(info)

        except Exception as e:
            self.status_updated.emit(f"获取系统信息失败: {e}")
    
    def run(self):
        """演示循环"""
        frame_count = 0
        
        while self.demo_running:
            frame_count += 1
            
            # 模拟检测结果
            detections = self.simulate_detection()
            
            # 发送检测结果
            result = {
                'frame': frame_count,
                'detections': detections,
                'timestamp': time.strftime("%H:%M:%S"),
                'fps': 30
            }
            
            self.detection_result.emit(result)
            
            # 模拟处理时间
            time.sleep(1/30)  # 30 FPS
    
    def simulate_detection(self):
        """模拟检测结果 - 更真实的数据"""
        detections = []

        # 模拟不同时间段的交通状况
        current_time = time.time()
        time_factor = (current_time % 60) / 60  # 0-1的周期

        # 根据时间调整检测概率和数量
        if time_factor < 0.3:  # 低峰期
            detection_prob = 0.4
            max_objects = 2
        elif time_factor < 0.7:  # 正常期
            detection_prob = 0.7
            max_objects = 4
        else:  # 高峰期
            detection_prob = 0.9
            max_objects = 6

        if random.random() < detection_prob:
            num_objects = random.randint(1, max_objects)

            for i in range(num_objects):
                # 更真实的目标类型分布
                obj_types = ['car'] * 6 + ['person'] * 3 + ['bicycle'] * 1 + ['motorcycle'] * 1
                obj_type = random.choice(obj_types)

                # 根据目标类型调整置信度
                if obj_type == 'car':
                    confidence = random.uniform(0.75, 0.95)
                elif obj_type == 'person':
                    confidence = random.uniform(0.65, 0.90)
                else:
                    confidence = random.uniform(0.60, 0.85)

                # 更真实的边界框
                x = random.randint(50, 600)
                y = random.randint(50, 350)

                if obj_type == 'car':
                    w, h = random.randint(80, 150), random.randint(60, 100)
                elif obj_type == 'person':
                    w, h = random.randint(30, 60), random.randint(80, 120)
                else:
                    w, h = random.randint(40, 80), random.randint(50, 90)

                detections.append({
                    'class': obj_type,
                    'confidence': confidence,
                    'bbox': [x, y, w, h],
                    'id': i + 1,
                    'speed': random.uniform(0, 60) if obj_type in ['car', 'motorcycle'] else random.uniform(0, 15)
                })

        return detections

class ModernButton(QPushButton):
    """现代化按钮样式"""
    
    def __init__(self, text, color="primary"):
        super().__init__(text)
        self.setMinimumHeight(50)  # 增加按钮高度
        self.setMinimumWidth(180)  # 设置最小宽度
        self.setFont(QFont("Arial", 12, QFont.Weight.Bold))  # 使用系统字体

        colors = {
            "primary": "#2196F3",
            "success": "#4CAF50",
            "danger": "#F44336",
            "warning": "#FF9800",
            "info": "#9C27B0"
        }

        base_color = colors.get(color, colors["primary"])

        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {base_color}, stop:1 {self.darken_color(base_color)});
                border: 2px solid {self.darken_color(base_color)};
                border-radius: 10px;
                color: white;
                padding: 12px 20px;
                font-weight: bold;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(base_color)}, stop:1 {base_color});
                border: 2px solid {base_color};
            }}
            QPushButton:pressed {{
                background: {self.darken_color(base_color)};
                border: 2px solid {self.darken_color(base_color)};
            }}
            QPushButton:disabled {{
                background: #BDBDBD;
                color: #757575;
                border: 2px solid #9E9E9E;
            }}
        """)
    
    def lighten_color(self, color):
        """使颜色变亮"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(min(255, int(c * 1.2)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def darken_color(self, color):
        """使颜色变暗"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(max(0, int(c * 0.8)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

class StatusCard(QFrame):
    """状态卡片组件"""
    
    def __init__(self, title, value="--", icon="📊"):
        super().__init__()
        self.setFrameStyle(QFrame.Shape.Box)
        self.setMinimumHeight(100)  # 减小高度，使其更紧凑

        # 使用系统主题兼容的样式
        self.setStyleSheet("""
            QFrame {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 12px;
                margin: 5px;
                padding: 8px;
            }
            QFrame:hover {
                border: 2px solid palette(highlight);
                background-color: palette(alternate-base);
            }
        """)
        
        layout = QVBoxLayout()
        
        # 图标和标题
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Arial", 14))  # 减小图标字体
        icon_label.setStyleSheet("color: palette(text);")

        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))  # 减小标题字体
        title_label.setStyleSheet("color: palette(text); margin-left: 6px;")

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # 数值
        self.value_label = QLabel(value)
        self.value_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))  # 减小数值字体
        self.value_label.setStyleSheet("color: palette(text); margin-top: 6px;")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.value_label)
        layout.addStretch()
        
        self.setLayout(layout)
    
    def update_value(self, value):
        """更新数值"""
        self.value_label.setText(str(value))

class TrafficPoliceQt6Demo(QMainWindow):
    """Qt6现代化演示界面"""
    
    def __init__(self):
        super().__init__()
        self.worker = SystemWorker()
        self.setup_ui()
        self.setup_connections()
        
        # 统计数据
        self.total_frames = 0
        self.total_detections = 0
        self.detection_history = []  # 检测历史
        self.detection_history = []
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🚁 无人机交通警察系统 - 现代化演示界面")
        self.setGeometry(100, 100, 1500, 1000)  # 增加窗口大小

        # 使用系统主题兼容的样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: palette(window);
            }
            QWidget {
                font-family: "Arial", "DejaVu Sans", "Liberation Sans", sans-serif;
                color: palette(text);
            }
            QSplitter::handle {
                background-color: palette(mid);
            }
            QSplitter::handle:horizontal {
                width: 3px;
            }
            QSplitter::handle:vertical {
                height: 3px;
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧控制面板
        self.create_control_panel(splitter)
        
        # 中央显示区域
        self.create_display_area(splitter)
        
        # 右侧信息面板
        self.create_info_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 700, 400])
        
        main_layout.addWidget(splitter)
        
        # 状态栏
        self.create_status_bar()
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        control_widget.setFixedWidth(350)  # 增加宽度
        control_widget.setStyleSheet("""
            QWidget {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 15px;
                margin: 10px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout(control_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("🎮 系统控制")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: palette(text); margin-bottom: 15px; padding: 10px;")
        layout.addWidget(title)
        
        # 控制按钮
        self.init_btn = ModernButton("🚀 初始化系统", "success")
        self.stop_btn = ModernButton("⏹️ 停止系统", "danger")
        self.demo_btn = ModernButton("🎭 开始演示", "primary")
        self.pause_btn = ModernButton("⏸️ 停止演示", "warning")
        
        self.stop_btn.setEnabled(False)
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        layout.addWidget(self.init_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(self.demo_btn)
        layout.addWidget(self.pause_btn)
        
        # 系统状态卡片
        status_title = QLabel("📊 系统状态")
        status_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        status_title.setStyleSheet("color: palette(text); margin-top: 20px; margin-bottom: 10px; padding: 5px;")
        layout.addWidget(status_title)
        
        status_layout = QGridLayout()
        self.status_card = StatusCard("系统状态", "未初始化", "🔧")
        self.fps_card = StatusCard("处理速度", "0 FPS", "⚡")
        self.detection_card = StatusCard("检测总数", "0", "🎯")
        self.uptime_card = StatusCard("运行时间", "0s", "⏱️")
        
        status_layout.addWidget(self.status_card, 0, 0)
        status_layout.addWidget(self.fps_card, 0, 1)
        status_layout.addWidget(self.detection_card, 1, 0)
        status_layout.addWidget(self.uptime_card, 1, 1)
        
        layout.addLayout(status_layout)
        layout.addStretch()
        
        parent.addWidget(control_widget)
    
    def create_display_area(self, parent):
        """创建显示区域"""
        display_widget = QWidget()
        display_widget.setStyleSheet("""
            QWidget {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 15px;
                margin: 10px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout(display_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("📹 实时检测画面")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: palette(text); margin-bottom: 15px; padding: 10px;")
        layout.addWidget(title)

        # 视频显示区域
        self.video_label = QLabel()
        self.video_label.setMinimumHeight(450)
        self.video_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: palette(dark);
                border: 3px solid palette(mid);
                border-radius: 12px;
                color: palette(bright-text);
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
            }
        """)
        self.video_label.setText("等待演示开始...")
        layout.addWidget(self.video_label)
        
        # 检测信息显示
        detection_info = QLabel("🎯 检测信息")
        detection_info.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        detection_info.setStyleSheet("color: palette(text); margin-top: 15px; padding: 5px;")
        layout.addWidget(detection_info)

        self.detection_display = QTextEdit()
        self.detection_display.setMaximumHeight(180)
        self.detection_display.setStyleSheet("""
            QTextEdit {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 10px;
                padding: 12px;
                font-family: "Courier New", "DejaVu Sans Mono", monospace;
                font-size: 12px;
                color: palette(text);
            }
            QTextEdit:focus {
                border: 2px solid palette(highlight);
            }
        """)
        layout.addWidget(self.detection_display)
        
        parent.addWidget(display_widget)
    
    def create_info_panel(self, parent):
        """创建信息面板"""
        info_widget = QWidget()
        info_widget.setFixedWidth(450)  # 增加宽度
        info_widget.setStyleSheet("""
            QWidget {
                background-color: palette(base);
                border: 2px solid palette(mid);
                border-radius: 15px;
                margin: 10px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout(info_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid palette(mid);
                border-radius: 10px;
                background-color: palette(base);
                padding: 5px;
            }
            QTabBar::tab {
                background-color: palette(button);
                color: palette(button-text);
                padding: 12px 20px;
                margin-right: 3px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid palette(mid);
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: palette(base);
                color: palette(text);
                border-bottom: 3px solid palette(highlight);
            }
            QTabBar::tab:hover {
                background-color: palette(alternate-base);
            }
        """)
        
        # 系统信息标签页
        self.system_info_text = QTextEdit()
        self.system_info_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Courier New", "DejaVu Sans Mono", monospace;
                font-size: 12px;
                background-color: transparent;
                color: palette(text);
                padding: 10px;
            }
        """)
        tab_widget.addTab(self.system_info_text, "📋 系统信息")

        # 日志标签页
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Courier New", "DejaVu Sans Mono", monospace;
                font-size: 12px;
                background-color: transparent;
                color: palette(text);
                padding: 10px;
            }
        """)
        tab_widget.addTab(self.log_text, "📝 系统日志")

        # 统计标签页
        self.stats_text = QTextEdit()
        self.stats_text.setStyleSheet("""
            QTextEdit {
                border: none;
                font-family: "Courier New", "DejaVu Sans Mono", monospace;
                font-size: 12px;
                background-color: transparent;
                color: palette(text);
                padding: 10px;
            }
        """)
        tab_widget.addTab(self.stats_text, "📊 统计信息")
        
        layout.addWidget(tab_widget)
        
        parent.addWidget(info_widget)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = QStatusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: palette(window);
                border-top: 2px solid palette(mid);
                padding: 8px;
                font-size: 12px;
            }
            QStatusBar::item {
                border: none;
            }
        """)

        self.status_label = QLabel("系统就绪")
        self.status_label.setStyleSheet("color: palette(text); font-weight: bold; padding: 5px;")
        status_bar.addWidget(self.status_label)

        version_label = QLabel("🚁 无人机交通警察系统 v1.0.0")
        version_label.setStyleSheet("color: palette(text); padding: 5px;")
        status_bar.addPermanentWidget(version_label)

        self.setStatusBar(status_bar)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.init_btn.clicked.connect(self.initialize_system)
        self.stop_btn.clicked.connect(self.stop_system)
        self.demo_btn.clicked.connect(self.start_demo)
        self.pause_btn.clicked.connect(self.stop_demo)
        
        # 工作线程信号连接
        self.worker.status_updated.connect(self.update_status)
        self.worker.system_info_updated.connect(self.update_system_info)
        self.worker.detection_result.connect(self.update_detection_result)
    
    def initialize_system(self):
        """初始化系统"""
        self.init_btn.setEnabled(False)
        self.log_message("开始初始化系统...")
        
        # 在新线程中初始化
        threading.Thread(target=self._init_system_thread, daemon=True).start()
    
    def _init_system_thread(self):
        """初始化系统线程"""
        success = self.worker.initialize_system()
        
        if success:
            self.init_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.demo_btn.setEnabled(True)
            self.status_card.update_value("运行中")
        else:
            self.init_btn.setEnabled(True)
            self.status_card.update_value("初始化失败")
    
    def stop_system(self):
        """停止系统"""
        self.worker.stop_system()
        
        self.init_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(False)
        
        self.status_card.update_value("已停止")
        self.fps_card.update_value("0 FPS")
    
    def start_demo(self):
        """开始演示"""
        self.worker.start_demo()
        self.demo_btn.setEnabled(False)
        self.pause_btn.setEnabled(True)

        self.video_label.setText("🎬 演示进行中...")
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #4CAF50;
                border: 3px solid #2E7D32;
                border-radius: 12px;
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
            }
        """)
    
    def stop_demo(self):
        """停止演示"""
        self.worker.stop_demo()
        self.demo_btn.setEnabled(True)
        self.pause_btn.setEnabled(False)

        self.video_label.setText("演示已停止")
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: palette(dark);
                border: 3px solid palette(mid);
                border-radius: 12px;
                color: palette(bright-text);
                font-size: 20px;
                font-weight: bold;
                padding: 20px;
            }
        """)
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
        self.log_message(message)
    
    def update_system_info(self, info):
        """更新系统信息"""
        info_text = f"""
系统名称: {info.get('name', 'N/A')}
版本号: {info.get('version', 'N/A')}
启动时间: {info.get('start_time', '未启动')}
运行状态: {'✅ 正常' if info.get('running') else '❌ 异常'}
初始化: {'✅ 完成' if info.get('initialized') else '❌ 未完成'}

组件状态:
- AI引擎: {'✅' if info.get('components', {}).get('ai_engine') else '❌'}
- 业务引擎: {'✅' if info.get('components', {}).get('business_engine') else '❌'}
- 硬件管理: {'✅' if info.get('components', {}).get('hardware_manager') else '❌'}
- 数据管理: {'✅' if info.get('components', {}).get('data_manager') else '❌'}
- LLM引擎: {'✅' if info.get('components', {}).get('llm_engine') else '❌'}
        """
        
        self.system_info_text.setText(info_text.strip())
        
        # 更新运行时间
        if info.get('uptime_seconds'):
            uptime_minutes = info['uptime_seconds'] / 60
            self.uptime_card.update_value(f"{uptime_minutes:.1f}m")
    
    def update_detection_result(self, result):
        """更新检测结果"""
        self.total_frames += 1
        detections = result['detections']
        self.total_detections += len(detections)

        # 更新统计卡片
        self.fps_card.update_value(f"{result['fps']} FPS")
        self.detection_card.update_value(str(self.total_detections))

        # 更新检测显示
        timestamp = result['timestamp']
        frame = result['frame']

        if detections:
            detection_text = f"🎯 [{timestamp}] 帧 {frame:04d}: 检测到 {len(detections)} 个目标\n"

            # 按类型分组显示
            by_type = {}
            for det in detections:
                obj_type = det['class']
                if obj_type not in by_type:
                    by_type[obj_type] = []
                by_type[obj_type].append(det)

            for obj_type, objects in by_type.items():
                type_emoji = {'car': '🚗', 'person': '🚶', 'bicycle': '🚲', 'motorcycle': '🏍️'}.get(obj_type, '📦')
                detection_text += f"  {type_emoji} {obj_type}: {len(objects)}个\n"

                for i, det in enumerate(objects[:3]):  # 只显示前3个
                    confidence = det['confidence']
                    bbox = det['bbox']
                    speed = det.get('speed', 0)

                    detection_text += f"    #{det.get('id', i+1)}: 置信度{confidence:.2f}"
                    if speed > 0:
                        detection_text += f", 速度{speed:.1f}km/h"
                    detection_text += f", 位置({bbox[0]},{bbox[1]})\n"

                if len(objects) > 3:
                    detection_text += f"    ... 还有{len(objects)-3}个{obj_type}\n"

            # 添加分析信息
            total_vehicles = sum(len(objects) for obj_type, objects in by_type.items()
                               if obj_type in ['car', 'motorcycle'])
            total_people = len(by_type.get('person', []))

            if total_vehicles > 5:
                detection_text += "  ⚠️ 交通密度较高\n"
            if total_people > 3:
                detection_text += "  👥 行人较多，注意安全\n"

        else:
            detection_text = f"⭕ [{timestamp}] 帧 {frame:04d}: 无目标检测\n"

        detection_text += "─" * 40 + "\n"
        self.detection_display.append(detection_text)

        # 保持最新的检测结果在视图中
        cursor = self.detection_display.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.detection_display.setTextCursor(cursor)

        # 更新统计信息
        self.update_statistics(detections)
    
    def update_statistics(self, current_detections=None):
        """更新统计信息"""
        # 更新检测历史
        if current_detections:
            self.detection_history.append(current_detections)
            # 只保留最近100帧的历史
            if len(self.detection_history) > 100:
                self.detection_history.pop(0)

        avg_detections = self.total_detections / max(1, self.total_frames)

        # 统计最近的检测类型
        recent_stats = {'car': 0, 'person': 0, 'bicycle': 0, 'motorcycle': 0}
        for frame_detections in self.detection_history[-20:]:  # 最近20帧
            for det in frame_detections:
                obj_type = det['class']
                if obj_type in recent_stats:
                    recent_stats[obj_type] += 1

        # 计算检测率
        frames_with_detection = sum(1 for frame_dets in self.detection_history if len(frame_dets) > 0)
        detection_rate = (frames_with_detection / max(1, len(self.detection_history))) * 100

        # 计算平均速度
        speeds = []
        for frame_detections in self.detection_history[-10:]:  # 最近10帧
            for det in frame_detections:
                if 'speed' in det and det['speed'] > 0:
                    speeds.append(det['speed'])
        avg_speed = sum(speeds) / len(speeds) if speeds else 0

        stats_text = f"""📊 实时统计数据

🎬 处理统计:
  总处理帧数: {self.total_frames:,}
  总检测数量: {self.total_detections:,}
  平均每帧检测: {avg_detections:.2f}个
  检测成功率: {detection_rate:.1f}%

🎯 最近检测 (20帧内):
  🚗 车辆: {recent_stats['car']}个
  🚶 行人: {recent_stats['person']}个
  🚲 自行车: {recent_stats['bicycle']}个
  🏍️ 摩托车: {recent_stats['motorcycle']}个

⚡ 性能指标:
  平均速度: {avg_speed:.1f} km/h
  处理延迟: < 33ms
  内存使用: 正常

🚨 安全评估:
  交通密度: {'高' if avg_detections > 3 else '中' if avg_detections > 1 else '低'}
  风险等级: {'⚠️ 注意' if recent_stats['person'] > 5 else '✅ 正常'}
        """

        self.stats_text.setText(stats_text.strip())
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.append(log_entry)
        
        # 保持最新日志在视图中
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

def main():
    """主函数"""
    if not QT6_AVAILABLE:
        print("❌ Qt6未安装")
        print("请运行以下命令安装:")
        print("pip install PyQt6")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用图标和样式
    app.setApplicationName("无人机交通警察系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = TrafficPoliceQt6Demo()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
