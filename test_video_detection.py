#!/usr/bin/env python3
"""
视频检测功能测试脚本
测试action1.mp4的检测功能
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_video_file():
    """测试视频文件是否可以正常读取"""
    print("🔍 测试视频文件...")
    
    video_path = Path("data/videos/action1.mp4")
    
    if not video_path.exists():
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    try:
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            print(f"❌ 无法打开视频文件: {video_path}")
            return False
        
        # 获取视频信息
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"✅ 视频文件信息:")
        print(f"   文件路径: {video_path}")
        print(f"   分辨率: {width}x{height}")
        print(f"   总帧数: {total_frames}")
        print(f"   帧率: {fps:.2f} FPS")
        print(f"   时长: {duration:.2f} 秒")
        
        # 读取第一帧测试
        ret, frame = cap.read()
        if ret:
            print(f"✅ 成功读取第一帧，大小: {frame.shape}")
        else:
            print("❌ 无法读取视频帧")
            cap.release()
            return False
        
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ 视频测试失败: {e}")
        return False

def test_yolo_models():
    """测试YOLO模型可用性"""
    print("\n🔍 测试YOLO模型...")
    
    # 检查模型文件
    model_paths = [
        "yolov8s.pt",
        "yolov8n.pt", 
        "yolov5s.pt",
        "models/yolov8s.pt",
        "models/yolov8n.pt"
    ]
    
    available_models = []
    for model_path in model_paths:
        if Path(model_path).exists():
            available_models.append(model_path)
            print(f"✅ 找到模型文件: {model_path}")
    
    if not available_models:
        print("⚠️ 未找到本地YOLO模型文件")
        print("💡 将尝试在线下载模型...")
    
    # 测试ultralytics
    try:
        from ultralytics import YOLO
        print("✅ ultralytics库可用")
        
        if available_models:
            try:
                model = YOLO(available_models[0])
                print(f"✅ 成功加载模型: {available_models[0]}")
                return True
            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
        
    except ImportError:
        print("⚠️ ultralytics库未安装")
    
    # 测试torch hub
    try:
        import torch
        print("✅ PyTorch可用")
        
        try:
            model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            print("✅ 成功通过torch hub加载YOLOv5")
            return True
        except Exception as e:
            print(f"⚠️ torch hub加载失败: {e}")
    
    except ImportError:
        print("❌ PyTorch未安装")
    
    return False

def test_qt6_availability():
    """测试Qt6可用性"""
    print("\n🔍 测试Qt6界面...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        print("✅ PyQt6可用")
        return True
    except ImportError:
        print("❌ PyQt6未安装")
        print("💡 安装命令: pip install PyQt6")
        return False

def test_opencv_functionality():
    """测试OpenCV功能"""
    print("\n🔍 测试OpenCV功能...")
    
    try:
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        # 测试基本图像操作
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        print("✅ 颜色空间转换正常")
        
        # 测试轮廓检测
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print("✅ 轮廓检测功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV测试失败: {e}")
        return False

def create_demo_video():
    """创建一个简单的演示视频（如果action1.mp4不存在）"""
    print("\n🎬 创建演示视频...")
    
    video_path = Path("data/videos/action1.mp4")
    
    if video_path.exists():
        print("✅ action1.mp4已存在，无需创建")
        return True
    
    try:
        # 确保目录存在
        video_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(video_path), fourcc, 30.0, (640, 480))
        
        print("正在创建演示视频...")
        
        # 生成100帧的简单动画
        for i in range(100):
            # 创建黑色背景
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 添加移动的矩形（模拟车辆）
            x = int(50 + i * 5)
            y = 200
            cv2.rectangle(frame, (x, y), (x + 80, y + 40), (0, 255, 0), -1)
            cv2.putText(frame, "Car", (x + 10, y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 添加移动的圆形（模拟行人）
            x2 = int(100 + i * 3)
            y2 = 350
            cv2.circle(frame, (x2, y2), 20, (255, 0, 0), -1)
            cv2.putText(frame, "Person", (x2 - 25, y2 + 35), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 添加帧号
            cv2.putText(frame, f"Frame: {i+1}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 演示视频创建成功: {video_path}")
        return True
        
    except Exception as e:
        print(f"❌ 演示视频创建失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📋 视频检测演示使用指南")
    print("=" * 50)
    
    print("🚀 启动方式:")
    print("   python demo_video_detection.py")
    
    print("\n🎮 操作说明:")
    print("   1. 点击'🎬 开始检测'开始处理视频")
    print("   2. 使用'⏸️ 暂停'和'▶️ 继续'控制播放")
    print("   3. 点击'⏹️ 停止'结束检测")
    
    print("\n📊 功能特色:")
    print("   ✅ 真实视频文件处理")
    print("   ✅ YOLO目标检测（如果可用）")
    print("   ✅ 运动检测备选方案")
    print("   ✅ 实时检测结果显示")
    print("   ✅ 检测框可视化")
    print("   ✅ 统计信息展示")
    
    print("\n🔧 依赖要求:")
    print("   - PyQt6: pip install PyQt6")
    print("   - ultralytics: pip install ultralytics (可选)")
    print("   - OpenCV: 已安装")
    print("   - PyTorch: 已安装")

def main():
    """主函数"""
    print("🎬 视频检测功能测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        ("视频文件", test_video_file),
        ("OpenCV功能", test_opencv_functionality),
        ("Qt6界面", test_qt6_availability),
        ("YOLO模型", test_yolo_models),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 如果视频文件不存在，尝试创建
    if not results.get("视频文件", False):
        results["创建演示视频"] = create_demo_video()
        if results["创建演示视频"]:
            results["视频文件"] = test_video_file()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 计算通过率
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n📈 总体通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if results.get("视频文件", False) and results.get("Qt6界面", False):
        print("\n🎉 基本功能测试通过！可以启动视频检测演示")
        show_usage_guide()
    else:
        print("\n⚠️ 部分功能不可用，请检查依赖安装")
        
        if not results.get("视频文件", False):
            print("   - 需要action1.mp4视频文件")
        if not results.get("Qt6界面", False):
            print("   - 需要安装PyQt6: pip install PyQt6")

if __name__ == "__main__":
    main()
