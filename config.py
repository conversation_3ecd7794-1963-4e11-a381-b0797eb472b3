"""
交通监控系统配置文件
统一管理所有配置参数，避免硬编码
"""
import os
from dataclasses import dataclass
from typing import Tuple, List


@dataclass
class ModelConfig:
    """模型相关配置"""
    model_path: str = "yolov5s.pt"
    input_size: Tuple[int, int] = (640, 640)
    conf_threshold: float = 0.25
    iou_threshold: float = 0.45
    device: str = "auto"  # "auto", "cpu", "cuda"


@dataclass
class VideoConfig:
    """视频处理配置"""
    video_path: str = "action1.mp4"
    output_fps: int = 30
    frame_skip: int = 1  # 跳帧处理，1表示不跳帧
    max_frames: int = -1  # -1表示处理全部帧


@dataclass
class DetectionConfig:
    """检测相关配置"""
    vehicle_classes: List[str] = None
    person_classes: List[str] = None
    
    def __post_init__(self):
        if self.vehicle_classes is None:
            self.vehicle_classes = ["car", "truck", "bus", "motorcycle"]
        if self.person_classes is None:
            self.person_classes = ["person"]


@dataclass
class DensityConfig:
    """密度计算配置"""
    vehicle_physical_area: float = 8.64  # 车辆物理面积 (m²)
    vehicle_width_m: float = 1.8  # 车辆物理宽度 (m)
    assumed_height_m: float = 20.0  # 假设监控区域高度 (m)
    calibration_frames: int = 10  # 用于校准的帧数
    default_area_m2: float = 1000.0  # 默认监控区域面积 (m²)


@dataclass
class AccidentConfig:
    """事故检测配置"""
    iou_threshold: float = 0.5  # IoU阈值
    speed_threshold: float = 5.0  # 速度阈值 (像素/帧)
    stationary_frames: int = 30  # 静止帧数阈值
    collision_frames: int = 5  # 碰撞持续帧数阈值


@dataclass
class VisualizationConfig:
    """可视化配置"""
    window_size: Tuple[int, int] = (400, 300)
    max_plot_points: int = 100
    bbox_color: Tuple[int, int, int] = (0, 255, 0)  # BGR格式
    accident_color: Tuple[int, int, int] = (0, 0, 255)  # BGR格式
    text_color: Tuple[int, int, int] = (0, 255, 255)  # BGR格式
    font_scale: float = 0.7
    font_thickness: int = 2
    display_scale: float = 0.7  # 显示窗口缩放比例


@dataclass
class SystemConfig:
    """系统配置"""
    max_memory_frames: int = 100  # 最大内存中保存的帧数
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR
    save_results: bool = False  # 是否保存结果
    output_dir: str = "output"  # 输出目录


class Config:
    """主配置类，整合所有配置"""
    
    def __init__(self, config_file: str = None):
        self.model = ModelConfig()
        self.video = VideoConfig()
        self.detection = DetectionConfig()
        self.density = DensityConfig()
        self.accident = AccidentConfig()
        self.visualization = VisualizationConfig()
        self.system = SystemConfig()
        
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
    
    def load_from_file(self, config_file: str):
        """从配置文件加载配置（预留接口）"""
        # TODO: 实现从JSON/YAML文件加载配置
        pass
    
    def save_to_file(self, config_file: str):
        """保存配置到文件（预留接口）"""
        # TODO: 实现保存配置到JSON/YAML文件
        pass
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        # 检查模型文件是否存在
        if not os.path.exists(self.model.model_path):
            print(f"警告: 模型文件不存在: {self.model.model_path}")
            return False
        
        # 检查视频文件是否存在
        if not os.path.exists(self.video.video_path):
            print(f"警告: 视频文件不存在: {self.video.video_path}")
            return False
        
        # 检查阈值范围
        if not (0.0 <= self.model.conf_threshold <= 1.0):
            print(f"警告: 置信度阈值超出范围: {self.model.conf_threshold}")
            return False
        
        if not (0.0 <= self.model.iou_threshold <= 1.0):
            print(f"警告: IoU阈值超出范围: {self.model.iou_threshold}")
            return False
        
        return True


# 创建默认配置实例
default_config = Config()
